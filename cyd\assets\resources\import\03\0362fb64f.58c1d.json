[{"__type__": "cc.SpriteFrame", "content": {"name": "jts_05", "texture": "beuoGf02FKUZLAUt6CUPNS", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_04", "texture": "8cWjLIvMVBuocJOWk7cZnG", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_01", "texture": "bccqC9UV9PCIZC7AV47w2A", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_02", "texture": "eaVdnsCgpLcrG8ZRFjlo4K", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_08", "texture": "5eXw54MO1BJ4iF7P+E6R9J", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_09", "texture": "88CZDFGGVJ5Z4/I3G2NtIq", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_06", "texture": "70Y0gSwR1E8Z8FCdmHocrm", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_00", "texture": "c2mM1xF89DtZA+Q6sfotqo", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_03", "texture": "f7UMXXEudCSLcC5copME6c", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "jts_07", "texture": "3coBuZaj9HJIf05qAPJ5Cu", "rect": [0, 0, 347, 296], "offset": [0, 0], "originalSize": [347, 296], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "jts_an", "_duration": 0.36666666666666664, "sample": 30, "curveData": {"comps": {"cc.Sprite": {"spriteFrame": [{"frame": 0, "value": {"__uuid__": "a48N/26WpP4bKOEcYWQ1tN"}}, {"frame": 0.03333333333333333, "value": {"__uuid__": "4drKGe0AFNYYiWgvKN8f7R"}}, {"frame": 0.06666666666666667, "value": {"__uuid__": "74aKspL6pPmK9bBouZ1qWx"}}, {"frame": 0.1, "value": {"__uuid__": "afQ+UffltLj7nYdMxjOlqx"}}, {"frame": 0.13333333333333333, "value": {"__uuid__": "20wgTX8pNB7b/p2MhoZq9Z"}}, {"frame": 0.16666666666666666, "value": {"__uuid__": "182MfXodFDa54+L5vKq86P"}}, {"frame": 0.2, "value": {"__uuid__": "84VHQcdvJA1qAzqUZYV5FH"}}, {"frame": 0.23333333333333334, "value": {"__uuid__": "cemfGsZKVLSLk7rC2qL0Q9"}}, {"frame": 0.26666666666666666, "value": {"__uuid__": "7862T/A/NJzIB9StIRRtpR"}}, {"frame": 0.3, "value": {"__uuid__": "79fnWEDZZD9Ky7hP0enN+Y"}}, {"frame": 0.3333333333333333, "value": null}]}}}}]