[{"__type__": "cc.SpriteFrame", "content": {"name": "p05", "texture": "dfwirbwH9AdLbkxFqCeqEp", "rect": [0, 0, 48, 42], "offset": [0, 0], "originalSize": [48, 42], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p11", "texture": "b2ha2Aod1B6okH/GY4MKD5", "rect": [0, 0, 50, 50], "offset": [0, 0], "originalSize": [50, 50], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "map7001", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map7001", "_children": [{"__id__": 2}, {"__id__": 68}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__type__": "cc.Node", "_name": "touchBg", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "25I4w9yz9Oho5ID+lI+oby"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 4}, {"__id__": 12}, {"__id__": 13}, {"__id__": 37}, {"__id__": 50}, {"__id__": 56}, {"__id__": 60}, {"__id__": 66}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 40, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "进门就过关了！", "_N$string": "进门就过关了！", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "57B2JM3x9Oo5FKi9rhQ8Ea"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-314.69625, 50.16875, 0, 0, 0, 0, 1, 1.25, 1.25, 1.25]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 2}, "_children": [{"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "c3sBZGsLROsJPS6X0TMxhJ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -21.456, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p08", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98GQf0M21BcrlVX+MPPswB"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "0efFJ5jtZPlZ6HtFqPxaZc"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 189.743, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p10", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f7u4QwdydI6rMNQi3KxJL6"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "584AIYxT1KBIYVbdKzy+pY"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-107.47, 186.385, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p10", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f7u4QwdydI6rMNQi3KxJL6"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "1dlDGVO9JIdpZIxzMC/XjF"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [104.603, 184.333, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p06", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "87aCrOeNVAT4BYMqeFjmVb"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "a1cX7V1y5BupcUsRq3amlD"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-67.47, 190.495, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "p06", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "87aCrOeNVAT4BYMqeFjmVb"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "2bctXpIhpHc5vJ3VWSGO/L"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [70.016, 188.443, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "p09", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19zOnvHhNGRaHfnNoQx1ms"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "49opxgulFJxJPXjd7p+PbK"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-47.47, 190.495, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}}, {"__type__": "cc.Node", "_name": "p09", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19zOnvHhNGRaHfnNoQx1ms"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "64S1TB/4hHcLHNDw/CB1iO"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [44.603, 188.443, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "p08", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98GQf0M21BcrlVX+MPPswB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "dbzdCINlREyblfkXGMvty7"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 40, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "groud02", "_parent": {"__id__": 2}, "_children": [{"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 13}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 13}, "_friction": 0, "_offset": {"__type__": "cc.Vec2", "y": -150}, "_size": {"__type__": "cc.Size", "width": 250, "height": 300}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "6a1qUZSGpE8qLwfqVFNkRA"}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 94.509, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 14}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "69XPCWdSJDjoUW5Kx9cx70"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "973uofVK9MrZueUmrJNeOz"}}, {"__type__": "cc.Widget", "node": {"__id__": 15}, "_enabled": false, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "8e9YRMOoJJU5CvDvbbbKTt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 13}, "_children": [{"__id__": 17}, {"__id__": 18}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 16}, "_layoutSize": {"__type__": "cc.Size", "width": 128, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "ce1UEnAZFPE6a5h3pQKeNr"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1dN2oGJFBMxIurB/lVVZNj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "00yfnbZkpA4LaJeWjT7YFQ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1dN2oGJFBMxIurB/lVVZNj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "21aXTo7plLrIzasm07MYi1"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78KV9K+EFA6LfTaL+RPdG4"}}, {"__type__": "cc.Widget", "node": {"__id__": 19}, "_enabled": false, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "778GHK2vVBWLUJF8UX4tja"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d5jDdOTsdKC5opDtnGstUK"}}, {"__type__": "cc.Widget", "node": {"__id__": 20}, "_enabled": false, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "f4nzMihJxOt7ajdIqI8viA"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d5jDdOTsdKC5opDtnGstUK"}}, {"__type__": "cc.Widget", "node": {"__id__": 21}, "_enabled": false, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "a6njAqasZOe7ETdwp2v30m"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "54YWmKVqxLpKLK5ZOCqd+H"}}, {"__type__": "cc.Widget", "node": {"__id__": 22}, "_enabled": false, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "f0EwSFFJJAA681XwHBNZ3t"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "54YWmKVqxLpKLK5ZOCqd+H"}}, {"__type__": "cc.Widget", "node": {"__id__": 23}, "_enabled": false, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "8e6Di1kvhLwaWLTnkv+wmm"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d5jDdOTsdKC5opDtnGstUK"}}, {"__type__": "cc.Widget", "node": {"__id__": 24}, "_enabled": false, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "e4cUpmP7dKfKJcgEUxbuN9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d5jDdOTsdKC5opDtnGstUK"}}, {"__type__": "cc.Widget", "node": {"__id__": 25}, "_enabled": false, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "20Zsasx0pOGLeguaGa7l91"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "54YWmKVqxLpKLK5ZOCqd+H"}}, {"__type__": "cc.Widget", "node": {"__id__": 26}, "_enabled": false, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "ae7uB1tzVIkpugkUHR+K86"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "54YWmKVqxLpKLK5ZOCqd+H"}}, {"__type__": "cc.Widget", "node": {"__id__": 27}, "_enabled": false, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "532IwodGZPTLcZMt7z85nN"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 13}, "_children": [{"__id__": 29}, {"__id__": 30}, {"__id__": 31}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "b02xlb7dZNC7gOBIa0bL5Q"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [12.258, 13.621, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p18", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "260r8mQ9tE56dAfSS2cB5M"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "ccM3HUrOpBxKffYJG53Hd+"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [20.955, -130.232, 0, 0, 0, 0.7071067811865476, -0.7071067811865475, 1, -1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 270}}, {"__type__": "cc.Node", "_name": "p15", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9fOiiI/MpK8KADygrxFUhx"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "a7lsxulFRAKZUKDuPUHlfi"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-76.046, -67.589, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p15", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9fOiiI/MpK8KADygrxFUhx"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "2a3Qk62pZIMpwaMg84f1Y+"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-74.304, -229.175, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p24", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a7jzqAPrNNPoZJdYnnivZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "64jY8WfkJBlrNvCQVhPJE+"}, "_contentSize": {"__type__": "cc.Size", "width": 92, "height": 44}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [55.09375, -137.922, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1.25, 1.25, 1.25]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}}, {"__type__": "cc.Node", "_name": "p18", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "260r8mQ9tE56dAfSS2cB5M"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "b8pSavfb5PnZoP96zL+CmQ"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-43.563, -184.33, 0, 0, 0, 0, 1, 1, -1, 1]}}, {"__type__": "cc.Node", "_name": "p22", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a35g70VR5HL4BeltjAeq5W"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "6dA1EYxLtGwojJa0Zl6g4x"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10.043, -188.205, 0, 0, 0, 0, 1, 1.25, 1.25, 1.25]}}, {"__type__": "cc.Node", "_name": "p16", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "518GOC6EtC47UHU6MH5pCj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "11FLajNs9PmICWMtnkiPO3"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12.868, -113, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p18", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "260r8mQ9tE56dAfSS2cB5M"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "behzSuCM9EI4J++/ENxaYr"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-57.825, -113.112, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "groud", "_parent": {"__id__": 2}, "_children": [{"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 42}, {"__id__": 43}, {"__id__": 44}, {"__id__": 45}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 37}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 37}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 1664, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "b53EMg/MVHuru07uDawWb/"}, "_contentSize": {"__type__": "cc.Size", "width": 1664, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -172, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 37}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 38}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "45h5GU2TdEf45Dat5TdFr5"}, "_contentSize": {"__type__": "cc.Size", "width": 1536, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 37}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "973uofVK9MrZueUmrJNeOz"}}, {"__type__": "cc.Widget", "node": {"__id__": 39}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "cdclLVNM5Ak6lmTW9GlYym"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-800, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 37}, "_children": [{"__id__": 41}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 40}, "_layoutSize": {"__type__": "cc.Size", "width": 1536, "height": 64}, "_resize": 2, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "ddX8kx4MJAnZEm610nLbuF"}, "_contentSize": {"__type__": "cc.Size", "width": 1536, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 40}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1dN2oGJFBMxIurB/lVVZNj"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "e0Ppkc4/9Fn7RrcpeK/86L"}, "_contentSize": {"__type__": "cc.Size", "width": 1536, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 37}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78KV9K+EFA6LfTaL+RPdG4"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "f5R3F5VMhDWok8botAHj0o"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [800, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 37}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d5jDdOTsdKC5opDtnGstUK"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "73cPLJjIJA/J8dIVt3tjs1"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [800, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 37}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "54YWmKVqxLpKLK5ZOCqd+H"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "f0F4q7vnZBBYtd6kPFTBsj"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-800, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform", "_parent": {"__id__": 37}, "_children": [{"__id__": 46}, {"__id__": 47}, {"__id__": 49}], "_components": [{"__type__": "cc.Widget", "node": {"__id__": 45}, "_alignFlags": 41, "_left": -2, "_right": -2, "_top": 10}, {"__type__": "cc.Layout", "node": {"__id__": 45}, "_layoutSize": {"__type__": "cc.Size", "width": 1668}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "6eBZdKe1lMSrPxwxxZ7e6r"}, "_contentSize": {"__type__": "cc.Size", "width": 1668}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p13", "_parent": {"__id__": 45}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "29Gbnt7pVLn6z5tsxTLGi9"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "91qAyn+OtJR4JJHlmzfqOX"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-801, 0, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 45}, "_children": [{"__id__": 48}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 47}, "_layoutSize": {"__type__": "cc.Size", "width": 1536, "height": 24}}, {"__type__": "cc.Widget", "node": {"__id__": 47}, "_alignFlags": 40, "_left": 66, "_right": 66, "_originalWidth": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "4184t+iEJGRpSr5qC1G6Wr"}, "_contentSize": {"__type__": "cc.Size", "width": 1536, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p12", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c7sW5G/IZL+LuXhO3t5u3k"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 48}, "_alignFlags": 40, "_originalWidth": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "a5mjOzf7ZCTZZvTrMvOhFj"}, "_contentSize": {"__type__": "cc.Size", "width": 1536, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p13", "_parent": {"__id__": 45}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "29Gbnt7pVLn6z5tsxTLGi9"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "d6vHB1P8FLG6eYBpvqW+9r"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [801, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tempPortal", "_parent": {"__id__": 2}, "_children": [{"__id__": 51}, {"__id__": 52}, {"__id__": 53}, {"__type__": "cc.Node", "_name": "box", "_parent": {"__id__": 50}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "71xOjUuOFNkJ5Bp4bvxda2"}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 10}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 50}, "_type": 0, "_allowSleep": false, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 50}, "tag": 111, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": 25}, "_size": {"__type__": "cc.Size", "width": 50, "height": 20}}, {"__id__": 54}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "7ah7ci5xZFi7RMnX2mshIa"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-125.544, -108.444, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1.25, 1.25, 1.25]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "4480svq6RIcrSCT+HSXaiD"}}, {"__type__": "cc.Animation", "node": {"__id__": 51}, "_defaultClip": {"__uuid__": "caaoqFKxBPdbLVVrUdESZq"}, "_clips": [{"__uuid__": "caaoqFKxBPdbLVVrUdESZq"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "65Uyf/KlxD04Ru7+FiNJ29"}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "bbvjGG+4pGion30Gx3qUTO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "d5/EEMsuxA+73zCVj8i13H"}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "nodeMask", "_parent": {"__id__": 50}, "_children": [{"__type__": "cc.Node", "_name": "roleParent", "_parent": {"__id__": 53}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "d4xUCIDE9Pxa1RnsfLc9vQ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 168, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Mask", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0coziJQU5KV4CW8wPgfDkf"}, "_type": 2, "_N$alphaThreshold": 0.5, "_N$inverted": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "554m/cnONPDbwaFvaPtS+H"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 108}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -2, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "fd713DodihHMKamj5QiOuN1", "node": {"__id__": 50}, "targetNode": {"__id__": 55}, "portalDir": 2}, {"__type__": "fd713DodihHMKamj5QiOuN1", "node": {"__id__": 56}, "targetNode": {"__id__": 54}, "portalDir": 3}, {"__type__": "cc.Node", "_name": "tempPortal", "_parent": {"__id__": 2}, "_children": [{"__id__": 57}, {"__id__": 58}, {"__id__": 59}, {"__type__": "cc.Node", "_name": "box", "_parent": {"__id__": 56}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "f07hOpSyxBf6+q3oL53Lua"}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 10}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 56}, "_type": 0, "_allowSleep": false, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 56}, "tag": 111, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": -25}, "_size": {"__type__": "cc.Size", "width": 50, "height": 20}}, {"__id__": 55}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "2feKGB3gJOhKfIhpmshskH"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [125.895, -108.444, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1.25, 1.25, 1.25]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 56}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "df4rM9boRCiaP2apCmuDOV"}}, {"__type__": "cc.Animation", "node": {"__id__": 57}, "_defaultClip": {"__uuid__": "419V/T2EdEqLp0xsHZtRqd"}, "_clips": [{"__uuid__": "419V/T2EdEqLp0xsHZtRqd"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "0chJ+3KWZJhaGOnNyHz2s1"}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 1, 6.123233995736766e-17, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 56}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "bbvjGG+4pGion30Gx3qUTO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "c0/Zwfzb5PhaqhYQGtLfor"}, "_contentSize": {"__type__": "cc.Size", "width": 132, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "nodeMask", "_parent": {"__id__": 56}, "_children": [{"__type__": "cc.Node", "_name": "roleParent", "_parent": {"__id__": 59}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "a0JKM4vNtPsKcU/v0vKx/i"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 168, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Mask", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0coziJQU5KV4CW8wPgfDkf"}, "_type": 2, "_N$alphaThreshold": 0.5, "_N$inverted": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "8dvgvCvBdPz6yEn/My8/bT"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 108}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -2, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "door", "_parent": {"__id__": 2}, "_children": [{"__id__": 61}, {"__id__": 62}, {"__id__": 63}, {"__id__": 64}, {"__id__": 65}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 60}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 60}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 62}, "_size": {"__type__": "cc.Size", "width": 100, "height": 124}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 60}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "2ek2iZC85BQLRwJo3xLMvf"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [469.833, -176.772, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "p08", "_parent": {"__id__": 60}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98GQf0M21BcrlVX+MPPswB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "141FmvpnRBpotWzaITgxKm"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 170, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p05", "_parent": {"__id__": 60}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "36ExoFJx9G07tnja4YRJDC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "63xFcvEpZOnpyzIHBtD3q6"}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 42}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 170, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "p11", "_parent": {"__id__": 60}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 63}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "92s37Gq8FLNKcAgnPsNI4j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "caWqjb6UdAZZWX8ZLM0lPk"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 170, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 60}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 64}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 64}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "dcDIgzK2dPfrAbnqdzQSNk"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 60}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 65}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "44Z+kJxWxJbJKMOmeU0eGX"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 147, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 67}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 66}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 66}, "_density": 5, "_friction": 40, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "410aefldt9L4bo6mV8a/cH6", "node": {"__id__": 66}, "aSpeed": 500, "maxASpeed": 200}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "a1o4qqUcFPebw6D0ZkOwJg"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-537.908, -102.818, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 66}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "cby2dQrt9LF4keTHrnNLaC"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "tempBullet", "_parent": {"__id__": 1}, "_children": [{"__type__": "cc.Node", "_name": "head", "_parent": {"__id__": 68}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "a3ZSt317VOw6Q3xP/oWSfD"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 13, "groupIndex": 13}, {"__id__": 69}, {"__id__": 70}], "_active": false, "_components": [{"__type__": "26af2rG2mdBAbJblMjmGBqS", "node": {"__id__": 68}}, {"__type__": "cc.RigidBody", "node": {"__id__": 68}, "_gravityScale": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 68}, "_offset": {"__type__": "cc.Vec2", "x": 20}, "_size": {"__type__": "cc.Size", "width": 40, "height": 1}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "748wm7tNtDVIAF4TMbU8oK"}, "_contentSize": {"__type__": "cc.Size", "width": 41.6, "height": 10}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-858.887, -171.777, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 13, "groupIndex": 13}, {"__type__": "cc.Node", "_name": "yellow", "_parent": {"__id__": 68}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98YTGEnc1BiYfZBUEOJkZJ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "9aI05fDrlHlYcu4kdn+7wF"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 13, "groupIndex": 13}, {"__type__": "cc.Node", "_name": "blue", "_parent": {"__id__": 68}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 70}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ffBN9KKidLDo9HpwCrwj8v"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfsFQw8GhLHpykUc7/iZNz"}, "fileId": "2fmT1FpxJDApFJbqZvF5In"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 26}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 13, "groupIndex": 13}]]