var Base64Values = require("./misc").BASE64_VALUES;
var HexChars = "0123456789abcdef".split("");
var _t = ["", "", "", ""];
var UuidTemplate = _t.concat(_t, "-", _t, "-", _t, "-", _t, "-", _t, _t, _t);
var Indices = UuidTemplate.map((function(x, i) {
    return "-" === x ? NaN : i;
})).filter(isFinite);
module.exports = function(base64) {
    // if (22 !== base64.length) return base64;
    UuidTemplate[0] = base64[0];
    UuidTemplate[1] = base64[1];
    for (var i = 2, j = 2; i < base64.length; i += 2) {
        var lhs = Base64Values[base64.charCodeAt(i)];
        var rhs = Base64Values[base64.charCodeAt(i + 1)];
        UuidTemplate[Indices[j++]] = HexChars[lhs >> 2];
        UuidTemplate[Indices[j++]] = HexChars[(3 & lhs) << 2 | rhs >> 4];
        UuidTemplate[Indices[j++]] = HexChars[15 & rhs];
    }
    return UuidTemplate.join("");
};