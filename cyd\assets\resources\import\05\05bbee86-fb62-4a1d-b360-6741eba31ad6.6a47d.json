[{"__type__": "cc.Prefab", "_name": "vStageBounceChooseDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__type__": "cc.Node", "_name": "CC_nodePage", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "bdy5p3uhNBDbc/cMSGvE6V"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 2}, {"__id__": 4}, {"__id__": 7}, {"__type__": "cc.Node", "_name": "CC_nodeTitleBar", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "a5YktMMK9AGbppjy3ijNJ+"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 251, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 10}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 2}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "91vGCYIVRIdbf5p1LYRuaa"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [380, 200, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b09", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48+6YuBptMr4rHwXDDBRsQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "2e/fmgpLVOPa3a9IsB0UHm"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnLeft", "_parent": {"__id__": 1}, "_children": [{"__id__": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "fbF3ouEJRDE6MoL6uzElaz"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-455, -2, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonLeft", "_parent": {"__id__": 4}, "_children": [{"__id__": 6}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 5}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "7f4W+qvfxGjoWJ0YutoKw8"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b07", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6fQlDtgX1Db5C3y1esGtZ8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "66Fgn/rh9CUYgh3ky3kRwW"}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 88}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnRight", "_parent": {"__id__": 1}, "_children": [{"__id__": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "718AgC7yBOY4jwilCT0kax"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [455, -2, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRight", "_parent": {"__id__": 7}, "_children": [{"__id__": 9}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 8}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "0evAuwPC1EaJ312W1j4LOx"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b07", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6fQlDtgX1Db5C3y1esGtZ8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "01jA1daGZJkYeKNE+vI6EW"}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 88}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTouchMask", "_parent": {"__id__": 1}, "_active": false, "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 10}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "05u+6G+2JKHbNgZ0HroxrW"}, "fileId": "97nHfJCe9HH50UyEe3rNyY"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}]