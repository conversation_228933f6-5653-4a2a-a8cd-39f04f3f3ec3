[{"__type__": "cc.SpriteFrame", "content": {"name": "btn_refresh", "texture": "1f500dd69", "rect": [543, 489, 82, 91], "offset": [0, 0], "originalSize": [82, 91], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "btn_skip", "texture": "1f500dd69", "rect": [543, 586, 82, 91], "offset": [0, 0], "originalSize": [82, 91], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vGameCutLineScene", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vGameCutLineScene", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__type__": "cc.Node", "_name": "CC_nodeGame", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "09Bqmb9ihGC7juqLtsyzKl"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 6}, {"__id__": 7}, {"__id__": 15}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "adaEjUgi9CqawZTWlnr47x"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 219.97045256124744, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeCamera", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Camera", "node": {"__id__": 2}, "_cullingMask": -290, "_depth": 2, "_nearClip": 0.1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "5dt8CnTWFOxKphnY7O+vMe"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 511.2526844653697, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "cameraUi", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Camera", "node": {"__id__": 3}, "_cullingMask": 32, "_clearFlags": 7, "_depth": 1, "_nearClip": 0.1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "48m1aiA4NDv7DG0LFjeqIf"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 511.2526844653697, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "cameraDefault", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Camera", "node": {"__id__": 4}, "_cullingMask": 1, "_depth": 3, "_nearClip": 0.1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "77GgeXAExN8Zr2OjTSrmLA"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 511.2526844653697, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBg", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "59gDElX9FNabX9T6dAnu/C"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 5, "groupIndex": 5}, {"__type__": "cc.Node", "_name": "CC_node4399", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "95ZSKE5WpJ5bPaba6PB2qT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "1cq9LD9tZDCbrTFQeUSEiH"}, "_contentSize": {"__type__": "cc.Size", "width": 165, "height": 37}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [400, 250, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_LLT_node3", "_parent": {"__id__": 1}, "_children": [{"__id__": 8}, {"__id__": 11}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "8btBYfMiJCs4oD7kkn52af"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-512, 288, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLv", "_parent": {"__id__": 7}, "_children": [{"__id__": 9}, {"__id__": 10}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "72Lu0RfQtEfKji9mhhDbxP"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [80, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a07", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "30wQMzA3FPzamVq/nOHf8D"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "1cCmnPE6VNrp5JCaldB5wH"}, "_contentSize": {"__type__": "cc.Size", "width": 137, "height": 180}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelLv", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_string": "第 1 关", "_N$string": "第 1 关", "_fontSize": 26, "_lineHeight": 26, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "82F6ZAIk9AxKjfKAtl7qUC"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 80.91, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 7}, "_children": [{"__id__": 12}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 11}, "_layoutSize": {"__type__": "cc.Size", "width": 77, "height": 70}, "_resize": 1, "_N$layoutType": 1, "_N$spacingX": 13}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "6buiGd4AhE64dNWvjrr1xY"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnStop", "_parent": {"__id__": 11}, "_children": [{"__id__": 13}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "db0zkjG/5J8KrPrpJXJcvR"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [38.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonStop", "_parent": {"__id__": 12}, "_children": [{"__id__": 14}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 13}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "81COWNyxBD05sNQdvSkcWW"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a03", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "abW1hjPyZF2rWBqEVtTr4/"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "ecguYNeGFKtYwwskkS/s8b"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_LL_node1", "_parent": {"__id__": 1}, "_children": [{"__id__": 16}, {"__id__": 19}, {"__id__": 23}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "02NTJytRRPuJWbliGhY531"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-430, -20, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRefresh", "_parent": {"__id__": 15}, "_children": [{"__id__": 17}, {"__id__": 18}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "51aWJZ0fhFVZ0sHs/H2QxM"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "51Idzey/5FIaioMTNG9Phm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "e6nCgyFEVCR5IMxmp8/pzP"}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "刷新", "_N$string": "刷新", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}, {"__type__": "cc.LabelOutline", "node": {"__id__": 18}, "_color": {"__type__": "cc.Color"}, "_width": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "7aboyOX+pA4q6LcFdS8gYk"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 39.28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -34, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonTips", "_parent": {"__id__": 15}, "_children": [{"__id__": 20}, {"__id__": 21}, {"__id__": 22}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "622iGTq85IXbN5UOobSdGA"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5GIJYecNJ+Yl8AnaDmnmN"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "e3ZpEHzI9MRo+W3b7MQPQ/"}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "提示", "_N$string": "提示", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}, {"__type__": "cc.LabelOutline", "node": {"__id__": 21}, "_color": {"__type__": "cc.Color"}, "_width": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "76zlXEtH1A2L2fbktOmGIH"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 39.28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -34, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteAdIconTip2", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c2EBVSVIJIV4l2PxZy2VNP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "9dYidHXuVGF4LCdWhvpxja"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [40, 45, 0, 0, 0, 0, 1, 0.9, 0.9, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonSkip", "_parent": {"__id__": 15}, "_children": [{"__id__": 24}, {"__id__": 25}, {"__id__": 26}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "9ekOMW+NNEdKrU8JKaDxx7"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 130}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -120, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5b1ve2pn1BiauK+MjXuOD+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "5bvnTR3QRAiqzQZeGL3nFx"}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "跳关", "_N$string": "跳关", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}, {"__type__": "cc.LabelOutline", "node": {"__id__": 25}, "_color": {"__type__": "cc.Color"}, "_width": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "53qL9eF2FLjLjjDq87NiYV"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 39.28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -34, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteAdIconTip3", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c2EBVSVIJIV4l2PxZy2VNP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "67RDEUhT1BbYn/onGzs0L0"}, "fileId": "dfUST0tqxGO4HgCZjRgK4l"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [40, 45, 0, 0, 0, 0, 1, 0.9, 0.9, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "btn_tips", "texture": "1f500dd69", "rect": [631, 489, 82, 91], "offset": [0, 0], "originalSize": [82, 91], "rotated": 1, "capInsets": [0, 0, 0, 0]}}]