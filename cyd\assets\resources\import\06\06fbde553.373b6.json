[{"__type__": "cc.SpriteFrame", "content": {"name": "p10", "texture": "c3gO+zdU9Il5RWr2B+s34s", "rect": [0, 0, 74, 82], "offset": [0, 0], "originalSize": [74, 82], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground04", "texture": "84B0zlZ3dFla/e6znHt0ln", "rect": [0, 0, 64, 28], "offset": [0, 0], "originalSize": [64, 28], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p09", "texture": "f8k1ZR4kxP/rhPi9H1onjJ", "rect": [0, 0, 74, 82], "offset": [0, 0], "originalSize": [74, 82], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground07", "texture": "67re58soZCAbbQZ7Cmmcg0", "rect": [16, 10, 130, 132], "offset": [1, 4], "originalSize": [160, 160], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground02", "texture": "a2LivMmWxGIa0bcUHlPPlk", "rect": [0, 0, 64, 28], "offset": [0, 0], "originalSize": [64, 28], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p06", "texture": "eeyFdj4fdASaLVM1Ah1yvz", "rect": [0, 0, 112, 110], "offset": [0, 0], "originalSize": [112, 110], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p08", "texture": "e4pGZrD15NpKFmYbJX/oo8", "rect": [0, 0, 68, 74], "offset": [0, 0], "originalSize": [68, 74], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground11", "texture": "4cmQJagPxBvqZ3DStJrVtP", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground03", "texture": "b16mEGwkdCgZwYoB2q99VZ", "rect": [0, 0, 64, 28], "offset": [0, 0], "originalSize": [64, 28], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "doorMoveAnim", "_duration": 5, "wrapMode": 2, "curveData": {"paths": {"p02": {"props": {"angle": [{"frame": 0, "value": 0}, {"frame": 5, "value": 360}]}}, "p01": {"props": {"angle": [{"frame": 0, "value": 0}, {"frame": 5, "value": 720}]}}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground01", "texture": "70TTcaX8ZGKrk6AUr8out6", "rect": [0, 0, 64, 28], "offset": [0, 0], "originalSize": [64, 28], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground08", "texture": "27o4HWvmRNFJ1oV30zZYqU", "rect": [9, 16, 140, 135], "offset": [-1, -3.5], "originalSize": [160, 160], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "doorAnim", "_duration": 0.2, "wrapMode": 2, "curveData": {"paths": {"p09": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.05, "value": 255}, {"frame": 0.06666666666666667, "value": 0}, {"frame": 0.18333333333333332, "value": 0}, {"frame": 0.2, "value": 255}]}}, "p11": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.11666666666666667, "value": 0}, {"frame": 0.13333333333333333, "value": 255}, {"frame": 0.18333333333333332, "value": 255}, {"frame": 0.2, "value": 0}]}}, "p10": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.05, "value": 0}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.11666666666666667, "value": 255}, {"frame": 0.13333333333333333, "value": 0}]}}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p05", "texture": "63yhSYbBNPvY1UKTe4GlLQ", "rect": [0, 0, 112, 110], "offset": [0, 0], "originalSize": [112, 110], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "bg01", "texture": "811KQ14VpA8rv6DN71nElf", "rect": [0, 0, 1600, 960], "offset": [0, 0], "originalSize": [1600, 960], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p04", "texture": "197osxb9pEnqikltNlwS0/", "rect": [0, 0, 112, 110], "offset": [0, 0], "originalSize": [112, 110], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground05", "texture": "02DvRNscBC37ubzNFRH4cG", "rect": [0, 0, 32, 28], "offset": [0, 0], "originalSize": [32, 28], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p11", "texture": "81DKF3cTZChZeF+caQLASS", "rect": [0, 0, 74, 82], "offset": [0, 0], "originalSize": [74, 82], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "ground06", "texture": "f3uw0amNNGKZuQ/kDvAiHl", "rect": [26, 25, 107, 117], "offset": [-0.5, -3.5], "originalSize": [160, 160], "capInsets": [0, 0, 0, 0]}}]