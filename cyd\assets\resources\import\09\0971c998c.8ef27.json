[{"__type__": "cc.SpriteFrame", "content": {"name": "a03", "texture": "11823d524", "rect": [3, 3, 47, 1], "offset": [0, 0], "originalSize": [47, 1], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vGameMainShopPartType", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vGameMainShopPartType", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "49Ob8Wv4JPcLNVn1EgqgdI"}, "fileId": "937mzh1LJPp4Yc7gO04Oxc"}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 65}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "49Ob8Wv4JPcLNVn1EgqgdI"}, "fileId": "6eSR5L+8dBFYxD+a33ktq6"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonBg", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "49Ob8Wv4JPcLNVn1EgqgdI"}, "fileId": "440U+mK+pL7LeUU4nmd5jL"}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 65}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeSelected", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "cavjaphAVC4YXYu4Z5Gj9E"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "49Ob8Wv4JPcLNVn1EgqgdI"}, "fileId": "d3QCzuEotFUqX4cqRqSK3V"}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 61}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteType", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "bfz9eMFnBJ25WZmp96D3e2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "49Ob8Wv4JPcLNVn1EgqgdI"}, "fileId": "63L2UAsCtHp41V/5uJ3S8z"}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 61}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteFocus", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "85T18ESYtHZ7N20/QorxPl"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "49Ob8Wv4JPcLNVn1EgqgdI"}, "fileId": "3aUTPNGLpBMbWRUZjeHwSq"}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 61}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a03", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "42ukfNRitK0qQq/bqBC6Iy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "49Ob8Wv4JPcLNVn1EgqgdI"}, "fileId": "20ZCBKOYpBI5v9HWyUYXZC"}, "_contentSize": {"__type__": "cc.Size", "width": 47, "height": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -34.57, 0, 0, 0, 0, 1, 1.11111, 1.11111, 1.11111]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "hat_select", "texture": "11823d524", "rect": [653, 3, 56, 61], "offset": [0, 0], "originalSize": [56, 61], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "hat", "texture": "11823d524", "rect": [787, 3, 56, 61], "offset": [0, 0], "originalSize": [56, 61], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a02", "texture": "11823d524", "rect": [854, 3, 56, 61], "offset": [0, 0], "originalSize": [56, 61], "rotated": 1, "capInsets": [0, 0, 0, 0]}}]