[[{"__type__": "cc.Prefab", "_name": "vGameBlackWhiteGuideDialog1", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "8cm6yTau9DLa6EnJ+kQ+7R"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6258cheCFBr6vuxDp+1HBF"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "0eMb3pyZFMeb5yamAYU8/F"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "捡起眼镜，解锁能力", "_N$string": "捡起眼镜，解锁能力", "_fontSize": 18, "_lineHeight": 20, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$overflow": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "1fx1wf/vlMCKlobiBBwdtg"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 194.7, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-275, -87.616, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "不戴眼镜时，处于原来的世界，点击按钮进入深层世界。", "_N$string": "不戴眼镜时，处于原来的世界，点击按钮进入深层世界。", "_fontSize": 18, "_lineHeight": 20, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$overflow": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "48zBpumeNMroT8r9C4vgVy"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 237, "height": 45.199999999999996}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-19, -97.616, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "带上眼镜后进入深层世界，取下眼镜后回到原来的世界", "_N$string": "带上眼镜后进入深层世界，取下眼镜后回到原来的世界", "_fontSize": 18, "_lineHeight": 20, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$overflow": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "04cik6R6RHZonBrZtZIm81"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 233, "height": 45.199999999999996}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [262, -97.616, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a001", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2c10kyJnlI04krSlh61bCd"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "627qdBk8JF/7FXPSrePIqQ"}, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 179.699, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 2}, "_children": [{"__id__": 9}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 8}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "2ccLAoZalC+o/rls+d2CgC"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -180.1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "e09", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3cZOflBIRNx6vca4Kr5dry"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "53CiT8lINNCJloQjEhS1cS"}, "fileId": "4egljx97pDYpHh1uG3nKsT"}, "_contentSize": {"__type__": "cc.Size", "width": 211, "height": 86}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "gg01", "texture": "a0fTfdql5BUq5Fd3iOLR97", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}]