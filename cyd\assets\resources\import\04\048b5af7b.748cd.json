[{"__type__": "cc.SpriteFrame", "content": {"name": "glow", "texture": "ffOOYkX1FMl49eCoy4c2V2", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "152_yanhua_02", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "152_yanhua_02", "_children": [{"__id__": 2}, {"__id__": 3}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 1}, "_clips": [{"__uuid__": "b8aii1ZPpCU6TIYGp5Bn0W"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "61HwpUBqZIk7vZq3htzb2F"}, "fileId": "25vzwWyIlNEIysjvLmwCfJ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "eff_yanhua_002", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "68uCmq+uRN3qUMiPYG00ai"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "61HwpUBqZIk7vZq3htzb2F"}, "fileId": "23RPnKVR9BUYbAMOA3xuvv"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 128}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "glow", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_dstBlendFactor": 1, "_spriteFrame": {"__uuid__": "48jKCz3RNBw755Ez8bsIOo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "61HwpUBqZIk7vZq3htzb2F"}, "fileId": "fcKhN2ePNJ+btwFeFyOluI"}, "_color": {"__type__": "cc.Color", "r": 255, "g": 138, "b": 138}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "eff_yanhua_003", "texture": "ab4PZz3fhDR6sT42fjN6D/", "rect": [0, 0, 128, 128], "offset": [0, 0], "originalSize": [128, 128], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "152_yanhua_02", "_duration": 0.36666666666666664, "sample": 30, "curveData": {"paths": {"glow": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.06666666666666667, "value": 183}, {"frame": 0.16666666666666666, "value": 183}, {"frame": 0.3, "value": 0}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2"}}, {"frame": 0.2, "value": {"__type__": "cc.Vec2", "x": 7.5, "y": 7.5}}, {"frame": 0.3, "value": {"__type__": "cc.Vec2", "x": 8, "y": 8}}]}}, "eff_yanhua_002": {"props": {"scale": [{"frame": 0.03333333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 0.1, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.2, "value": {"__type__": "cc.Vec2", "x": 2, "y": 2}}, {"frame": 0.36666666666666664, "value": {"__type__": "cc.Vec2", "x": 2.5, "y": 2.5}}], "opacity": [{"frame": 0.03333333333333333, "value": 0}, {"frame": 0.1, "value": 255}, {"frame": 0.2, "value": 255}, {"frame": 0.36666666666666664, "value": 0}]}}}}}]