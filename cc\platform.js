module.exports = function(require, module, exports) {
    var tempCIDGenerater = new(require("./id-generater"))("TmpCId.");

    function _getPropertyDescriptor(obj, name) {
        while (obj) {
            var pd = Object.getOwnPropertyDescriptor(obj, name);
            if (pd) return pd;
            obj = Object.getPrototypeOf(obj);
        }
        return null;
    }

    function _copyprop(name, source, target) {
        var pd = _getPropertyDescriptor(source, name);
        Object.defineProperty(target, name, pd);
    }
    var js = {
        isNumber: function(obj) {
            return "number" === typeof obj || obj instanceof Number;
        },
        isString: function(obj) {
            return "string" === typeof obj || obj instanceof String;
        },
        addon: function(obj) {
            "use strict";
            obj = obj || {};
            for (var i = 1, length = arguments.length; i < length; i++) {
                var source = arguments[i];
                if (source) {
                    if ("object" !== typeof source) {
                        cc.errorID(5402, source);
                        continue;
                    }
                    for (var name in source) name in obj || _copyprop(name, source, obj);
                }
            }
            return obj;
        },
        mixin: function(obj) {
            "use strict";
            obj = obj || {};
            for (var i = 1, length = arguments.length; i < length; i++) {
                var source = arguments[i];
                if (source) {
                    if ("object" !== typeof source) {
                        cc.errorID(5403, source);
                        continue;
                    }
                    for (var name in source) _copyprop(name, source, obj);
                }
            }
            return obj;
        },
        extend: function(cls, base) {
            false;
            for (var p in base) base.hasOwnProperty(p) && (cls[p] = base[p]);
            cls.prototype = Object.create(base.prototype, {
                constructor: {
                    value: cls,
                    writable: true,
                    configurable: true
                }
            });
            return cls;
        },
        getSuper: function(ctor) {
            var proto = ctor.prototype;
            var dunderProto = proto && Object.getPrototypeOf(proto);
            return dunderProto && dunderProto.constructor;
        },
        isChildClassOf: function(subclass, superclass) {
            if (subclass && superclass) {
                if ("function" !== typeof subclass) return false;
                if ("function" !== typeof superclass) {
                    false;
                    return false;
                }
                if (subclass === superclass) return true;
                for (;;) {
                    subclass = js.getSuper(subclass);
                    if (!subclass) return false;
                    if (subclass === superclass) return true;
                }
            }
            return false;
        },
        clear: function(obj) {
            var keys = Object.keys(obj);
            for (var i = 0; i < keys.length; i++) delete obj[keys[i]];
        },
        isEmptyObject: function(obj) {
            for (var key in obj) return false;
            return true;
        },
        getPropertyDescriptor: _getPropertyDescriptor
    };
    var tmpValueDesc = {
        value: void 0,
        enumerable: false,
        writable: false,
        configurable: true
    };
    js.value = function(obj, prop, value, writable, enumerable) {
        tmpValueDesc.value = value;
        tmpValueDesc.writable = writable;
        tmpValueDesc.enumerable = enumerable;
        Object.defineProperty(obj, prop, tmpValueDesc);
        tmpValueDesc.value = void 0;
    };
    var tmpGetSetDesc = {
        get: null,
        set: null,
        enumerable: false
    };
    js.getset = function(obj, prop, getter, setter, enumerable) {
        if ("function" !== typeof setter) {
            enumerable = setter;
            setter = void 0;
        }
        tmpGetSetDesc.get = getter;
        tmpGetSetDesc.set = setter;
        tmpGetSetDesc.enumerable = enumerable;
        Object.defineProperty(obj, prop, tmpGetSetDesc);
        tmpGetSetDesc.get = null;
        tmpGetSetDesc.set = null;
    };
    var tmpGetDesc = {
        get: null,
        enumerable: false,
        configurable: false
    };
    js.get = function(obj, prop, getter, enumerable, configurable) {
        tmpGetDesc.get = getter;
        tmpGetDesc.enumerable = enumerable;
        tmpGetDesc.configurable = configurable;
        Object.defineProperty(obj, prop, tmpGetDesc);
        tmpGetDesc.get = null;
    };
    var tmpSetDesc = {
        set: null,
        enumerable: false,
        configurable: false
    };
    js.set = function(obj, prop, setter, enumerable, configurable) {
        tmpSetDesc.set = setter;
        tmpSetDesc.enumerable = enumerable;
        tmpSetDesc.configurable = configurable;
        Object.defineProperty(obj, prop, tmpSetDesc);
        tmpSetDesc.set = null;
    };
    js.getClassName = function(objOrCtor) {
        if ("function" === typeof objOrCtor) {
            var prototype = objOrCtor.prototype;
            if (prototype && prototype.hasOwnProperty("__classname__") && prototype.__classname__) return prototype.__classname__;
            var retval = "";
            objOrCtor.name && (retval = objOrCtor.name);
            if (objOrCtor.toString) {
                var arr, str = objOrCtor.toString();
                arr = "[" === str.charAt(0) ? str.match(/\[\w+\s*(\w+)\]/) : str.match(/function\s*(\w+)/);
                arr && 2 === arr.length && (retval = arr[1]);
            }
            return "Object" !== retval ? retval : "";
        }
        if (objOrCtor && objOrCtor.constructor) return js.getClassName(objOrCtor.constructor);
        return "";
    };

    function isTempClassId(id) {
        return "string" !== typeof id || id.startsWith(tempCIDGenerater.prefix);
    }
    (function() {
        var _idToClass = {};
        var _nameToClass = {};

        function setup(key, publicName, table) {
            js.getset(js, publicName, (function() {
                return Object.assign({}, table);
            }), (function(value) {
                js.clear(table);
                Object.assign(table, value);
            }));
            return function(id, constructor) {
                constructor.prototype.hasOwnProperty(key) && delete table[constructor.prototype[key]];
                js.value(constructor.prototype, key, id);
                if (id) {
                    var registered = table[id];
                    if (registered && registered !== constructor) {
                        var error = "A Class already exists with the same " + key + ' : "' + id + '".';
                        false;
                        cc.error(error);
                    } else table[id] = constructor;
                }
            };
        }
        js._setClassId = setup("__cid__", "_registeredClassIds", _idToClass);
        var doSetClassName = setup("__classname__", "_registeredClassNames", _nameToClass);
        js.setClassName = function(className, constructor) {
            doSetClassName(className, constructor);
            if (!constructor.prototype.hasOwnProperty("__cid__")) {
                var id = className || tempCIDGenerater.getNewId();
                id && js._setClassId(id, constructor);
            }
        };
        js.unregisterClass = function() {
            for (var i = 0; i < arguments.length; i++) {
                var p = arguments[i].prototype;
                var classId = p.__cid__;
                classId && delete _idToClass[classId];
                var classname = p.__classname__;
                classname && delete _nameToClass[classname];
            }
        };
        js._getClassById = function(classId) {
            return _idToClass[classId];
        };
        js.getClassByName = function(classname) {
            return _nameToClass[classname];
        };
        js._getClassId = function(obj, allowTempId) {
            allowTempId = "undefined" === typeof allowTempId || allowTempId;
            var res;
            if ("function" === typeof obj && obj.prototype.hasOwnProperty("__cid__")) {
                res = obj.prototype.__cid__;
                if (!allowTempId && (false, false) && isTempClassId(res)) return "";
                return res;
            }
            if (obj && obj.constructor) {
                var prototype = obj.constructor.prototype;
                if (prototype && prototype.hasOwnProperty("__cid__")) {
                    res = obj.__cid__;
                    if (!allowTempId && (false, false) && isTempClassId(res)) return "";
                    return res;
                }
            }
            return "";
        };
    })();
    js.obsolete = function(obj, obsoleted, newExpr, writable) {
        var extractPropName = /([^.]+)$/;
        var oldProp = extractPropName.exec(obsoleted)[0];
        var newProp = extractPropName.exec(newExpr)[0];

        function get() {
            false;
            return this[newProp];
        }
        writable ? js.getset(obj, oldProp, get, (function(value) {
            false;
            this[newProp] = value;
        })) : js.get(obj, oldProp, get);
    };
    js.obsoletes = function(obj, objName, props, writable) {
        for (var obsoleted in props) {
            var newName = props[obsoleted];
            js.obsolete(obj, objName + "." + obsoleted, newName, writable);
        }
    };
    var REGEXP_NUM_OR_STR = /(%d)|(%s)/;
    var REGEXP_STR = /%s/;
    js.formatStr = function() {
        var argLen = arguments.length;
        if (0 === argLen) return "";
        var msg = arguments[0];
        if (1 === argLen) return "" + msg;
        var hasSubstitution = "string" === typeof msg && REGEXP_NUM_OR_STR.test(msg);
        if (hasSubstitution)
            for (var i = 1; i < argLen; ++i) {
                var arg = arguments[i];
                var regExpToTest = "number" === typeof arg ? REGEXP_NUM_OR_STR : REGEXP_STR;
                regExpToTest.test(msg) ? msg = msg.replace(regExpToTest, arg) : msg += " " + arg;
            } else
                for (var _i = 1; _i < argLen; ++_i) msg += " " + arguments[_i];
        return msg;
    };
    js.shiftArguments = function() {
        var len = arguments.length - 1;
        var args = new Array(len);
        for (var i = 0; i < len; ++i) args[i] = arguments[i + 1];
        return args;
    };
    js.createMap = function(forceDictMode) {
        var map = Object.create(null);
        if (forceDictMode) {
            var INVALID_IDENTIFIER_1 = ".";
            var INVALID_IDENTIFIER_2 = "/";
            map[INVALID_IDENTIFIER_1] = true;
            map[INVALID_IDENTIFIER_2] = true;
            delete map[INVALID_IDENTIFIER_1];
            delete map[INVALID_IDENTIFIER_2];
        }
        return map;
    };

    function removeAt(array, index) {
        array.splice(index, 1);
    }

    function fastRemoveAt(array, index) {
        var length = array.length;
        if (index < 0 || index >= length) return;
        array[index] = array[length - 1];
        array.length = length - 1;
    }

    function remove(array, value) {
        var index = array.indexOf(value);
        if (index >= 0) {
            removeAt(array, index);
            return true;
        }
        return false;
    }

    function fastRemove(array, value) {
        var index = array.indexOf(value);
        if (index >= 0) {
            array[index] = array[array.length - 1];
            --array.length;
        }
    }

    function verifyType(array, type) {
        if (array && array.length > 0)
            for (var i = 0; i < array.length; i++)
                if (!(array[i] instanceof type)) {
                    cc.logID(1300);
                    return false;
                }
        return true;
    }

    function removeArray(array, minusArr) {
        for (var i = 0, l = minusArr.length; i < l; i++) remove(array, minusArr[i]);
    }

    function appendObjectsAt(array, addObjs, index) {
        array.splice.apply(array, [index, 0].concat(addObjs));
        return array;
    }
    var indexOf = Array.prototype.indexOf;

    function contains(array, value) {
        return array.indexOf(value) >= 0;
    }

    function copy(array) {
        var i, len = array.length,
            arr_clone = new Array(len);
        for (i = 0; i < len; i += 1) arr_clone[i] = array[i];
        return arr_clone;
    }
    js.array = {
        remove: remove,
        fastRemove: fastRemove,
        removeAt: removeAt,
        fastRemoveAt: fastRemoveAt,
        contains: contains,
        verifyType: verifyType,
        removeArray: removeArray,
        appendObjectsAt: appendObjectsAt,
        copy: copy,
        indexOf: indexOf,
        MutableForwardIterator: require("./mutable-forward-iterator")
    };

    function Pool(cleanupFunc, size) {
        if (void 0 === size) {
            size = cleanupFunc;
            cleanupFunc = null;
        }
        this.get = null;
        this.count = 0;
        this._pool = new Array(size);
        this._cleanup = cleanupFunc;
    }
    Pool.prototype._get = function() {
        if (this.count > 0) {
            --this.count;
            var cache = this._pool[this.count];
            this._pool[this.count] = null;
            return cache;
        }
        return null;
    };
    Pool.prototype.put = function(obj) {
        var pool = this._pool;
        if (this.count < pool.length) {
            if (this._cleanup && false === this._cleanup(obj)) return;
            pool[this.count] = obj;
            ++this.count;
        }
    };
    Pool.prototype.resize = function(length) {
        if (length >= 0) {
            this._pool.length = length;
            this.count > length && (this.count = length);
        }
    };
    js.Pool = Pool;
    cc.js = js;
    module.exports = js;
}