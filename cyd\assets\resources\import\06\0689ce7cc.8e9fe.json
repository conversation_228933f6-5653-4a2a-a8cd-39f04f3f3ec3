[{"__type__": "cc.SpriteFrame", "content": {"name": "p71", "texture": "32VoRTjBhNk7f3ECA4TI/6", "rect": [0, 0, 68, 154], "offset": [0, 0], "originalSize": [68, 154], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p70", "texture": "ecPaG6RE1MhKquTp38FX7m", "rect": [0, 0, 68, 154], "offset": [0, 0], "originalSize": [68, 154], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p67", "texture": "36CySb3CVNJYuvcDnPByIy", "rect": [0, 0, 68, 154], "offset": [0, 0], "originalSize": [68, 154], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p72", "texture": "bbD7BZMalDzrwmcLJkBwIJ", "rect": [0, 0, 68, 154], "offset": [0, 0], "originalSize": [68, 154], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p68", "texture": "d9zeF4xUlO/7FlxO0JFQnH", "rect": [0, 0, 68, 154], "offset": [0, 0], "originalSize": [68, 154], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p69", "texture": "1dzuF4m2FCF6uDHl4yVdGF", "rect": [0, 0, 68, 154], "offset": [0, 0], "originalSize": [68, 154], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "portalEff", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "portalEff", "_children": [{"__id__": 2}, {"__id__": 6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "71gOpQfnNEzK45N84Twsed"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-976.84, 46.624, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "yellow", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "3e5m8vkN5L1a+lO1BnFpZt"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3, 33.87, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}}, {"__type__": "cc.Node", "_name": "p69", "_parent": {"__id__": 2}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d5RjBqSURKUrW43KxzpFuk"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "2eAqeSet9HGKmKkxKah/Uy"}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 154}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}}, {"__type__": "cc.Node", "_name": "p68", "_parent": {"__id__": 2}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c9of/2LKZMzIHZ6L9uux1+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "b7uUZhN8hLTruG3SP0EIl+"}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 154}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}}, {"__type__": "cc.Node", "_name": "p67", "_parent": {"__id__": 2}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "95IKrhnXhH6K+pjAY0QCzV"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "fbgynYNfhMtYNUF+d3/E9g"}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 154}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}}, {"__type__": "cc.Node", "_name": "blue", "_parent": {"__id__": 1}, "_children": [{"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "aebkI9ipVDlKAgpHzxDuUQ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3, 33.87, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}}, {"__type__": "cc.Node", "_name": "p72", "_parent": {"__id__": 6}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c5auU1NC5JLI1nBD265fmK"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "83PGlFfn5MpoepxJmWfaED"}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 154}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}}, {"__type__": "cc.Node", "_name": "p71", "_parent": {"__id__": 6}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "89BQMHz/tNipyz4n5jEEPl"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "7bfGXiRalN7rtAJYGEFKzC"}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 154}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}}, {"__type__": "cc.Node", "_name": "p70", "_parent": {"__id__": 6}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8fJWFBxjlLGIe2Ji5qt4xW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ddSzbA84hBcoFVOTB5Dzm7"}, "fileId": "d9+E+w3rdCN42PDpq56id+"}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 154}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}}]]