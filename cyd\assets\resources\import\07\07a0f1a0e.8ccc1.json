[[{"__type__": "cc.Prefab", "_name": "map290", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map290", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 58}, {"__id__": 59}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg1", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "74qKsjptxBwL1ZemhMlsFf"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1280, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg2", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "9aw7qAqoBETbO+6d9hZOB8"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg3", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "52lfDtXIVJFKZljutX9jzu"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1280, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 6}, {"__id__": 7}, {"__id__": 10}, {"__id__": 13}, {"__id__": 23}, {"__id__": 40}, {"__id__": 42}, {"__id__": 49}, {"__id__": 52}, {"__id__": 55}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "加速前进", "_N$string": "加速前进", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "c6eqfr74tGJL6WOSZvO7I7"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeDis", "_parent": {"__id__": 5}, "_children": [{"__id__": 8}, {"__id__": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "a2T9fFXDBIlYDpp4p2K8n4"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 140, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "前进距离", "_N$string": "前进距离", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "94dEEbVLhCVLbcIQcAFnj6"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 20, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelCount", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "0/10000", "_N$string": "0/10000", "_fontSize": 32, "_lineHeight": 36, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "f7Br5CdMVLf5OIpc52ix+6"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 115.67, "height": 45.36}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDoorEnd", "_parent": {"__id__": 5}, "_children": [{"__id__": 11}, {"__id__": 12}, {"__type__": "cc.Node", "_name": "posEnd", "_parent": {"__id__": 10}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "eaHOyaQ65EuJ026AVUqgAf"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 61, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 10}, "_type": 0, "_gravityScale": 3, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 10}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 62}, "_size": {"__type__": "cc.Size", "width": 100, "height": 124}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 10}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "4b05IwIm5ISJhTO9HKxbjz"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 10}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 11}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 11}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "4c+g/QPAZGS6D4jT8p9fxV"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 12}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "82Ulu1MmpCPo76U+SndJze"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 147, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform", "_parent": {"__id__": 5}, "_children": [{"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 13}, "_type": 1, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 13}, "_offset": {"__type__": "cc.Vec2", "y": -320}, "_size": {"__type__": "cc.Size", "width": 256, "height": 640}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 13}, "msgId": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "32+oyDDe5O04WtPLz9Kj/N"}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-368, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 14}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "7dSQ1bpChG2q1z+m0V+EqO"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 576}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 15}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "391ZwF1MZFVIRVE/Id7wQT"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "11/bDyJFdGA675ki1JdPDB"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "62xfIHMVlAvoTWibwzFfoD"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [116.807, -130.692, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 13}, "_children": [{"__id__": 18}, {"__id__": 19}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 17}, "_layoutSize": {"__type__": "cc.Size", "width": 128, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "0fWccCbGNJEqQksoyFK7PO"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 17}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "f8Shpr8h5FrYbGycFoZbii"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 17}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "cdj4vpx8xFo4ZfyTWXfJnz"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 20}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "6fsDApFa1EsbtidSXFmY/I"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 21}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "257yJlGctEwbzdnTtUj65o"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, -352, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 22}, "_alignFlags": 13, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "54T49DymtIUbr6XOkibMfS"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, -352, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "groundParent", "_parent": {"__id__": 5}, "_children": [{"__id__": 24}, {"__id__": 39}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "84eWDu/HREaYvLbhgsl+WS"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeGround", "_parent": {"__id__": 23}, "_children": [{"__id__": 25}, {"__id__": 32}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 24}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 24}, "_offset": {"__type__": "cc.Vec2", "y": 342}, "_size": {"__type__": "cc.Size", "width": 120, "height": 378}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 24}, "_offset": {"__type__": "cc.Vec2", "y": -342}, "_size": {"__type__": "cc.Size", "width": 120, "height": 378}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 24}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "e2Sm2cm0lH5oXTNSvAT7a8"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 1068}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groundTop", "_parent": {"__id__": 24}, "_children": [{"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}, {"__id__": 31}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "afF/PiXK1Jh6I78aeHxbUr"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 384}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 167, 0, 0, 0, 0, 1, 1, -1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b19", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "11/bDyJFdGA675ki1JdPDB"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "56nTf6BElHAJyHPuED7t1K"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -5, 0, 0, 0, 1, 6.123233995736766e-17, 1, -1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 27}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "d48EZysZNFC5gX9zVVgMtX"}, "_contentSize": {"__type__": "cc.Size", "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 28}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "51SACyGjpAtpcxFnCPMFA+"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 29}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "94L8qCWARGb4ng1bGblWiX"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 30}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "1aZkb2IL1ChJ6F2OpOnDo+"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 320}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 31}, "_alignFlags": 13, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "aeZYCgFktI8JYp1bJWYNxj"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 320}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groundBottom", "_parent": {"__id__": 24}, "_children": [{"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "c8VCRYrdlABIPjPFhd/bLw"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 384}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -167, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b19", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "11/bDyJFdGA675ki1JdPDB"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "8a3otDDlxNnKQJqY9SfDE+"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 34}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "beqUGb3G9LH5wGYKrMkf/t"}, "_contentSize": {"__type__": "cc.Size", "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 35}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "3a9yV6aoNMjqqk1Q/O4x1B"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 36}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "92eEEBIIJAd6w8G3sbEdZ+"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 37}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "24VunHmu9JvJN1NWoY7Cek"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 320}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 38}, "_alignFlags": 13, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "02xSp006dExotLczyLe9+p"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 320}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBroken", "_parent": {"__id__": 23}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 39}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "posui", "_preCacheMode": 0, "loop": false, "premultipliedAlpha": false, "_animationName": "posui", "_playTimes": 1, "_N$skeletonData": {"__uuid__": "a7bG+6j/FL07eRiZH6iQhN"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "55Usjqv3NOP7yacIXkfIfL"}, "_contentSize": {"__type__": "cc.Size", "width": 105, "height": 108}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -217, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 5}, "_children": [{"__id__": 41}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 40}, "_allowSleep": false, "_gravityScale": 4.5, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 40}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 34, "height": 66}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 40}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "28GMGMxHlEv7A5bxtgzW5J"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-376, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 40}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "657iLqm8JBF7Gx0/O14W5K"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeProp", "_parent": {"__id__": 5}, "_children": [{"__id__": 43}, {"__id__": 44}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 42}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 42}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 25}, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 42}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "ffPOarFhBHtoAyq1zhtWPl"}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-278.448, -17.552, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeProp1", "_parent": {"__id__": 42}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "91g6QdksFHxKLkWIlNgude"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "a6JJRxvXRGoa7hG4mcsbg/"}, "_contentSize": {"__type__": "cc.Size", "width": 102, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeProp2", "_parent": {"__id__": 42}, "_children": [{"__id__": 45}, {"__id__": 46}, {"__id__": 47}, {"__id__": 48}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "17gAKzlg5GkqZQgYmDfvb3"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLight1", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ffBeT8z7tLKqky2CHp229B"}}, {"__type__": "cc.Animation", "node": {"__id__": 45}, "_defaultClip": {"__uuid__": "27gVcEE/hO9rOLV0TmMG2i"}, "_clips": [{"__uuid__": "27gVcEE/hO9rOLV0TmMG2i"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "9diCIDTCZH/IdgP1VcN52e"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12, -37.04, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeLight2", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ffBeT8z7tLKqky2CHp229B"}}, {"__type__": "cc.Animation", "node": {"__id__": 46}, "_defaultClip": {"__uuid__": "27gVcEE/hO9rOLV0TmMG2i"}, "_clips": [{"__uuid__": "27gVcEE/hO9rOLV0TmMG2i"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "7648Xhx21GzLMhUJwhPtsH"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [14, -37.04, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "a712", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1hmfVcxVE3aCbm0g42gHI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "b8RYmILRZFOJe5XPxC+FUW"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a712", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1hmfVcxVE3aCbm0g42gHI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "d1Fz6IJ5RKboIACmIVcmuU"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [12, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWaterRoot1", "_parent": {"__id__": 5}, "_children": [{"__id__": 50}, {"__id__": 51}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "0f2RfBzUVHfrjjOOBq9h29"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1280, -260, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWave1", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e71lsajAhFdqy/7GmTWm0S"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 50}, "_alignFlags": 40, "_left": -64, "_right": -64, "_originalWidth": 600}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "e7hFhyH9BGRaB7byY6fhUN"}, "_contentSize": {"__type__": "cc.Size", "width": 1408, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b12", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "67Fjl1ABZBybi990drEiVO"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 51}, "_alignFlags": 40, "_originalWidth": 600}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "aeBEDjvyhAtr6c9nmNZKWJ"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 288}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWaterRoot2", "_parent": {"__id__": 5}, "_children": [{"__id__": 53}, {"__id__": 54}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "26jFCpI+tO671eZoaNuc7z"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -260, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWave2", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e71lsajAhFdqy/7GmTWm0S"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 53}, "_alignFlags": 40, "_left": -64, "_right": -64, "_originalWidth": 600}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "6fNlvQBxFEMqu9MQijr77t"}, "_contentSize": {"__type__": "cc.Size", "width": 1408, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b12", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "67Fjl1ABZBybi990drEiVO"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 54}, "_alignFlags": 40, "_originalWidth": 600}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "66N8Q+HUFJ7Z/qFvqQbV6G"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 288}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWaterRoot3", "_parent": {"__id__": 5}, "_children": [{"__id__": 56}, {"__id__": 57}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "02YDUrwNNINowPw0bece1Y"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1280, -260, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWave3", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e71lsajAhFdqy/7GmTWm0S"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 56}, "_alignFlags": 40, "_left": -64, "_right": -64, "_originalWidth": 600}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "66odWuqTBEP4X7+XAznNw0"}, "_contentSize": {"__type__": "cc.Size", "width": 1408, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b12", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "67Fjl1ABZBybi990drEiVO"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 57}, "_alignFlags": 40, "_originalWidth": 600}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "6dR1KeNOpM9au0JB/xuXgv"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 288}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_buttonOperate", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "62HNe6BzRGc6Q7rrIe8twG"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "93qKulusxBzobwZXOaK2ti"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 102}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -200, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDev", "_parent": {"__id__": 1}, "_children": [{"__id__": 60}, {"__id__": 62}, {"__id__": 64}, {"__id__": 66}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "19138NGlFN66nhGQ2ZcmTQ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-250, -100, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonShow", "_parent": {"__id__": 59}, "_children": [{"__id__": 61}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "baiGQZEQ1FQ5K/KRGxf6fl"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 60}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "显示/隐藏", "_N$string": "显示/隐藏", "_fontSize": 18, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "01nIjk5R1IaIQ5wyhW6JTm"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAdd10", "_parent": {"__id__": 59}, "_children": [{"__id__": 63}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "79C451kRREq7yoPnY0HdlW"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 62}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 63}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "+10", "_N$string": "+10", "_fontSize": 18, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "68xjcPzu1K14yb3pmSGpPa"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30.53, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAdd100", "_parent": {"__id__": 59}, "_children": [{"__id__": 65}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 64}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "49mU3J8mVF2b2smXRwvpW3"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 64}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "+100", "_N$string": "+100", "_fontSize": 18, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "49D57/L9NCVZ9lPHl3wMHu"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40.54, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAdd1000", "_parent": {"__id__": 59}, "_children": [{"__id__": 67}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 66}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "446xBhWCJLXYfOGYV1a68R"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 66}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "+1000", "_N$string": "+1000", "_fontSize": 18, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "268r4Sn6lIGZuhEJjGXSyy"}, "fileId": "eelqql1dFBkZX8mh5AH+3k"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 50.55, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "a711", "texture": "1c735b164", "rect": [3, 354, 148, 102], "offset": [0, 0], "originalSize": [148, 102], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a710", "texture": "1c735b164", "rect": [157, 354, 102, 68], "offset": [0, 0], "originalSize": [102, 68], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a712", "texture": "1c735b164", "rect": [111, 741, 58, 68], "offset": [0, 0], "originalSize": [58, 68], "rotated": 1, "capInsets": [0, 0, 0, 0]}}]