[{"__type__": "cc.SpriteFrame", "content": {"name": "p41", "texture": "17uzAgSYZIW5L75bS+Zikj", "rect": [0, 0, 52, 60], "offset": [0, 0], "originalSize": [52, 60], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map7018lightoffAnim", "curveData": {"paths": {"close": {"props": {"active": [{"frame": 0, "value": true}]}}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p42", "texture": "48SslnUB5FjYjRHigPjmXH", "rect": [0, 0, 52, 60], "offset": [0, 0], "originalSize": [52, 60], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p43", "texture": "14aXbcZPtGPr9uLqIqNrOn", "rect": [0, 0, 52, 60], "offset": [0, 0], "originalSize": [52, 60], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p44", "texture": "8bgLLZ8l9ArreQ5s0Zn9FI", "rect": [0, 0, 52, 60], "offset": [0, 0], "originalSize": [52, 60], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "p45", "texture": "7fhBzdVyhJ3pOCkNixLhU1", "rect": [0, 0, 52, 60], "offset": [0, 0], "originalSize": [52, 60], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map7018lightonAnim", "curveData": {"paths": {"close": {"props": {"active": [{"frame": 0, "value": false}]}}}}}]