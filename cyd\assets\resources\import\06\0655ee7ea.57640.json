[[{"__type__": "cc.Prefab", "_name": "vGameDailyChallengeDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeWhole", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 8}, {"__id__": 9}, {"__id__": 21}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "53HP5IlxZKj5krsm05vqzP"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBg", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "10wH31IrZC0oThOVwTra8B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 80, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c6Td9gEiNAOocqZOGnx1lq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "9dkej7o8lBHbVKsNPKWk2y"}, "_contentSize": {"__type__": "cc.Size", "width": 656, "height": 409}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "t007", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f35NR3DnxIerqnm8KBUc6R"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "1310ZKVd5C+YnR3tQ2ZI89"}, "_contentSize": {"__type__": "cc.Size", "width": 139, "height": 33}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 123.438, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 3}, "_children": [{"__id__": 7}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 6}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "2fo56SCAZCOY98kNfa/qgb"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [277.55, 124.219, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b06", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48+6YuBptMr4rHwXDDBRsQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "98h8Qdm9dLfa8SyfWo+Er7"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTip", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "完成60关可为所在的队伍获得1分", "_N$string": "完成60关可为所在的队伍获得1分", "_fontSize": 24, "_enableWrapText": false, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "20bND0SNZHV6bipDO3WJEQ"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 344.9, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 18.989, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeSimple", "_parent": {"__id__": 2}, "_children": [{"__id__": 10}, {"__id__": 13}, {"__id__": 16}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 9}, "_layoutSize": {"__type__": "cc.Size", "width": 660, "height": 70}, "_resize": 1, "_N$layoutType": 1, "_N$spacingX": 90}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "45j7BI34dGhLAQU1NUuc81"}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -72.47, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonWaive", "_parent": {"__id__": 9}, "_children": [{"__id__": 11}, {"__id__": 12}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 10}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "9cpOIG57xCG5LnK1RIoFuh"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-250, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c04", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UluCm4dBo42BBMDb/rwX"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "c1Hk34h65AtLM7WiR2tUZn"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.3, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "放弃", "_N$string": "放弃", "_fontSize": 26, "_lineHeight": 26, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "11N8iglt5AMqLGhf+pf4+o"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonStart", "_parent": {"__id__": 9}, "_children": [{"__id__": 14}, {"__id__": 15}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 13}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "6aCzyoYedGdqpU6FIiyaot"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c04", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UluCm4dBo42BBMDb/rwX"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "9f5Swo5z1HgYTiLSQAJv+m"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.3, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "去挑战", "_N$string": "去挑战", "_fontSize": 26, "_lineHeight": 26, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "2feo4R/FdLiqbl5SrbWwrL"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRetryAd", "_parent": {"__id__": 9}, "_children": [{"__id__": 17}, {"__id__": 18}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 16}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "cc5Y/NxmBPzYpqkTkR/lHU"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [250, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c04", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UluCm4dBo42BBMDb/rwX"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "6c9cK+S+5BsbTGZ7L/XaEa"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1.3, 1, 1]}}, {"__type__": "cc.Node", "_name": "layout", "_parent": {"__id__": 16}, "_children": [{"__id__": 19}, {"__id__": 20}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 18}, "_layoutSize": {"__type__": "cc.Size", "width": 131, "height": 150}, "_resize": 1, "_N$layoutType": 1, "_N$spacingX": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "f2KrR9LzdLJargxiPPvDiX"}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 150}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteAdIcon", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f8eJnfOM1KYZTMKdEM6EDd"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "70f5BcBtJFp7k6rFv9BC0h"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-42.5, -1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "重新挑战", "_N$string": "重新挑战", "_fontSize": 20, "_lineHeight": 26, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "c1JiKQEdFBjqTqqyGBXQ0U"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [25.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeModleicon", "_parent": {"__id__": 2}, "_children": [{"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}, {"__id__": 27}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "c7AwiD9/tGY56JVAxPshYX"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 108.151, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "reaction", "_parent": {"__id__": 21}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2cRSGfYbFFZLGLsF9o5vQY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "47pYrWEB9Pnq2vhAAlWULC"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 152}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10.466, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "brains", "_parent": {"__id__": 21}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "88FpYz8A5LVLYSe0KHJt/N"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "53vJKhGFFHg4gt/5bQsBP8"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 152}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10.466, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "velocity", "_parent": {"__id__": 21}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "cev+Z9CTNF2YnPE+sDy4Ux"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "bbDYJhPttNJI2giJMYEhbZ"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 152}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10.466, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "luck", "_parent": {"__id__": 21}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d3ZGp5rXlA4plZ/ENvopqA"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "12ttngoCJKrID0P0Sp0sub"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 152}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10.466, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "memory", "_parent": {"__id__": 21}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "63jAF/wQJLMoecdgP4M6q7"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "caG/w/X1dIPo1kFIeDDRPn"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 152}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10.466, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "discernment", "_parent": {"__id__": 21}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2eHEOi+VFOMLa5aetEHugq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cbdkYiVBlBW6WEXTj72JbJ"}, "fileId": "5454aOFuJN7rHcQEIYjQRY"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 152}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10.466, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "t005", "texture": "02BUMzEWxAg6w6JXqIzryU", "rect": [0, 0, 139, 33], "offset": [0, 0], "originalSize": [139, 33], "capInsets": [0, 0, 0, 0]}}]