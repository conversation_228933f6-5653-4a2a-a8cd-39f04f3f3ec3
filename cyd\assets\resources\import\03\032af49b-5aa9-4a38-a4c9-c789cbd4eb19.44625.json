[{"__type__": "cc.Prefab", "_name": "vGotItem", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vGotItem", "_children": [{"__id__": 2}, {"__id__": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "03KvSbWqlKOKTJx4nL1OsZ"}, "fileId": "bd9Db43uNBVa5EwDPcF+Km"}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 110}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteIcon", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "03KvSbWqlKOKTJx4nL1OsZ"}, "fileId": "3e4RdRs3pORbulglGLh/w0"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 0.9]}}, {"__type__": "cc.Node", "_name": "CC_labelNum", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "100", "_N$string": "100", "_fontSize": 24, "_lineHeight": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "03KvSbWqlKOKTJx4nL1OsZ"}, "fileId": "20RsabT0pL1YzNwLbiVLS8"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40.04, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 0.9, 0.9, 0.9]}}]