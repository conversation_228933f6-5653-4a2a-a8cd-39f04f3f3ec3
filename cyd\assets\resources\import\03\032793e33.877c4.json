[[{"__type__": "cc.Prefab", "_name": "vNativeBanner", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vNativeBanner", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "fdaAqmc5xHv7eoeO34gpOu"}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1cIDPMflVEhIvOUQ9aetxm"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "bd71gCX4xDDoR9frMmxYES"}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容", "_N$string": "这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容", "_fontSize": 16, "_lineHeight": 18, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "c90Dq2KKtPOYovEut1lWjq"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-10, 48, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTitle", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "标题", "_N$string": "标题", "_fontSize": 24, "_lineHeight": 24, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "17GyMgxyNK5ZOsWYzckBtH"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-10, 99, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteIcon", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "bb7XOBIAdHmKrtGpmFn1js"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-144, 64, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "nodego", "_parent": {"__id__": 1}, "_children": [{"__id__": 7}, {"__id__": 8}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_sizeMode": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "9dAU07e31CV5+hIxDsl1Ut"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [131, 64, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c04", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UluCm4dBo42BBMDb/rwX"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "65AwFeDVBArKSR4rKITR3A"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.6, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTargetTitle", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "查看详情", "_N$string": "查看详情", "_fontSize": 16, "_lineHeight": 16, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "96gutCU8RJw7RXLwdp/kjh"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "广告", "_N$string": "广告", "_fontSize": 16, "_lineHeight": 16, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "a0yLCRI0REwLGYEopQbR9R"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 20.16}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-144, 16, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelAdSource", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "名字名字", "_N$string": "名字名字", "_fontSize": 16, "_lineHeight": 16, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "91QSmQJ4xK7r4AGyNNXwKg"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 16}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [131, 27, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonGo", "_parent": {"__id__": 1}, "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 11}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "78ftA3oB9KO5Et9Nt8gX5X"}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 128}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 1}, "_children": [{"__id__": 13}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 12}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "e7IvodAJBOxIxFIdnDy4Z+"}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 45}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [181.105, 113.173, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "art_02", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48+6YuBptMr4rHwXDDBRsQ"}, "_sizeMode": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "10M8kWiZxM4rDtszeb3dEL"}, "fileId": "5aQvFWwwRL6aXdk6/3gFni"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "b01", "texture": "11268548a", "rect": [91, 3, 478, 128], "offset": [0, 0], "originalSize": [478, 128], "rotated": 1, "capInsets": [0, 0, 0, 0]}}]