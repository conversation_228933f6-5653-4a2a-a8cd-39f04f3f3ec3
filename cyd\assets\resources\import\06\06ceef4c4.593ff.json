[{"__type__": "cc.SpriteFrame", "content": {"name": "t037", "texture": "deLx+AAT5C8aa1kv484t/n", "rect": [0, 0, 60, 52], "offset": [0, 0], "originalSize": [60, 52], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t020", "texture": "2erqX5+/dO34eEQUT4AGP9", "rect": [0, 0, 689, 12], "offset": [0, 0], "originalSize": [689, 12], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t028", "texture": "e1WPC6vw5Ar4N0d3mh0uS+", "rect": [0, 0, 155, 24], "offset": [0, 0], "originalSize": [155, 24], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t021", "texture": "d6sEdAEihOPZnNpvqUGQZT", "rect": [0, 0, 64, 19], "offset": [0, 0], "originalSize": [64, 19], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t025", "texture": "1cS0A7gBxGL5YsbALpovME", "rect": [0, 0, 148, 48], "offset": [0, 0], "originalSize": [148, 48], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vUserInfoDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vNativeDialog", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "d1C5Y8y7BJGaA8GjMorBX8"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "parent", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 18}, {"__id__": 27}, {"__id__": 29}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "05xtjbTaxBSbgU1gU0Z9Pp"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9cJwXtuRxHYpmLoBg+xIwk"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "1cRWv/54pGHLc6Zq//oL6e"}, "_contentSize": {"__type__": "cc.Size", "width": 842, "height": 450}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7, 1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeNameInfo", "_parent": {"__id__": 2}, "_children": [{"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__type__": "cc.Node", "_name": "CC_nodeNameContent", "_parent": {"__id__": 4}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "ffxP4hZRxKJ7DdurJmL5rJ"}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -56.6, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "dbyKb/bjpEnr/PhBRMn0PV"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "title", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3cU0LnkRxI2KrTfW5/PvmE"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "034yZ8Np9GwbuXSg+noXbj"}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 72.047, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "t020", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1f8WCuar5BbqvKvU1jhsbu"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "12Eh8iO3NProUgCin1w1u0"}, "_contentSize": {"__type__": "cc.Size", "width": 689, "height": 12}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 48.299, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRepeat", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0a3sPy5xBDjqFjrgyo95FJ"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "220G7mHv9FAqCTZjeJ7h4a"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [345.238, 84.033, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "t021", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "65s8NJYbhDWJK1e7sY59pH"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "28ejS8td5HaKhEEa07vTed"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 19}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [283.238, 67.033, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "name", "_parent": {"__id__": 4}, "_children": [{"__id__": 10}, {"__id__": 17}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9c+/l1nZZH3bKZ9q8KVmhS"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "f5b6cByXtLCpcWN7dsnhFh"}, "_contentSize": {"__type__": "cc.Size", "width": 404, "height": 65}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-18, 135, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 9}, "_children": [{"__id__": 11}, {"__id__": 14}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 10}, "_layoutSize": {"__type__": "cc.Size", "width": 301, "height": 200}, "_resize": 1, "_N$layoutType": 1, "_N$spacingX": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "d09rHqaB5OC4aY/Wpa/JM4"}, "_contentSize": {"__type__": "cc.Size", "width": 301, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [37.298, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonName1", "_parent": {"__id__": 10}, "_children": [{"__id__": 12}, {"__id__": 13}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "69HL2lacJEIaWJ4fz1xwxm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "60goD8mj9In7gsxYy6l6Xb"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-76.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "name", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_fontSize": 25, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "7dHOPKJcxIZagtG36kbS5L"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "select", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b0MeM71M1Dc5mh97UfKdyc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "a0Rjufbs9OB4ijWRNuEJa+"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonName2", "_parent": {"__id__": 10}, "_children": [{"__id__": 15}, {"__id__": 16}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "69HL2lacJEIaWJ4fz1xwxm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "205gX+qgdMVKskG9eRSAF/"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [76.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "name", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_fontSize": 25, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "37hzxIW3xNFZKfewWYeJ3y"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "select", "_parent": {"__id__": 14}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b0MeM71M1Dc5mh97UfKdyc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "d9bfH3vj9MhIc2G4Yq9hCk"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRandomName", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "da4rJJEzhJhIvzRTL760Y0"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "63LZyn7e9C7KEB/2sGVszk"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [236.355, -0.971, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTeamInfo", "_parent": {"__id__": 2}, "_children": [{"__type__": "cc.Node", "_name": "CC_nodeTeamContent", "_parent": {"__id__": 18}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "9bqo7V14ZB5pvFWJb3MHiS"}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 256.6}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -11.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 19}, {"__id__": 22}, {"__id__": 25}, {"__id__": 26}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "92PurRnKhJOqKoMdm3sJa0"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnLeft", "_parent": {"__id__": 18}, "_children": [{"__id__": 20}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "2af1N72ZdGbL5oDAggSTS4"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-450, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonLeft", "_parent": {"__id__": 19}, "_children": [{"__id__": 21}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 20}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "20P5UTqWRMhLdDJn2AiuoY"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b07", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6fQlDtgX1Db5C3y1esGtZ8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "767dqUv4NIor//T61Fpx0i"}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 88}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnRight", "_parent": {"__id__": 18}, "_children": [{"__id__": 23}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "48BH4U7mlA7bk2hgfawHfN"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [450, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRight", "_parent": {"__id__": 22}, "_children": [{"__id__": 24}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 23}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "0cXyZ46eFMzLNvEUlKb4WI"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b07", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6fQlDtgX1Db5C3y1esGtZ8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "c8ThTSC5NKsYYmtPVuAcjH"}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 88}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "team", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d6Oa+hNrlC+5+6gfCFetDo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "0eEBHGIehCVb4pzqU807oL"}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 155.327, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "t020", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1f8WCuar5BbqvKvU1jhsbu"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "01rcltQUVEXoMpbtOBsDAg"}, "_contentSize": {"__type__": "cc.Size", "width": 689, "height": 12}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 131.817, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonOK", "_parent": {"__id__": 2}, "_children": [{"__id__": 28}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78/01KIaBJGJCxAuSY2x9h"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "b722e1ItNBRpASXfKvpbzX"}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -201.898, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "确定", "_N$string": "确定", "_fontSize": 25, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "8f2JDcJ/RFYJbiY7nFj+KO"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 2}, "_children": [{"__id__": 30}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 29}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "adK1vCbVZALLkBKGiEORWv"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [385.421, 183.097, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b06", "_parent": {"__id__": 29}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48+6YuBptMr4rHwXDDBRsQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8ed4VpUWpEb6Mb5ySZ+czG"}, "fileId": "61xFGe8AdA7ba9Fe2vrHGn"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "t017", "texture": "eatiL7I6ZAhZti6w8Kjc2s", "rect": [0, 0, 842, 450], "offset": [0, 0], "originalSize": [842, 450], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t022", "texture": "b3n0DpfrFPaZRr2xrhj4LX", "rect": [0, 0, 392, 65], "offset": [0, 0], "originalSize": [392, 65], "capInsets": [181, 16, 200, 21]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t026", "texture": "cdn3CadZJLwoTMZgulCGdF", "rect": [0, 0, 148, 48], "offset": [0, 0], "originalSize": [148, 48], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t027", "texture": "4c9J9/0C1HX5TLaTu2nnyB", "rect": [0, 0, 155, 25], "offset": [0, 0], "originalSize": [155, 25], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t023", "texture": "b18HM50YpDrb2LOWlDH77+", "rect": [0, 0, 52, 53], "offset": [0, 0], "originalSize": [52, 53], "capInsets": [0, 0, 0, 0]}}]