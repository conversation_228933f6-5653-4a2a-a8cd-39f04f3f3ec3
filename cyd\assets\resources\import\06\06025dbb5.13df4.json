[[{"__type__": "cc.Prefab", "_name": "vLaunchRewardKuaiShouDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vLaunchRewardKuaiShouDialog", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "77oWC+yl9CjbrFtRpxvLk+"}, "fileId": "ebIJZVra9PHa6aO92n/U21"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "parent", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "77oWC+yl9CjbrFtRpxvLk+"}, "fileId": "12HdIvQ/pDmIXWmu0KprT8"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8bXyV/ZGNML7063dAPOcnr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "77oWC+yl9CjbrFtRpxvLk+"}, "fileId": "f3vLLMBcBEhJVvk1OcBRAq"}, "_contentSize": {"__type__": "cc.Size", "width": 376, "height": 465}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonSure", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f31qwNdHlC7ZtC6bkFlhlT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "77oWC+yl9CjbrFtRpxvLk+"}, "fileId": "c6giekHgBF1okyra60urv/"}, "_contentSize": {"__type__": "cc.Size", "width": 147, "height": 61}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -170, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "bg1", "texture": "0f8QuPbeFFca8admerEYib", "rect": [0, 0, 376, 465], "offset": [0, 0], "originalSize": [376, 465], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "2", "texture": "13uaMdxvtNhqoBeI7npSM+", "rect": [0, 0, 147, 61], "offset": [0, 0], "originalSize": [147, 61], "capInsets": [0, 0, 0, 0]}}]