[{"__type__": "cc.SpriteFrame", "content": {"name": "t095", "texture": "d76eUt6spFfr7VlVI3kWEQ", "rect": [0, 0, 96, 98], "offset": [0, 0], "originalSize": [96, 98], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t096", "texture": "07dozXUFZBJqpDaNj0JQr4", "rect": [0, 0, 110, 86], "offset": [0, 0], "originalSize": [110, 86], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "mapVelocity", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "mapVelocity", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 10}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "在规定时间内点击小黑获取积分", "_N$string": "在规定时间内点击小黑获取积分", "_fontSize": 36, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "36lK4g4CNHB63CKj+eSEul"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 504, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 177.647, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_labelTime", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "10s", "_N$string": "10s", "_fontSize": 60, "_lineHeight": 60, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "9dRRDEHqRMkZJD5lzDH3d4"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 100.11, "height": 75.6}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [399.928, 139.505, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLayer", "_parent": {"__id__": 2}, "_children": [{"__id__": 6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "29FNkH4J1Fep50gCuJ5s2c"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBox", "_parent": {"__id__": 5}, "_children": [{"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "7bfgbedOBFBo3z+Y5etdTU"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "t062", "_parent": {"__id__": 6}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "49nkSHG0tJ0qG3RMnSPlpl"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "04y5e8aQVI7JsDQRccyhKD"}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 86}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "t063", "_parent": {"__id__": 6}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2aH5D5LW5Gr6xI9vpSSZ9+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "8eWz7sSKREY70G2lSaJWrV"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 98}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "t064", "_parent": {"__id__": 6}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a7Zj0Uj8FCYqakWaEZQX7a"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "4fQqc+D71G1bQk5jdZG7vS"}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 112}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTip", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "点击小黑后开始计时", "_N$string": "点击小黑后开始计时", "_fontSize": 36, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7e4mU7TxJPzq0LCdGRkskI"}, "fileId": "98NFW/utpLaJr5NuBSMwY1"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 324, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, -189.224, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "t097", "texture": "4bSifssidNpIESGaPyeUW+", "rect": [0, 0, 82, 112], "offset": [0, 0], "originalSize": [82, 112], "capInsets": [0, 0, 0, 0]}}]