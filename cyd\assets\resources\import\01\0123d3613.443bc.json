[[{"__type__": "cc.Prefab", "_name": "vAddGameToMyFavoriteComfirmDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "node", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "407l3A9+tOYYHhEGVKx4+i"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "7fDVZyN8xFZYG4MqXfHc8u"}, "_color": {"__type__": "cc.Color", "r": 81, "g": 60, "b": 60}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0pnCzDlxFXZf5e9sQwZFB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "cfI6Dqs05Eb5+xTM9ay0L6"}, "_contentSize": {"__type__": "cc.Size", "width": 376, "height": 452}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "art02", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d3oBnWp7dNYJ5SzZzQBlCp"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "19ii+sQidKg471gULn1mTT"}, "_contentSize": {"__type__": "cc.Size", "width": 184, "height": 37}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 180, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelDesc", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "将茶叶蛋大冒险设为常用，后续可以直接从侧边栏“常用小程序”快速访问", "_N$string": "将茶叶蛋大冒险设为常用，后续可以直接从侧边栏“常用小程序”快速访问", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1, "_N$overflow": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "e7naB7++RJNoXC5JI1Apwt"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 78.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, 110, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "领取奖励", "_N$string": "领取奖励", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1, "_N$overflow": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "05p6cJ24BNFqID9pe2w9hT"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 30.240000000000002}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, -40, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "解锁模式", "_N$string": "解锁模式", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "6fg8zhXgFPiIf/SfNJ0N4x"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a15", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "cd0NNOW5BOYahwbSWGDx3S"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "7aMvFFpgpAYLAuLoIqCwF6"}, "_contentSize": {"__type__": "cc.Size", "width": 386, "height": 166}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -120, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonOK", "_parent": {"__id__": 2}, "_children": [{"__id__": 10}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 9}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "a5IghPIw1PmIJ7FaHNbPQr"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -215, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c04", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c+T+fdIJHCL+smF6naH0x"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8dAh780HhJgYzO847UYwOU"}, "fileId": "72oIRPRc9N7ZLZz1IdRJvs"}, "_contentSize": {"__type__": "cc.Size", "width": 147, "height": 61}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "art03", "texture": "e2TsgZ9pVEHb/Rw/SHVVzM", "rect": [0, 0, 184, 37], "offset": [0, 0], "originalSize": [184, 37], "capInsets": [0, 0, 0, 0]}}]