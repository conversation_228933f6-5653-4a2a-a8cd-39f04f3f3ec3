{"__type__": "sp.SkeletonData", "_name": "yun_zi", "_skeletonJson": {"skeleton": {"hash": "Dr13cIggS0LDNYcfpONAChlEwuo", "spine": "3.6.52", "width": 109.13, "height": 57.17, "images": ""}, "bones": [{"name": "root"}, {"name": "dian", "parent": "root", "length": 15.4, "rotation": 129.42, "x": -2.31, "y": 8.02}, {"name": "normal/yun", "parent": "root", "x": 51.27, "y": 2.64}, {"name": "wu", "parent": "root", "length": 48.9, "rotation": 0.76, "x": 0.14, "y": -0.13}, {"name": "smoke_01_00000", "parent": "wu", "length": 59.08, "rotation": 119.2, "x": 0.74, "y": -5.87}, {"name": "smoke_01_0", "parent": "wu", "length": 59.08, "rotation": -116.99, "x": 9.57, "y": -13.65, "scaleX": -1}], "slots": [{"name": "wu", "bone": "wu", "attachment": "wu"}, {"name": "dian", "bone": "dian", "attachment": "dian"}, {"name": "smoke_01_00000", "bone": "smoke_01_00000"}, {"name": "smoke_01_0", "bone": "smoke_01_0"}, {"name": "normal/yun", "bone": "normal/yun", "attachment": "normal/yun"}], "skins": {"default": {}, "normal": {"dian": {"dian": {"name": "normal/dian", "x": 6.65, "y": -1.33, "rotation": -129.42, "width": 26, "height": 33}}, "normal/yun": {"normal/yun": {"x": 0.36, "y": -1.81, "width": 56, "height": 54}}, "smoke_01_0": {"smoke_01_00000": {"name": "normal/smoke_01_00000", "x": 11.34, "y": -3.7, "rotation": -119.96, "width": 67, "height": 69}}, "smoke_01_00000": {"smoke_01_00000": {"name": "normal/smoke_01_00000", "x": 11.34, "y": -3.7, "rotation": -119.96, "width": 67, "height": 69}}, "wu": {"wu": {"name": "normal/wu", "x": -4.1, "y": 3.18, "rotation": -0.76, "width": 51, "height": 56}}}}, "animations": {"dian": {"slots": {"dian": {"attachment": [{"time": 0, "name": null}, {"time": 0.0667, "name": "dian"}]}, "smoke_01_00000": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "smoke_01_00000"}]}, "smoke_01_0": {"color": [{"time": 0.0667, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "smoke_01_00000"}]}}, "bones": {"dian": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0}, {"time": 0.3333, "angle": 5.95}, {"time": 0.4667, "angle": -0.55}, {"time": 0.6333, "angle": 6.84}, {"time": 0.7667, "angle": 0, "curve": "stepped"}, {"time": 0.9, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": -51.91, "y": -7.97, "curve": "stepped"}, {"time": 0.1667, "x": -51.91, "y": -7.97, "curve": "stepped"}, {"time": 0.3333, "x": -51.91, "y": -7.97}, {"time": 0.4667, "x": 0.45, "y": 1.28}, {"time": 0.7667, "x": -0.48, "y": 4.6}, {"time": 0.9, "x": -0.11, "y": 0.2}, {"time": 1, "x": -0.11, "y": 2.1}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1667, "x": 0.591, "y": 0.591, "curve": [0.289, 0.71, 0.75, 1]}, {"time": 0.4667, "x": 1.246, "y": 1.246}, {"time": 0.7667, "x": 1.445, "y": 1.445}, {"time": 0.8333, "x": 0.492, "y": 0.492}, {"time": 0.9, "x": 0.464, "y": 0.464}, {"time": 1, "x": 0.492, "y": 0.492}]}, "smoke_01_00000": {"rotate": [{"time": 0, "angle": -11.66}, {"time": 0.2667, "angle": 3.79}], "translate": [{"time": 0, "x": -57.98, "y": 6.2}, {"time": 0.2667, "x": -58.02, "y": 3.33}], "scale": [{"time": 0, "x": 0.499, "y": 0.499, "curve": [0.346, 0.87, 0.75, 1]}, {"time": 0.2667, "x": 1.08, "y": 1.08}]}, "smoke_01_0": {"rotate": [{"time": 0.0667, "angle": 0}, {"time": 0.3333, "angle": -12.82}], "translate": [{"time": 0.0667, "x": -57.98, "y": 6.2}, {"time": 0.3333, "x": -63.08, "y": 6.58}], "scale": [{"time": 0.0667, "x": 0.499, "y": 0.499, "curve": [0.346, 0.87, 0.75, 1]}, {"time": 0.3333, "x": 1.08, "y": 1.08}]}, "wu": {"rotate": [{"time": 0.7667, "angle": 0, "curve": "stepped"}, {"time": 0.9, "angle": 0}], "scale": [{"time": 0.7667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 0.9, "x": 0.9, "y": 0.9}, {"time": 1, "x": 1, "y": 1}]}, "normal/yun": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}, {"time": 0.9, "angle": 2.1}, {"time": 1, "angle": -2.06}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 0.9, "x": 4.33, "y": 0}, {"time": 1, "x": 6.98, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}}}, "idle": {"slots": {"dian": {"attachment": [{"time": 0, "name": null}]}, "smoke_01_00000": {"attachment": [{"time": 0, "name": null}]}, "smoke_01_0": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"dian": {"translate": [{"time": 0, "x": -64.12, "y": -7.97}]}, "smoke_01_00000": {"translate": [{"time": 0, "x": -65.71, "y": 0.88}]}, "smoke_01_0": {"translate": [{"time": 0, "x": -65.71, "y": 0.88}]}}}, "idle02": {"slots": {"smoke_01_00000": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "smoke_01_00000"}]}}, "bones": {"dian": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -0.11, "y": 2.1}], "scale": [{"time": 0, "x": 0.492, "y": 0.492}]}, "smoke_01_00000": {"rotate": [{"time": 0, "angle": -11.66}], "translate": [{"time": 0, "x": -57.98, "y": 6.2}], "scale": [{"time": 0, "x": 0.499, "y": 0.499}]}, "wu": {"rotate": [{"time": 0, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "normal/yun": {"rotate": [{"time": 0, "angle": -2.06}], "translate": [{"time": 0, "x": 6.98, "y": 0}]}}}}}, "_atlasText": "\nyun_zi.png\nsize: 128,128\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nnormal/dian\n  rotate: true\n  xy: 71, 40\n  size: 26, 33\n  orig: 26, 33\n  offset: 0, 0\n  index: -1\nnormal/smoke_01_00000\n  rotate: false\n  xy: 2, 55\n  size: 67, 69\n  orig: 67, 69\n  offset: 0, 0\n  index: -1\nnormal/wu\n  rotate: true\n  xy: 2, 2\n  size: 51, 56\n  orig: 51, 56\n  offset: 0, 0\n  index: -1\nnormal/yun\n  rotate: true\n  xy: 71, 68\n  size: 56, 54\n  orig: 56, 54\n  offset: 0, 0\n  index: -1\n", "textures": [{"__uuid__": "catCvr4LlCh6lxQURnv8/J"}], "textureNames": ["yun_zi.png"]}