[{"__type__": "cc.SpriteFrame", "content": {"name": "a436", "texture": "1d7eb79f9", "rect": [39, 907, 82, 20], "offset": [0, 0], "originalSize": [82, 20], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "map272", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map272", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 114}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg1", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "fcuwLj+BJBE5S8hKbCTaSr"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -768, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg2", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "38l0C2r8lAZpXiOYhoT8kV"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg3", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "b8MuTaZHtE/L7bbGJj55Q9"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 768, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "我命由我不由天", "_N$string": "我命由我不由天", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "01+74lnx9Ne6iw+K04Fj8E"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 150, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_labelHeight", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "当前高度\n0/10000", "_N$string": "当前高度\n0/10000", "_fontSize": 32, "_lineHeight": 36, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "e5rxk5kMpA7IeVRY3Zoif8"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 81.36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [400, 100, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 8}, {"__id__": 10}, {"__id__": 27}, {"__id__": 63}, {"__id__": 76}, {"__id__": 79}, {"__id__": 83}, {"__id__": 89}, {"__id__": 94}, {"__id__": 99}, {"__id__": 103}, {"__id__": 109}, {"__id__": 111}, {"__id__": 112}, {"__id__": 113}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeHJQBlock", "_parent": {"__id__": 7}, "_children": [{"__id__": 9}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 8}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 8}, "_offset": {"__type__": "cc.Vec2", "x": -67}, "_size": {"__type__": "cc.Size", "width": 134, "height": 15}}, {"__type__": "d0aabggwVlIIplF8BRBIFLQ", "node": {"__id__": 8}, "msgIdIn": 21, "msgIdOut": 22}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7aXKEypDFASanERLeOB+aA"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [330, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeHJQSpine", "_parent": {"__id__": 8}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 9}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "10X3LLrkhG1bZjaaEFkQDQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "5aj11nivlPvJttmpTgv0np"}, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 31}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-10, -58, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGround1", "_parent": {"__id__": 7}, "_children": [{"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 10}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 10}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 320, "height": 320}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 10}, "tag": 2, "_offset": {"__type__": "cc.Vec2", "x": -150, "y": -160}, "_size": {"__type__": "cc.Size", "width": 10, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "c7OcmWRsJCj5YnUyxyz3WA"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 11}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "9aTdWWsPFLi7mHM/fD1vmQ"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 12}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "31hE4NiUpB9rZmLR3RYqbq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 10}, "_children": [{"__id__": 14}, {"__id__": 16}, {"__id__": 17}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 13}, "_layoutSize": {"__type__": "cc.Size", "width": 192, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "2fjkIbOCFClb/o0Rarwf89"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 13}, "_children": [{"__id__": 15}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "b95qP6xM5JfLMszAj4719a"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-64, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "b9dYxxVmRJEa8kI4jAh37P"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "bfJw969UNLUK8FJ0j8olbU"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 13}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "a0zv8ziRdBSpddw/myf1XH"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [64, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 18}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "04tEzG0idBX5UgjKGzMj7t"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 19}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "c46gonj5RB7bFNqRteY3ts"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 20}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "8bkp6ojoBNpaKx9aSZ/d3v"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 21}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "b0oY/2sQhDJa1NJUZykpgp"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 22}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "1fwWnq1SNH2qqX8527jxPc"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 23}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "49m0xUFTdIT7+3X9D9/A9J"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 24}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "d4Kd8qW7RIBLKNevTqkdXr"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 25}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "29H8bNSFZFR7A0qnPooghF"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 26}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "bakqw7tjFJcostFZJJBXON"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground", "_parent": {"__id__": 7}, "_children": [{"__id__": 28}, {"__id__": 29}, {"__id__": 30}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 27}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 27}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 1280, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "092najum9JkLmT7dfEn8P8"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 28}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "1e+3MlJPtO1YubySJA2wf2"}, "_contentSize": {"__type__": "cc.Size", "width": 1152, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 29}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "346XH3HYxEmY7fx4S+wVjP"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 27}, "_children": [{"__id__": 31}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 42}, {"__id__": 43}, {"__id__": 44}, {"__id__": 45}, {"__id__": 46}, {"__id__": 47}, {"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 53}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 30}, "_layoutSize": {"__type__": "cc.Size", "width": 1152, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "a5FkdRomFO/65H2xM2aCIa"}, "_contentSize": {"__type__": "cc.Size", "width": 1152, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 30}, "_children": [{"__id__": 32}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "37Nzy+GUZIxrax9YI4+XG0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "4eiEMtduNKL7gR1TfPGLul"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "80OmgtKd5BWpUL2kIJe9mp"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "86KSd3+TVJ9ajnK35rbJJM"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "fe2cijugVCErejXQvgswQc"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 30}, "_children": [{"__id__": 37}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "06ar7y9ktE5JapXYK2Ho2y"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "c9l2yv9+ZJJLUcWbHSy3Ta"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "2eO52ks4pP9a+OK+G5t2Dn"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "2a2EORs4BI65G96ylKDC1I"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 30}, "_children": [{"__id__": 41}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "e1tHT5hJBA6IUmmnqJPsqQ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 40}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "15TTtSGEFHwoGUOUkMuRzb"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, 28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "4eUotxeC1Na7UgssXcgNfm"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "8bop0+NVJJQJq7ExHDQ/S7"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7cOFF7DF1D/aeVJvysr78l"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "8flLG7yI9Gl6hrav5uupj9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7blr3w+GxJWYU33nN37HLt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 30}, "_children": [{"__id__": 48}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "e9IzODPhtNTb5G6Rv8JNTE"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little04", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78LL5TK2RJ/7QmPvmVHdJr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "dbPxGch5VDrLZfUPovxyGy"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 7.955, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "a82xYhzSFGhbetVzFEk6+a"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7dJI843oNDhq5aUwxbkNzn"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 30}, "_children": [{"__id__": 52}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "efoZ1GWTBAS5TRIZEXxdmQ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 51}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "8fuw/Q2fBDQLINe31Wu5WG"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "a3K4EcmdhM8Yr4R8JJoO6F"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 54}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "3dtMeV6z1OnK/aADPa0/Aa"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 55}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 55}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "11rxLLsOdM6K6dW3yRXnO2"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 56}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "8crN6kHmhDoLx/ZtJ+lGTF"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 57}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "86lNy4YvZAB6oX74F5AF4W"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 58}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "9dSa8KPD5F0LQAB2Si5Ilr"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 59}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "d5cLryYUBMpogSaHOwBejy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 60}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7aUgNdTwhPxaWdRPzHjEMm"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 61}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "97dTro/DxK95e7vhqQB3rq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 62}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "71UaZki5lHzZpKdbZMIbqJ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform", "_parent": {"__id__": 7}, "_children": [{"__id__": 64}, {"__id__": 70}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "12G6tRZlFHU5Uhiai/nWzt"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatformPrefab", "_parent": {"__id__": 63}, "_children": [{"__id__": 65}, {"__id__": 66}, {"__id__": 69}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 64}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 64}, "_offset": {"__type__": "cc.Vec2", "y": -10}, "_size": {"__type__": "cc.Size", "width": 116, "height": 20}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 64}, "msgId": 17, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "91XysO5bpHGqEseP+wwZN4"}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [220, 130, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform05", "_parent": {"__id__": 64}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "165meFshxKB7nkA02qR7/u"}}, {"__type__": "cc.Widget", "node": {"__id__": 65}, "_alignFlags": 8, "_left": -6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "58dHZ4k/lE7Kpz/GgvCULa"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-48, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 64}, "_children": [{"__id__": 67}, {"__id__": 68}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 66}, "_layoutSize": {"__type__": "cc.Size", "width": 64, "height": 32}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "10hO3DGy9Py7GXM9Y9m5DJ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform01", "_parent": {"__id__": 66}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "35z9XnZOJGzr70yZKNfr0J"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "52NPfM/f9JWZ5y5hwQk7AV"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-16, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform03", "_parent": {"__id__": 66}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 68}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "17xMH3uYNN1JpsAaIvJgr4"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "f6nWBp6MlIEJkCQxRxs2oB"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [16, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform04", "_parent": {"__id__": 64}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f2WnrNh2JOH6Bqc3nutMZi"}}, {"__type__": "cc.Widget", "node": {"__id__": 69}, "_alignFlags": 32, "_right": -6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "38sHRW2qtB/77yM7BUubFx"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [48, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform1", "_parent": {"__id__": 63}, "_children": [{"__id__": 71}, {"__id__": 72}, {"__id__": 75}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 70}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 70}, "_offset": {"__type__": "cc.Vec2", "y": -10}, "_size": {"__type__": "cc.Size", "width": 116, "height": 20}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 70}, "msgId": 17, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "70Cv6Enq1PMr1sbDMnMsJG"}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [220, 130, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform05", "_parent": {"__id__": 70}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 71}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "165meFshxKB7nkA02qR7/u"}}, {"__type__": "cc.Widget", "node": {"__id__": 71}, "_alignFlags": 8, "_left": -6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "baz52H/jdPP75FHZQ4zeT0"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-48, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 70}, "_children": [{"__id__": 73}, {"__id__": 74}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 72}, "_layoutSize": {"__type__": "cc.Size", "width": 64, "height": 32}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "55jC0C3nhCU6dUpHFKfe/R"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform01", "_parent": {"__id__": 72}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "35z9XnZOJGzr70yZKNfr0J"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "a26Oarhn9JXpFzDeGoJEeA"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-16, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform03", "_parent": {"__id__": 72}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 74}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "17xMH3uYNN1JpsAaIvJgr4"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "d39YefEkNE6q3bPjbFJ1ag"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [16, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform04", "_parent": {"__id__": 70}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 75}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f2WnrNh2JOH6Bqc3nutMZi"}}, {"__type__": "cc.Widget", "node": {"__id__": 75}, "_alignFlags": 32, "_right": -6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "d6XR1DprBJXoTpCzjel7kE"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [48, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeHTL", "_parent": {"__id__": 7}, "_children": [{"__id__": 77}, {"__id__": 78}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 76}, "_type": 1, "_allowSleep": false, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 76}, "tag": 1, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 90, "height": 100}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 76}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "b1YAVo6zhN3LEH75NG3APs"}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-150, 50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 76}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 77}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UI4W9U9AvrRJ/3cLJzPm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "98qdsoOVZK5Jai9MVC5ksR"}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 76}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 78}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "强化跳跃", "_N$string": "强化跳跃", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "b9tRVhoMVOdIgSoA6gOcyU"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 35.28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -35, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeFHL", "_parent": {"__id__": 7}, "_children": [{"__id__": 80}, {"__id__": 81}, {"__id__": 82}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 79}, "_type": 1, "_allowSleep": false, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 79}, "tag": 1, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 90, "height": 100}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 79}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "3d7Flt6mNKyqnjdiiv0jME"}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [150, 50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeFHL1Sprite", "_parent": {"__id__": 79}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 80}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "397H3lImBL1YPpbhOsPa5p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7aovStHz5DCLkm6S7CCjx2"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9, 13.723, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeFHL2Sprite", "_parent": {"__id__": 79}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 81}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "397H3lImBL1YPpbhOsPa5p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7fnVtwXMBCXLVbDoQFX5jY"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7.892, 5.688, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 79}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 82}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "解锁二连跳", "_N$string": "解锁二连跳", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "1co+xv3PNOm5LE+yaoHRE+"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 35.28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -35, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeHJQ", "_parent": {"__id__": 7}, "_children": [{"__id__": 84}, {"__id__": 88}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 83}, "_type": 1, "_allowSleep": false, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 83}, "tag": 1, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 90, "height": 100}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 83}, "_offset": {"__type__": "cc.Vec2", "x": 20, "y": 60}, "_size": {"__type__": "cc.Size", "width": 20, "height": 30}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 83}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "fclWpFGLpOCrVwxQ4zbe+S"}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeHJQSprite", "_parent": {"__id__": 83}, "_children": [{"__id__": 85}, {"__id__": 86}, {"__id__": 87}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "02KxIzSg9GCqMZ2CsS6e3M"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp1", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 85}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7faBiaa6RN7bqzIaANIuxA"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "0cSL9Z6TFD75F8Zv/ymWEb"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp2", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 86}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "bctgpcbPlJ9aY74gCA61JQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "b13BoCeKZMKbrkVhsqZv48"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.426, 14.668, 0, 0, 0, 0.7986355100472927, -0.6018150231520484, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 254}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp3", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 87}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "05Syhj6gxAXJviRrgbSOhI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "9emO2yuD5IRKOVnObBPltn"}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-11.547, -14.98, 0, 0, 0, 0.4617486132350339, 0.8870108331782217, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 55}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 83}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 88}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "生命数+2", "_N$string": "生命数+2", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "56H2lLQgZKg7WJ9e6D9vWV"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 99.36, "height": 35.28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -35, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeHTL2FHL", "_parent": {"__id__": 7}, "_children": [{"__id__": 90}, {"__id__": 91}, {"__id__": 92}, {"__id__": 93}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 89}, "_type": 1, "_allowSleep": false, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 89}, "tag": 1, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 90, "height": 100}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 89}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7aKHWDCh1OCpbreNdFjkek"}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-200, 250.63, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp1", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 90}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "397H3lImBL1YPpbhOsPa5p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "c1s7OCNypOgrmJIKDvz/SN"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9, 13.723, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp2", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 91}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "397H3lImBL1YPpbhOsPa5p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "e8+WRC2TJKhqtgIkIybJty"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7.892, 5.688, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 92}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UI4W9U9AvrRJ/3cLJzPm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "709ocIstVHubNnlUb80cdP"}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 93}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "强化跳跃\n解锁二连跳", "_N$string": "强化跳跃\n解锁二连跳", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "91ErMrcshGQ5pM3kU6j+4W"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 63.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeFHL2HJQ", "_parent": {"__id__": 7}, "_children": [{"__id__": 95}, {"__id__": 96}, {"__id__": 97}, {"__id__": 98}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 94}, "_type": 1, "_allowSleep": false, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 94}, "tag": 1, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 90, "height": 100}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 94}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "e6ohYbz6xHzIK87FwnfyJe"}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 250.63, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 94}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 95}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7faBiaa6RN7bqzIaANIuxA"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "484D+JMQ5B0YttYOlpiNAW"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12.662, 30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp1", "_parent": {"__id__": 94}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 96}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "397H3lImBL1YPpbhOsPa5p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "45W8bL6dpB94KeAg4Uld27"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23.772, 13.723, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp2", "_parent": {"__id__": 94}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 97}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "397H3lImBL1YPpbhOsPa5p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "5fEbxBBSVI35jq6fMhnUc7"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [6.88, 5.688, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 94}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 98}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "解锁二连跳\n生命数+2", "_N$string": "解锁二连跳\n生命数+2", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "e4tzzj+79EobvTePHb+PWL"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 63.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeHTL2HJQ", "_parent": {"__id__": 7}, "_children": [{"__id__": 100}, {"__id__": 101}, {"__id__": 102}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 99}, "_type": 1, "_allowSleep": false, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 99}, "tag": 1, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 90, "height": 100}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 99}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "cdKkwqrPNIMpzSNU9fAu1F"}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 250.63, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 99}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 100}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UI4W9U9AvrRJ/3cLJzPm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "f1UmGjn5RGLYybYaJrcmHi"}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 99}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 101}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7faBiaa6RN7bqzIaANIuxA"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "eb31pb7+xEG6HbjPQmu4Rs"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 99}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 102}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "强化跳跃\n生命数+2", "_N$string": "强化跳跃\n生命数+2", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "138Ra8pI9KQbM3oB8QXuMY"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 99.36, "height": 63.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeHTL2FHL2HJQ", "_parent": {"__id__": 7}, "_children": [{"__id__": 104}, {"__id__": 105}, {"__id__": 106}, {"__id__": 107}, {"__id__": 108}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 103}, "_type": 1, "_allowSleep": false, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 103}, "tag": 1, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 90, "height": 100}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 103}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "7asKXufQ5G15GY0+gXJHz0"}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 250.63, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp1", "_parent": {"__id__": 103}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 104}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "397H3lImBL1YPpbhOsPa5p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "66/PavXG5Dq67JSiicmOmB"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9, 13.723, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp2", "_parent": {"__id__": 103}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 105}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "397H3lImBL1YPpbhOsPa5p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "d5vmvJl+1KcauAiltNgIml"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7.892, 5.688, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 103}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 106}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UI4W9U9AvrRJ/3cLJzPm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "e1QnD91zdAEp3/bCwBF8CQ"}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "sp", "_parent": {"__id__": 103}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 107}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7faBiaa6RN7bqzIaANIuxA"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "c1jbv7QG5HRLHhLnicuXgU"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 103}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 108}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "强化跳跃\n解锁二连跳\n生命数+2", "_N$string": "强化跳跃\n解锁二连跳\n生命数+2", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "70PdQyBE5KWYd8Qwmv2hhX"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 91.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 22, "groupIndex": 22}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 7}, "_children": [{"__id__": 110}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 109}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 109}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 109}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "8eCjbVtYhJ/7+EeY+k6PFz"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-430, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 109}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 110}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "f7XNNWWadDC56UU/m0WVVI"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeCloth", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 111}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7fHUwSSmlEyK68+Tb28fCH"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "18UOx9FI1Iyol/ni2ONwiH"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 84}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeHTLSpine", "_parent": {"__id__": 7}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 112}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "0aesYwB3ZC0KDmL7LJqfB+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "adRugCgcxHILg6l8tuUn3B"}, "_contentSize": {"__type__": "cc.Size", "width": 105.07, "height": 74.01}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDead", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 113}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a8PJW1uK5EabX0dF6VvQU4"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 113}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 113}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 14}, "_size": {"__type__": "cc.Size", "width": 2560, "height": 20}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 113}, "msgId": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "6byNjP1bVNFpoy0vpX340l"}, "_contentSize": {"__type__": "cc.Size", "width": 2560, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBgBlack", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 114}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "3bxXOWSXFI+7EMPTToT9kn"}, "fileId": "0c/Q4egdRJIZ5SwUYsmPcK"}, "_opacity": 0, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "a437", "texture": "1d7eb79f9", "rect": [131, 907, 64, 84], "offset": [0, 1], "originalSize": [64, 86], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a435", "texture": "1d7eb79f9", "rect": [221, 424, 130, 60], "offset": [0, 0], "originalSize": [130, 60], "rotated": 1, "capInsets": [0, 0, 0, 0]}}]