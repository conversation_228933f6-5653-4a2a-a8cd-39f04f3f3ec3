[{"__type__": "cc.SpriteFrame", "content": {"name": "s89", "texture": "135189691", "rect": [3, 345, 120, 122], "offset": [0, 0], "originalSize": [120, 122], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "b12", "texture": "1cdc283e0", "rect": [447, 883, 104, 86], "offset": [0, 0], "originalSize": [104, 86], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "map5009", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map5007", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 20}, {"__id__": 54}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "art01", "_parent": {"__id__": 1}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fbav3ljDBAi6iz222wdO7v"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "150h0VyPxG0okkIa1MF3Sm"}, "_contentSize": {"__type__": "cc.Size", "width": 976, "height": 534}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 1}, "_children": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}, {"__id__": 12}, {"__id__": 14}, {"__id__": 16}, {"__id__": 18}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 3}, "_layoutSize": {"__type__": "cc.Size", "width": 304, "height": 150}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "a57PThde9FBKi3WfcRVWsd"}, "_contentSize": {"__type__": "cc.Size", "width": 304, "height": 150}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 152.053, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s70", "_parent": {"__id__": 3}, "_children": [{"__id__": 5}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5wzcYpiFLpaRL9I91Zeg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "80s6KF63lOb6uGkS5nZXHz"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "如", "_N$string": "如", "_fontSize": 30, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "07uXOi949E7YXiysfiGDRo"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1.974, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "s70 copy", "_parent": {"__id__": 3}, "_children": [{"__id__": 7}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5wzcYpiFLpaRL9I91Zeg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "39mqP998dMIZtOhdpYbgNq"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-95, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "何", "_N$string": "何", "_fontSize": 30, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "49tUNd8HRAtL/b+14BVAn5"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1.974, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "s70 copy", "_parent": {"__id__": 3}, "_children": [{"__id__": 9}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5wzcYpiFLpaRL9I91Zeg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "39dC9SryhOwLmJ2KhJP6sf"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-57, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "赢", "_N$string": "赢", "_fontSize": 30, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "57WSw/UyJGm7POMTaF7REO"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1.974, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "s70 copy", "_parent": {"__id__": 3}, "_children": [{"__id__": 11}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5wzcYpiFLpaRL9I91Zeg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "86ypN9J3FJV7Kui3yM5xrt"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-19, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "得", "_N$string": "得", "_fontSize": 30, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "cerqvE5m1FS64YIC5B+ba7"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1.974, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "s70 copy", "_parent": {"__id__": 3}, "_children": [{"__id__": 13}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5wzcYpiFLpaRL9I91Zeg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "84gtE1ikhG6rQiD/ZhqnAs"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [19, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "赛", "_N$string": "赛", "_fontSize": 30, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "d5OvmisYVGkqSrPi2fOEr8"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1.974, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "s70 copy", "_parent": {"__id__": 3}, "_children": [{"__id__": 15}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5wzcYpiFLpaRL9I91Zeg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "6e/HiyAyFBA7EZoErDe0Qg"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [57, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "马", "_N$string": "马", "_fontSize": 30, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "162MIr11xI2K0XV+FMLEBK"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1.974, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "s70 copy", "_parent": {"__id__": 3}, "_children": [{"__id__": 17}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5wzcYpiFLpaRL9I91Zeg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "5aJM2MvppB26U1b+YRqS66"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [95, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "比", "_N$string": "比", "_fontSize": 30, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "7bzDkA5flKYr2XYuweZjKI"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1.974, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "s70 copy", "_parent": {"__id__": 3}, "_children": [{"__id__": 19}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5wzcYpiFLpaRL9I91Zeg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "aalndICnxKOIEwczLmczRF"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [133, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "赛", "_N$string": "赛", "_fontSize": 30, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "1bJ0r3R7BNsokvdPTzpNul"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1.974, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 21}, {"__id__": 24}, {"__id__": 25}, {"__type__": "cc.Node", "_name": "CC_nodeBox1", "_parent": {"__id__": 20}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "7dh4pNC8ZLXrbLohNW+ADn"}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 144}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-364, 104.957, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__id__": 32}, {"__type__": "cc.Node", "_name": "CC_nodeBox2", "_parent": {"__id__": 20}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "2d0MKStLpKuo/muYt4WwNF"}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 144}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-364, -20, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__id__": 39}, {"__type__": "cc.Node", "_name": "CC_nodeBox3", "_parent": {"__id__": 20}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "44jMhg7KtLdYjwDoovlb3v"}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 144}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-364, -141.784, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeEggBox", "_parent": {"__id__": 20}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "afZsos1Y9ILIAzusDHiscI"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2.058, -161.986, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 46}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeSun", "_parent": {"__id__": 20}, "_children": [{"__id__": 22}, {"__id__": 23}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "6eQd+My6BEZ5b2kpNa3yCP"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [334.944, 74.427, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s90", "_parent": {"__id__": 21}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b7t8J9Ag9JWKTzOGu8kU+Z"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "e4D9y2MzVCy6OXJyoDDAUc"}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b12", "_parent": {"__id__": 21}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "274/TlquFJkZsURspiQcvB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "956F/ZMe1J6I1TX0u+HooY"}, "_contentSize": {"__type__": "cc.Size", "width": 108, "height": 98}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-40.524, 61.392, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s99", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "50D43GffNMYabWpA6zhq0o"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "20RabwFk9Ox5A9biT2ZzAS"}, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 214}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [214, -55.566, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeMa1", "_parent": {"__id__": 20}, "_children": [{"__id__": 26}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "79Py2chNdCKryUBAIgdI2p"}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 144}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-364, 104.957, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 25}, "_children": [{"__id__": 27}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 26}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal5", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "move", "_N$skeletonData": {"__uuid__": "82vTnN2SpGUqirlLm/m8MK"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "4cBkvtPDBIMqRQr0rJR9t/"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 164}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7, -30, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 26}, "_children": [{"__id__": 28}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "54EI6+0PFBwLZnk+GOCqfz"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 27}, "_children": [{"__id__": 29}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "a4DX/8IOBMoa8YKDXi4t21"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:my", "_parent": {"__id__": 28}, "_children": [{"__id__": 30}, {"__id__": 31}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "95p7vkssRHLqRT0O8BCZD7"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s86", "_parent": {"__id__": 29}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ea2iDYjFNLIZg0amdKrfU5"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "49MA1f6TVO/7I0xLwvqkwD"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, -0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "CC_nodeHeiMa1", "_parent": {"__id__": 29}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1UPLZhoZOGYCDZIM8XVDy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "8f/aOW1YBDd4TwKums4V3Q"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.269, -56.376, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, -0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "CC_nodeMa2", "_parent": {"__id__": 20}, "_children": [{"__id__": 33}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "599vtzJwFIv5FMUUsCiwX/"}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 144}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-364, -20, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 32}, "_children": [{"__id__": 34}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 33}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal5", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "move", "_N$skeletonData": {"__uuid__": "82vTnN2SpGUqirlLm/m8MK"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "92CyiWYRhPNbjaLKfMTsMc"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 164}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7, -30, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 33}, "_children": [{"__id__": 35}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "c8W2q/qq5LtKue5y+pIdjW"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 34}, "_children": [{"__id__": 36}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "7dMp61AyhKqbeplKoZbn9q"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:my", "_parent": {"__id__": 35}, "_children": [{"__id__": 37}, {"__id__": 38}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "39lbMVCPpN/ZGgRw+OPMZc"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s86", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ea2iDYjFNLIZg0amdKrfU5"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "eayRZl8MZG+YKmmIDaaFZ+"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, -0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "CC_nodeHeiMa2", "_parent": {"__id__": 36}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1UPLZhoZOGYCDZIM8XVDy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "099D1EuV1E4rAmrOv238Kl"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.269, -56.376, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, -0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "CC_nodeMa3", "_parent": {"__id__": 20}, "_children": [{"__id__": 40}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "23le9cnSRCQ7fbHkD54KQO"}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 144}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-364, -141.784, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 39}, "_children": [{"__id__": 41}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 40}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal5", "_preCacheMode": 0, "premultipliedAlpha": false, "timeScale": 0.5, "_animationName": "move", "_N$skeletonData": {"__uuid__": "82vTnN2SpGUqirlLm/m8MK"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "50i/+s0a9CrKUtzfmgeH23"}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 164}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7, -30, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 40}, "_children": [{"__id__": 42}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "6deWv+WyBM0aZYhuG4irAj"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 41}, "_children": [{"__id__": 43}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "94G96JWPVLFoDg7M2r+Bwq"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:my", "_parent": {"__id__": 42}, "_children": [{"__id__": 44}, {"__id__": 45}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "50n0NIyfVMZLWS/GIld+nx"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s86", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ea2iDYjFNLIZg0amdKrfU5"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "f1zf8DotdPGou1Vy0NW1KC"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, -0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "CC_nodeHeiMa3", "_parent": {"__id__": 43}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1UPLZhoZOGYCDZIM8XVDy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "300kKsO55OVrOg+3No49nc"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.269, -56.376, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, -0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "CC_nodeEgg", "_parent": {"__id__": 20}, "_children": [{"__id__": 47}, {"__id__": 48}, {"__id__": 50}, {"__id__": 53}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "43AK9KxX9K2JmO5rDa5Ezm"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 120}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2.058, -161.986, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeHeiPos", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1UPLZhoZOGYCDZIM8XVDy"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "77NtHNNU9MGrTPf37S8XHB"}, "_opacity": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-22, 23, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bai", "_parent": {"__id__": 46}, "_children": [{"__id__": 49}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "afstyFH9ZKzbHksGqds9pm"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s88", "_parent": {"__id__": 48}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "93u4xG2TlPP79JjmM0nqM0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "2aRnro/mlDuKI4mY+9FvZY"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 122}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "hei", "_parent": {"__id__": 46}, "_children": [{"__id__": 51}, {"__id__": 52}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "1cM+yrsptPq6J8ffa+vpJn"}, "_opacity": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s89", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "01BvIUnoBD2oEWLxXmqnVW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "c6nBJBt/pBTa5X7RlznImw"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 122}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s92", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0qw+EKtxPwoWYsm1WHtjL"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "a6XVl8M6tOwKkqXsOPruon"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 75}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [27.56, 24.249, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeHei", "_parent": {"__id__": 46}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1UPLZhoZOGYCDZIM8XVDy"}}, {"__type__": "cc.BlockInputEvents", "node": {"__id__": 53}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "6c/zX1HhpIJ7xri2lU1zHv"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-22, 23, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 1}, "_active": false, "_components": [{"__type__": "cc.Label", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "如何让小黑遵守规则", "_N$string": "如何让小黑遵守规则", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "47UKE8awxHCpvs9z43zMej"}, "fileId": "b44OYsbIlJKJO4KhyFS9GI"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [60.982, 249.172, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], {"__type__": "cc.SpriteFrame", "content": {"name": "s99", "texture": "135189691", "rect": [746, 297, 71, 214], "offset": [0, 0], "originalSize": [71, 214], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s88", "texture": "135189691", "rect": [105, 177, 74, 122], "offset": [0, 0], "originalSize": [74, 122], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s90", "texture": "135189691", "rect": [877, 620, 142, 69], "offset": [0, 0], "originalSize": [142, 69], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s94", "texture": "135189691", "rect": [372, 471, 74, 76], "offset": [0, 0], "originalSize": [74, 76], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s93", "texture": "135189691", "rect": [376, 367, 74, 75], "offset": [0, 0], "originalSize": [74, 75], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s95", "texture": "135189691", "rect": [290, 471, 76, 76], "offset": [0, 0], "originalSize": [76, 76], "capInsets": [0, 0, 0, 0]}}]