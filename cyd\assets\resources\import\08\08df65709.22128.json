[{"__type__": "cc.SpriteFrame", "content": {"name": "h743", "texture": "1d737216c", "rect": [559, 183, 88, 60], "offset": [0, 0], "originalSize": [88, 60], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map161towergoodAnim", "_duration": 1.3333333333333333, "wrapMode": 2, "curveData": {"paths": {"face/good": {"props": {"opacity": [{"frame": 0, "value": 255}]}}, "face/bad": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "light/bad": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "light/good": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.5, "value": 255}, {"frame": 0.6666666666666666, "value": 0}, {"frame": 1.3333333333333333, "value": 0}]}}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h747", "texture": "19bab4923", "rect": [3, 101, 542, 202], "offset": [0, 0], "originalSize": [542, 202], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h753", "texture": "19bab4923", "rect": [608, 3, 192, 408], "offset": [0, 0], "originalSize": [192, 408], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map161brokeAnim", "_duration": 1.0166666666666666, "curveData": {"paths": {"h742": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.05, "value": 0}, {"frame": 0.08333333333333333, "value": 255}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.21666666666666667, "value": 0}, {"frame": 0.55, "value": 0}, {"frame": 0.6, "value": 0}, {"frame": 0.6333333333333333, "value": 255}, {"frame": 0.7166666666666667, "value": 255}, {"frame": 0.7666666666666667, "value": 0}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0.7, "y": 0.7}}, {"frame": 0.05, "value": {"__type__": "cc.Vec2", "x": 0.7, "y": 0.7}}, {"frame": 0.21666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.55, "value": {"__type__": "cc.Vec2", "x": 0.7, "y": 0.7}}, {"frame": 0.6, "value": {"__type__": "cc.Vec2", "x": 0.7, "y": 0.7}}, {"frame": 0.7666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}, "h743": {"props": {"opacity": [{"frame": 0.3, "value": 0}, {"frame": 0.3333333333333333, "value": 255}, {"frame": 0.4166666666666667, "value": 255}, {"frame": 0.4666666666666667, "value": 0}, {"frame": 0.85, "value": 0}, {"frame": 0.8833333333333333, "value": 255}, {"frame": 0.9666666666666667, "value": 255}, {"frame": 1.0166666666666666, "value": 0}], "scale": [{"frame": 0.3, "value": {"__type__": "cc.Vec2", "x": 0.7, "y": 0.7}}, {"frame": 0.4666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.85, "value": {"__type__": "cc.Vec2", "x": 0.7, "y": 0.7}}, {"frame": 1.0166666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h748", "texture": "1d737216c", "rect": [387, 353, 174, 176], "offset": [0, 0], "originalSize": [174, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map161UavMoveAnim", "_duration": 4.333333333333333, "wrapMode": 2, "curveData": {"props": {"x": [{"frame": 0, "value": 250, "curve": "cubicIn"}, {"frame": 0.5, "value": 150}, {"frame": 1.3333333333333333, "value": -250, "curve": "cubicOut"}, {"frame": 1.8333333333333333, "value": -350}, {"frame": 2.1666666666666665, "value": -350, "curve": "cubicIn"}, {"frame": 2.6666666666666665, "value": -250}, {"frame": 3.5, "value": 150, "curve": "cubicOut"}, {"frame": 4, "value": 250}, {"frame": 4.333333333333333, "value": 250}], "scale": [{"frame": 1.8333333333333333, "value": {"__type__": "cc.Vec2", "x": 0.8, "y": 0.8}}, {"frame": 2.15, "value": {"__type__": "cc.Vec2", "x": 0.8, "y": 0.8}}, {"frame": 2.1666666666666665, "value": {"__type__": "cc.Vec2", "x": -0.8, "y": 0.8}}, {"frame": 4.316666666666666, "value": {"__type__": "cc.Vec2", "x": -0.8, "y": 0.8}}, {"frame": 4.333333333333333, "value": {"__type__": "cc.Vec2", "x": 0.8, "y": 0.8}}]}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h754", "texture": "19bab4923", "rect": [607, 201, 192, 408], "offset": [0, 0], "originalSize": [192, 408], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h757", "texture": "19bab4923", "rect": [551, 267, 40, 56], "offset": [0, 0], "originalSize": [40, 56], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "map161", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map161", "_children": [{"__id__": 2}, {"__id__": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonHuDong", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "cfzw4qvyBJNoCbFVe2+Oyo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "5bYSub4qpJUJXNn5Nqw1Yf"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 104}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [108.595, -777.542, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 4}, {"__id__": 5}, {"__type__": "cc.Node", "_name": "CC_nodeTop", "_parent": {"__id__": 3}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "b0M9YC80VM3aOvBVZBE+rm"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 20}, {"__id__": 48}, {"__id__": 49}, {"__id__": 55}, {"__id__": 57}, {"__id__": 59}, {"__id__": 67}, {"__id__": 68}, {"__id__": 71}, {"__id__": 88}, {"__id__": 111}, {"__id__": 156}, {"__id__": 157}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "76l87vgHNO7qh9KEYBSOup"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg1", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "c9GbDdw9tMWYPa3I+h8hTR"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 4608}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 1681.049, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg2", "_parent": {"__id__": 3}, "_children": [{"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "90Dx47Y3BOzrjPMDHHBksI"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 768, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b13", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "23ixnycwFMhYsganjBdbpo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "0dathdTgBDr4txYjhEFfo/"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-492.667, 90.398, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "c2VnLwXmtBuquqgGLCno5m"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [379.671, 164.975, 0, 0, 0, 0, 1, -0.8, 0.8, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "2dwIJ/XONJDLdyQoHTlZZL"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-228.253, 223.733, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "3c+p7QvDJA26z8QH3QA54E"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [285.529, 781.644, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b13", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "23ixnycwFMhYsganjBdbpo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "38A1tdsTNGiqAjdUjW31nN"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288.439, 1105.651, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "d2/XE6MT1ALpjk3mZ1r70E"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-445.744, 683.931, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "12trApWuZGIIazyqCbddj3"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [339.113, 1282.818, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "8fQlBtlxxFK5bMBAkJLQ3g"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [435.664, 1247.335, 0, 0, 0, 0, 1, -0.6, 0.6, 1]}}, {"__type__": "cc.Node", "_name": "b13", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "23ixnycwFMhYsganjBdbpo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "63YFKYIdJNE6kgt7F7r+te"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [360.881, 1685.625, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "42HuDOAANF4r83dYwNDEUM"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-389.007, 1761.927, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "e87iO5nhpHhbyrMuMhRkwh"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [80.646, 1828.12, 0, 0, 0, 0, 1, 1.1, 1.1, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "14fPgPfy9MjKe8lJPA/XKC"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [280.905, 2327.749, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b13", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "23ixnycwFMhYsganjBdbpo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "57/547i0JGhqAgFGPAduTP"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [359.837, 2585.918, 0, 0, 0, 0, 1, 0.9, 0.9, 1]}}, {"__type__": "cc.Node", "_name": "b14", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "84BdXwuudGlqrYEX1CHASC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "62dHWP1uVAzaFaVf5HqrI3"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-295.747, 2855.44, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Ground", "_parent": {"__id__": 3}, "_children": [{"__id__": 21}, {"__id__": 22}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 20}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 20}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 1280, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "30xsO5Vx1Bg7OyNoHyfYd0"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 21}, "_alignFlags": 45, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "44mvF4V7VOZ4HXEuO5ecPe"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 20}, "_children": [{"__id__": 23}, {"__id__": 25}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 30}, {"__id__": 31}, {"__id__": 32}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 39}, {"__id__": 40}, {"__id__": 42}, {"__id__": 43}, {"__id__": 44}, {"__id__": 45}, {"__id__": 46}, {"__id__": 47}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 22}, "_layoutSize": {"__type__": "cc.Size", "width": 1280, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "7aip44N4hB5JKV2ELpNQDZ"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 22}, "_children": [{"__id__": 24}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "29IwrUuadGzr1cErahpjOX"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "17E0FqMLJMIIBuYvBWR0Xf"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "fdCMqcr31LwrB87x4IztBj"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "130T46GKBDJ45ck7jYfYa8"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "b7YUPVtuNPbL2u4sp8EsW1"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 22}, "_children": [{"__id__": 29}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "1bRfF/+i9JD7XTcODKzhCE"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "99+/iL085Mf6l1r2EZgirC"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "56PDb5kV1LaZ0eSo6R967Z"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "51y54uy2BEq5VhwaokPFGG"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 22}, "_children": [{"__id__": 33}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "c17P9hjxNMKrnKDcJkpoZX"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "d1oDbwtr9EkZDRulyR3EQu"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, 28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "60u10AiddCvZPAjrsZmsQG"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "61tB/e+ZhJnoIjcCL03AfL"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "7ezJsuh7lFTZRVWf0/nSu1"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 22}, "_children": [{"__id__": 38}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "6aIHmc48FOyIh4iskrpV+Z"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 37}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "10Q1vyEm5HJZIiC8mYYssN"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.9759999999999991, 23.812, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "bcSJF187NPA6UqX2+ncuik"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 22}, "_children": [{"__id__": 41}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "a3ois4WWtCca189XuWr983"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little04", "_parent": {"__id__": 40}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78LL5TK2RJ/7QmPvmVHdJr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "eaaCM7N/1GNqfqLcpVFL2Y"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 7.955, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "a71NF3jhBNT4VQXAurmsMt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "7d47J6DLpOaa4UUn4qhvXw"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "cdspog+1dIdo2nSg4598mZ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "d0iDY7hgZGq4KwL0NUS0+b"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "bbKR5baFdOhYo64NiW2oQ/"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "b4CrYJ/N9L8oWmVYiKJWAh"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_labelTitle", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_string": "太空电梯", "_N$string": "太空电梯", "_enableWrapText": false, "_N$horizontalAlign": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "5dO4gZP7lJnIa59ee74gEt"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 140, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeUAV01", "_parent": {"__id__": 3}, "_children": [{"__id__": 50}, {"__id__": 51}, {"__id__": 52}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 49}, "_defaultClip": {"__uuid__": "42j34H6FlP+YySE1EhqUC9"}, "_clips": [{"__uuid__": "42j34H6FlP+YySE1EhqUC9"}, {"__uuid__": "d0hP+LZSxN/ISawt5SnOBj"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "9dHEPt1iJEjZXUtBWl7H0P"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [298.242, 102, 0, 0, 0, 0, 1, 0.8, 0.8, 0.8]}}, {"__type__": "cc.Node", "_name": "h748", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "38ZyHjfMxOVqidyv0IlV6O"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "a4MVcbp0RLG7v0ZRISvWy8"}, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "fire", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "71y+S5BeNEEZsAjesRNt2h"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "98SJ/kH+JL9IxKKYUSSvxi"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "face", "_parent": {"__id__": 49}, "_children": [{"__id__": 53}, {"__id__": 54}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "b3+bre4GtF1rvKq0Ueof00"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bad", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f3sVX6ScBJE4NavG6Rzg2+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "9cgAaV1O1EQ45UAUD5sUC5"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "good", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a5/wjSTjRMbZWHPLHDLzX0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "99fp5EJ0BI6K51yFXA+H7C"}, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "guanzi", "_parent": {"__id__": 3}, "_children": [{"__id__": 56}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "d6pNfRIMBN2qJpiaGp3fOb"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-146.65, -128, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f14", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "72wp88u4VLiKg+wFVW+AFE"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "697zP58qJGTrhMW73GpDce"}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 4096}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "guanzi", "_parent": {"__id__": 3}, "_children": [{"__id__": 58}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "db3XickP9HvIe3mrHqsBF9"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [144.831, -128, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f14", "_parent": {"__id__": 57}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "72wp88u4VLiKg+wFVW+AFE"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "d0lOwSR/BGQ59QuvulBHDA"}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 4096}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeXinHaoTa", "_parent": {"__id__": 3}, "_children": [{"__id__": 60}, {"__id__": 61}, {"__id__": 64}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 59}, "_defaultClip": {"__uuid__": "10bjrEuBBLWL+t5crK7wFx"}, "_clips": [{"__uuid__": "10bjrEuBBLWL+t5crK7wFx"}, {"__uuid__": "fclS24hBJOKYpmBLQN/jBM"}], "playOnLoad": true}, {"__type__": "cc.RigidBody", "node": {"__id__": 59}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 59}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": 24, "y": -170}, "_size": {"__type__": "cc.Size", "width": 50, "height": 100}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 59}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "b0+pOtr6JD56sZ5IJfCRZh"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [368.708, 73.132, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h752", "_parent": {"__id__": 59}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "cfzn+jRW1PbJP8yZ58XSE0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "1dI/4PfalIloZvtK3Dw5lM"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 408}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "light", "_parent": {"__id__": 59}, "_children": [{"__id__": 62}, {"__id__": 63}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "f7eKNq9elGGawLWdXo3NId"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "good", "_parent": {"__id__": 61}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f1FUpYjmZEn5jF4eLG0NxY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "f16Wz1+WhJkYxyxfl5+9h1"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 408}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bad", "_parent": {"__id__": 61}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 63}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a7uOWd4uRJ3Y7so6oYm1se"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "c0JjJzbM1KHLTL7bIPYJRJ"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 408}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "face", "_parent": {"__id__": 59}, "_children": [{"__id__": 65}, {"__id__": 66}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "de88AdhORCurVpk9NC6t6D"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "good", "_parent": {"__id__": 64}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "43xT+U5aBJ7LpkohkqFqwK"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "a6SSfG4TVHVYs+2Esp5LXV"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 408}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bad", "_parent": {"__id__": 64}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 66}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "259XD1chRL/palOBS+Yn8e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "c2kjNQFQ9JkJ2Ux6oypiBH"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 408}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeDianTiBg", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "25WeUd79pJeZNWC1RI7wUB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "6eoGwq7UZL3ZkW0zgxE7L+"}, "_contentSize": {"__type__": "cc.Size", "width": 542, "height": 202}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10.791, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLayer1", "_parent": {"__id__": 3}, "_children": [{"__id__": 69}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "74gPcrvqpHd5Feim+QKmUt"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 68}, "_children": [{"__id__": 70}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 69}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 69}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 69}, "aniNode": {"__id__": 69}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "4cfR8QJHlJLYtP2mZBR4Lh"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-440, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 69}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 70}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "bbkLptRzRHJJUlGk/RnY9Y"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeDianti", "_parent": {"__id__": 3}, "_children": [{"__id__": 72}, {"__id__": 73}, {"__id__": 74}, {"__id__": 75}, {"__id__": 78}, {"__id__": 85}, {"__type__": "cc.Node", "_name": "CC_nodeBadEff", "_parent": {"__id__": 71}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "e1+FyyDlhEq6J70xgwmEze"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [239, 59, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 71}, "_type": 0, "_allowSleep": false, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 71}, "_offset": {"__type__": "cc.Vec2", "y": -60}, "_size": {"__type__": "cc.Size", "width": 280, "height": 20}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 71}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 260, "height": 10}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 71}, "tag": 111, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 71}, "tag": 1, "_offset": {"__type__": "cc.Vec2", "x": -130}, "_size": {"__type__": "cc.Size", "width": 10, "height": 100}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 71}, "tag": 2, "_offset": {"__type__": "cc.Vec2", "x": 130}, "_size": {"__type__": "cc.Size", "width": 10, "height": 100}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 71}, "tag": 222, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 260, "height": 100}}, {"__type__": "d0aabggwVlIIplF8BRBIFLQ", "node": {"__id__": 71}, "msgIdIn": 7, "msgIdOut": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "5a9gOjp5RPTLFUBIqIK8pz"}, "_contentSize": {"__type__": "cc.Size", "width": 542, "height": 202}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "left", "_parent": {"__id__": 71}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 72}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6fYtkfV79D3K2JgaCVVmil"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "34OdGUenJO+pG618BZXxza"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.78, "y": 0.155}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-180.471, -73.221, 0, 0, 0, 0.7313537016191705, 0.6819983600624985, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 94}}, {"__type__": "cc.Node", "_name": "right", "_parent": {"__id__": 71}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6fYtkfV79D3K2JgaCVVmil"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "0cQnHoLLtElrXcQX6phjhS"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.78, "y": 0.155}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [180.471, -73.221, 0, 0, 0, -0.7313537016191705, 0.6819983600624985, -1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -94}}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 71}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 74}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "aeXFVP+85LqKjyHl+mZTow"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "32po9QjGpBububYNqW6dFY"}, "_contentSize": {"__type__": "cc.Size", "width": 542, "height": 202}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "broke", "_parent": {"__id__": 71}, "_children": [{"__id__": 76}, {"__id__": 77}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 75}, "_defaultClip": {"__uuid__": "298aGxE59GgrU76mm+6M3K"}, "_clips": [{"__uuid__": "298aGxE59GgrU76mm+6M3K"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "36Wzn6wppLoKY+oN4sMwdG"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [203.764, 95.025, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h742", "_parent": {"__id__": 75}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 76}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "79UdoqlkxB8YXGbWTe3OZw"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "9fuQKGmiNEfLRc2QYmLwQm"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.75, "y": 0.05}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h743", "_parent": {"__id__": 75}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 77}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "003k7OFVVMCosuVWQgTTSD"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "f9crGYrsxG/LU0/uO4+/qz"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.75, "y": 0.05}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDownFire", "_parent": {"__id__": 71}, "_children": [{"__id__": 79}, {"__id__": 82}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "c5tVI3EdlGWpe4uQw0wfe6"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 59.319, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "fire1", "_parent": {"__id__": 78}, "_children": [{"__id__": 80}, {"__id__": 81}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "5eMb72NnJJtYZ7gKFz6l1P"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-236.444, -84.305, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "h713", "_parent": {"__id__": 79}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 80}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ffBeT8z7tLKqky2CHp229B"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "81DxsOWihN2oF0kT6g6OE7"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "h714", "_parent": {"__id__": 79}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 81}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98YbwUR2tGIYaDbvKiOPr6"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "f7iN5K6slIgpw2t68J3IhJ"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "fire2", "_parent": {"__id__": 78}, "_children": [{"__id__": 83}, {"__id__": 84}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "7bDWzI5glCfaZJdue4TYiE"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [236.396, -84.305, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "h713", "_parent": {"__id__": 82}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 83}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ffBeT8z7tLKqky2CHp229B"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "28KcvHy2tEqaQTG9HTy1z0"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "h714", "_parent": {"__id__": 82}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 84}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98YbwUR2tGIYaDbvKiOPr6"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "e9yCbLFr1FYpSfTVRBpj+I"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 90}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeYanDown", "_parent": {"__id__": 71}, "_children": [{"__id__": 86}, {"__id__": 87}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "7d9EH57tREM7uAA0iXGpCF"}, "_opacity": 0, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [310.784, 41.438, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h691", "_parent": {"__id__": 85}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 86}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2dt3VuinNBpZRKzT4Jzhfm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "7bZGSpMcBARJzVsoRZKPBH"}, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 104}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h692", "_parent": {"__id__": 85}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 87}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdRr+gVPVA34azyZvbTjTw"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "e7V1+eED1Fk5D8DbOasKqS"}, "_contentSize": {"__type__": "cc.Size", "width": 164, "height": 104}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLayer", "_parent": {"__id__": 3}, "_children": [{"__id__": 89}, {"__id__": 95}, {"__id__": 97}, {"__id__": 99}, {"__id__": 101}, {"__id__": 104}, {"__id__": 107}, {"__id__": 109}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "64HaGVOItB/5mIz4RKQGLC"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeUAV", "_parent": {"__id__": 88}, "_children": [{"__id__": 90}, {"__id__": 91}, {"__id__": 92}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 89}, "_defaultClip": {"__uuid__": "f0xSSGiR1HMZSh2soQuusp"}, "_clips": [{"__uuid__": "f0xSSGiR1HMZSh2soQuusp"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "c1nXjYxMhMJoTCI3bz8KUu"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [323.54, -943.182, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h748", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 90}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "38ZyHjfMxOVqidyv0IlV6O"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "90yPtyi4BNA7EYhSwvzyv1"}, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "fire", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 91}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "71y+S5BeNEEZsAjesRNt2h"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "4aQy7Sz7NKAoQbn3yFdLe9"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "face", "_parent": {"__id__": 89}, "_children": [{"__id__": 93}, {"__id__": 94}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "08l8zkJs5GfJWJNJjFPCaG"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bad", "_parent": {"__id__": 92}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 93}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f3sVX6ScBJE4NavG6Rzg2+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "73HeeGwg9H9Z6KBY+hvavs"}, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "good", "_parent": {"__id__": 92}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 94}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a5/wjSTjRMbZWHPLHDLzX0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "d6c4xDtCFLe7lLYoQfn90U"}, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTouchJump", "_parent": {"__id__": 88}, "_children": [{"__id__": 96}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "6fuqUWUZhN5omU/egp+PVJ"}, "_contentSize": {"__type__": "cc.Size", "width": 146, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [373.477, -222.662, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a06", "_parent": {"__id__": 95}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 96}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "944x7TrvtIK6vu268rYsfi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "52Hcd/3mtMTbluJTIwBlwo"}, "_contentSize": {"__type__": "cc.Size", "width": 146, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTouchRight", "_parent": {"__id__": 88}, "_children": [{"__id__": 98}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "62NN84wtlCgJcHElYmET4T"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-149.573, -222.001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a05", "_parent": {"__id__": 97}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 98}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78HXp7GrZC8paPaf7VorCD"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "93vFfEpa1AbZ4qi/oMTCC3"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTouchLeft", "_parent": {"__id__": 88}, "_children": [{"__id__": 100}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "ffPKCQHfhFLo+9oUoifB0E"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-385.288, -224.001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a04", "_parent": {"__id__": 99}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 100}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "26boGrf5RBBZdJD+iM/Tnr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "62CBfONoFHpYA8hNWSXWJb"}, "_contentSize": {"__type__": "cc.Size", "width": 133, "height": 99}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLv", "_parent": {"__id__": 88}, "_children": [{"__id__": 102}, {"__id__": 103}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "8cBG8sThFGf7BKClB/XDC1"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-436.542, 236.689, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a07", "_parent": {"__id__": 101}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 102}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "30wQMzA3FPzamVq/nOHf8D"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "12QiJU3mVNX6DXoQ23qf9j"}, "_contentSize": {"__type__": "cc.Size", "width": 137, "height": 180}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelLv", "_parent": {"__id__": 101}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 103}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_string": "第 1 关", "_N$string": "第 1 关", "_fontSize": 26, "_lineHeight": 26, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "50AqSkF2tE7ZHXsUeN5Z4g"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 80.91, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTip", "_parent": {"__id__": 88}, "_children": [{"__id__": 105}, {"__id__": 106}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "5b3p4mpHxNv7Rc+zbiy5mb"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-315, 238, 219.97045, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a01", "_parent": {"__id__": 104}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 105}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d9aCmc+PRGmLKmtQb5azMR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "48Uxbz8CJOhaeYEjE3HtPu"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_spriteAdIconTip", "_parent": {"__id__": 104}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 106}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c2EBVSVIJIV4l2PxZy2VNP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "cfFLp1wGpNLahY9A+BRbyE"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-22.812, 23.633, -219.97045, 0, 0, 0, 1, 0.75, 0.75, 0.75]}}, {"__type__": "cc.Node", "_name": "CC_nodeRestart", "_parent": {"__id__": 88}, "_children": [{"__id__": 108}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "05UORgT/RK6ppLzJSlDyAR"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 238, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a01", "_parent": {"__id__": 107}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 108}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ebWm99k/VEFqleynufScfS"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "63Itg7EuRFAZ4F9BBA69We"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStop", "_parent": {"__id__": 88}, "_children": [{"__id__": 110}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "fbGhh24aNEn7Ch4VXCqvkc"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-135, 238, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a01", "_parent": {"__id__": 109}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 110}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "abW1hjPyZF2rWBqEVtTr4/"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "9572hYA01AoaStgo1o4oc/"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePassWardDialog", "_parent": {"__id__": 3}, "_children": [{"__id__": 112}, {"__id__": 113}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "87ztPEP95C75YdH/WIUwAj"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg1", "_parent": {"__id__": 111}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 112}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "07Y3LamaBDm6Kbf3aPsOEU"}, "_opacity": 70, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 111}, "_children": [{"__id__": 114}, {"__id__": 115}, {"__id__": 116}, {"__id__": 119}, {"__id__": 153}, {"__id__": 155}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "8aeFMa7/hAVLBVl8aa+jUv"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b01", "_parent": {"__id__": 113}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 114}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48cRGUNsVI3rXXz2xKNwYu"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "4cAsTebshHM5yRh4jTO4bI"}, "_contentSize": {"__type__": "cc.Size", "width": 376, "height": 499}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b02", "_parent": {"__id__": 113}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 115}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e6cgGuqC5K1qCTAXK1aVOq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "8fuy1y+RNK5q2Oq81Z3Gd/"}, "_contentSize": {"__type__": "cc.Size", "width": 295, "height": 88}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3, 155, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLayout", "_parent": {"__id__": 113}, "_children": [{"__id__": 117}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 116}, "_layoutSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_resize": 1, "_N$layoutType": 1, "_N$spacingX": 45}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "a5IFuGh/dDbZceuHWHrpPf"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, 158, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeItem", "_parent": {"__id__": 116}, "_children": [{"__id__": 118}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "02Za9GIQVArb6UrMudGtbd"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 117}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 118}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "9", "_N$string": "9", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "8c5KbvnzhHyqfU3Qf7ZW9L"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePassWardItems", "_parent": {"__id__": 113}, "_children": [{"__id__": 120}, {"__id__": 123}, {"__id__": 126}, {"__id__": 129}, {"__id__": 132}, {"__id__": 135}, {"__id__": 138}, {"__id__": 141}, {"__id__": 144}, {"__id__": 147}, {"__id__": 150}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 119}, "_layoutSize": {"__type__": "cc.Size", "width": 260, "height": 304}, "_resize": 1, "_N$layoutType": 3, "_N$spacingX": 10, "_N$spacingY": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "67DOhONrdDkohsnTqrm56X"}, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 304}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -54, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node1", "_parent": {"__id__": 119}, "_children": [{"__id__": 121}, {"__id__": 122}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 120}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 121}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "94rzGZkqtG974H950ptF1U"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-90, 114, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 120}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 121}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "27kM6Mr/NDcIRxE2UeuPcl"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 120}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 122}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "1", "_N$string": "1", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "01Gz059+dCgINoovu/C7XN"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node2", "_parent": {"__id__": 119}, "_children": [{"__id__": 124}, {"__id__": 125}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 123}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 124}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "a6XpYcRIVNKoLGHJHNy9Yw"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 123}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 124}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "86EHOu+XpMpqfkK9NghnOO"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 123}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 125}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "2", "_N$string": "2", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "7dRFRZW2NCuYeoilh4errg"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node3", "_parent": {"__id__": 119}, "_children": [{"__id__": 127}, {"__id__": 128}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 126}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 127}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "9dLn5fa25GpqsDYdtc9V+O"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [90, 114, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 126}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 127}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "603hzTMYxKSpNPSjELoOu/"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 126}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 128}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "3", "_N$string": "3", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "c0cu7IZx9EHqpgxNeMWUPa"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node4", "_parent": {"__id__": 119}, "_children": [{"__id__": 130}, {"__id__": 131}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 129}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 130}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "e4SRykQ6xDLaQSQihAot3i"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-90, 36, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 129}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 130}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "f4L3EcgQNLaJovQwTUq25v"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 129}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 131}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "4", "_N$string": "4", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "afOokYkrpH3puP1OF4QgC7"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node5", "_parent": {"__id__": 119}, "_children": [{"__id__": 133}, {"__id__": 134}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 132}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 133}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "fbvJ9U4v9BSI8GlCm1FbPe"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 36, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 132}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 133}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "faoHGwaHVMFrikBpOxh4tv"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 132}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 134}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "5", "_N$string": "5", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "0fTRg0/eJAaoZOCiPoum1Z"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node6", "_parent": {"__id__": 119}, "_children": [{"__id__": 136}, {"__id__": 137}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 135}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 136}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "dbb4AhAdZJHKjfHg7h/VaB"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [90, 36, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 135}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 136}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "f9r9I9BeJDNq/U/4YAOMew"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 135}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 137}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "6", "_N$string": "6", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "346abPxURBfZJZN2gU774e"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node7", "_parent": {"__id__": 119}, "_children": [{"__id__": 139}, {"__id__": 140}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 138}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 139}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "absRTYSbRFXZAYEf39gQZM"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-90, -42, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 138}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 139}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "13o7/LfM5Mqqi8RlhnYqjO"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 138}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 140}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "7", "_N$string": "7", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "afMy3GNztPIJAHwVv6D0Kn"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node8", "_parent": {"__id__": 119}, "_children": [{"__id__": 142}, {"__id__": 143}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 141}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 142}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "a9q+eZyQpO4qIt6Im0w/mm"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -42, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 141}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 142}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "60597gq/1LHKJmzHWjpj6y"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 141}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 143}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "8", "_N$string": "8", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "18qFI3Zf5NhaB+gI7rHbgG"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node9", "_parent": {"__id__": 119}, "_children": [{"__id__": 145}, {"__id__": 146}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 144}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 145}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "4aXmDX9ntItaVT7CEkU1Mo"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [90, -42, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 144}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 145}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aIVU6FzBGGYViI6B7wOar"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "0cTAKfHIZD3bjfeaX7DQj2"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 144}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 146}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "9", "_N$string": "9", "_fontSize": 38, "_lineHeight": 38, "_N$file": {"__uuid__": "dbafkwk9xLI7gHL88Te/A4"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "b7EDLqLJlIEqLrw0RQY3M9"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node0", "_parent": {"__id__": 119}, "_children": [{"__id__": 148}, {"__id__": 149}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 147}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 148}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "0eb5fzhlhNJrm1wbGe12PK"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-68, -117, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 147}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 148}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UluCm4dBo42BBMDb/rwX"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "d2evXsGFhMmaP7mKMncA9T"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeRole00", "_parent": {"__id__": 147}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 149}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "46W0UsMZpPpZB31sqwldjI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "50DQIuBIZM2INUFKOekkjj"}, "_opacity": 0, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 56}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDelete", "_parent": {"__id__": 119}, "_children": [{"__id__": 151}, {"__id__": 152}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 150}, "_N$transition": 1, "transition": 1, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "pressedSprite": {"__uuid__": "e97GVMl6JHh5Ml5qEDdSGa"}, "_N$hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "hoverSprite": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_N$disabledSprite": {"__uuid__": "29FYIk+N1GYaeWH/q1NxQO"}, "_N$target": {"__id__": 151}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "d4zbv4JxlC+JF8sIgI8cm+"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [66, -117, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 150}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 151}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90UluCm4dBo42BBMDb/rwX"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "85H/7Wh+JJ94iG54j7V+zF"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b05", "_parent": {"__id__": 150}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 152}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aNI+jpttAx6uyONTMM5LM"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "de5rOodZNCA4xf7Yx+Kwk9"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 113}, "_children": [{"__id__": 154}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 153}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "72ufwhbeBOoatsvcDSlEih"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 221, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b06", "_parent": {"__id__": 153}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 154}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48+6YuBptMr4rHwXDDBRsQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "ac6NBd8tZB1J18WglI6GTi"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h744", "_parent": {"__id__": 113}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 155}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "43vsui149I6K3SJn6167Na"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "531nR9gglLY5lcSmI2LtnP"}, "_contentSize": {"__type__": "cc.Size", "width": 206, "height": 194}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [213.562, -8.136, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLayer2", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "CC_nodeBox0", "_parent": {"__id__": 156}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "58AniL4AlKoLP/mJu875r8"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-68, -117, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "ebKJ+i2H1LCr+w+81IrUKH"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 157}, "_type": 0}, {"__type__": "cc.PhysicsPolygonCollider", "node": {"__id__": 157}, "points": [{"__type__": "cc.Vec2", "x": -280, "y": -17}, {"__type__": "cc.Vec2", "x": 280, "y": -17}, {"__type__": "cc.Vec2", "x": 140, "y": 50}, {"__type__": "cc.Vec2", "x": -140, "y": 50}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69B6Tt201Jsa4pj28A6xke"}, "fileId": "34DDsuKbNJyJPHv+zSMYlS"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -105.352, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], {"__type__": "cc.SpriteFrame", "content": {"name": "h745", "texture": "19bab4923", "rect": [551, 101, 50, 96], "offset": [0, 0], "originalSize": [50, 96], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h751", "texture": "1d737216c", "rect": [559, 3, 174, 176], "offset": [0, 0], "originalSize": [174, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h742", "texture": "1d737216c", "rect": [519, 249, 88, 60], "offset": [0, 0], "originalSize": [88, 60], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h750", "texture": "1d737216c", "rect": [101, 485, 174, 176], "offset": [0, 0], "originalSize": [174, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h755", "texture": "19bab4923", "rect": [551, 399, 192, 408], "offset": [0, 0], "originalSize": [192, 408], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h746", "texture": "19bab4923", "rect": [3, 309, 542, 202], "offset": [0, 0], "originalSize": [542, 202], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h752", "texture": "19bab4923", "rect": [3, 597, 192, 408], "offset": [0, 0], "originalSize": [192, 408], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map161UavMoveAtkAnim", "_duration": 4.416666666666667, "wrapMode": 2, "curveData": {"props": {"x": [{"frame": 0, "value": 250, "curve": "cubicIn"}, {"frame": 0.5, "value": 150}, {"frame": 1.3333333333333333, "value": -250, "curve": "cubicOut"}, {"frame": 1.8333333333333333, "value": -350}, {"frame": 2.1666666666666665, "value": -350, "curve": "cubicIn"}, {"frame": 2.6666666666666665, "value": -250}, {"frame": 3.5, "value": 150, "curve": "cubicOut"}, {"frame": 4, "value": 250}, {"frame": 4.416666666666667, "value": 250}], "scale": [{"frame": 1.8333333333333333, "value": {"__type__": "cc.Vec2", "x": 0.8, "y": 0.8}}, {"frame": 2.15, "value": {"__type__": "cc.Vec2", "x": 0.8, "y": 0.8}}, {"frame": 2.1666666666666665, "value": {"__type__": "cc.Vec2", "x": -0.8, "y": 0.8}}, {"frame": 4.4, "value": {"__type__": "cc.Vec2", "x": -0.8, "y": 0.8}}, {"frame": 4.416666666666667, "value": {"__type__": "cc.Vec2", "x": 0.8, "y": 0.8}}]}, "paths": {"face/bad": {"props": {"opacity": [{"frame": 0, "value": 255}]}}, "face/good": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "fire": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.15, "value": 255}, {"frame": 0.16666666666666666, "value": 0}, {"frame": 0.25, "value": 0}, {"frame": 0.31666666666666665, "value": 255}, {"frame": 0.4, "value": 255}, {"frame": 0.4166666666666667, "value": 0}, {"frame": 0.5, "value": 0}, {"frame": 0.5666666666666667, "value": 255}, {"frame": 0.65, "value": 255}, {"frame": 0.6666666666666666, "value": 0}, {"frame": 0.75, "value": 0}, {"frame": 0.8166666666666667, "value": 255}, {"frame": 0.9, "value": 255}, {"frame": 0.9166666666666666, "value": 0}, {"frame": 1, "value": 0}, {"frame": 1.0666666666666667, "value": 255}, {"frame": 1.15, "value": 255}, {"frame": 1.1666666666666667, "value": 0}, {"frame": 1.25, "value": 0}, {"frame": 1.3166666666666667, "value": 255}, {"frame": 1.4, "value": 255}, {"frame": 1.4166666666666667, "value": 0}, {"frame": 1.5, "value": 0}, {"frame": 1.5666666666666667, "value": 255}, {"frame": 1.65, "value": 255}, {"frame": 1.6666666666666667, "value": 0}, {"frame": 1.75, "value": 0}, {"frame": 1.8166666666666667, "value": 255}, {"frame": 1.9, "value": 255}, {"frame": 1.9166666666666667, "value": 0}, {"frame": 2, "value": 0}, {"frame": 2.066666666666667, "value": 255}, {"frame": 2.15, "value": 255}, {"frame": 2.1666666666666665, "value": 0}, {"frame": 2.25, "value": 0}, {"frame": 2.316666666666667, "value": 255}, {"frame": 2.4, "value": 255}, {"frame": 2.4166666666666665, "value": 0}, {"frame": 2.5, "value": 0}, {"frame": 2.566666666666667, "value": 255}, {"frame": 2.65, "value": 255}, {"frame": 2.6666666666666665, "value": 0}, {"frame": 2.75, "value": 0}, {"frame": 2.816666666666667, "value": 255}, {"frame": 2.9, "value": 255}, {"frame": 2.9166666666666665, "value": 0}, {"frame": 3, "value": 0}, {"frame": 3.066666666666667, "value": 255}, {"frame": 3.15, "value": 255}, {"frame": 3.1666666666666665, "value": 0}, {"frame": 3.25, "value": 0}, {"frame": 3.316666666666667, "value": 255}, {"frame": 3.4, "value": 255}, {"frame": 3.4166666666666665, "value": 0}, {"frame": 3.5, "value": 0}, {"frame": 3.566666666666667, "value": 255}, {"frame": 3.65, "value": 255}, {"frame": 3.6666666666666665, "value": 0}, {"frame": 3.75, "value": 0}, {"frame": 3.816666666666667, "value": 255}, {"frame": 3.9, "value": 255}, {"frame": 3.9166666666666665, "value": 0}, {"frame": 4, "value": 0}, {"frame": 4.066666666666666, "value": 255}, {"frame": 4.15, "value": 255}, {"frame": 4.166666666666667, "value": 0}, {"frame": 4.25, "value": 0}, {"frame": 4.316666666666666, "value": 255}, {"frame": 4.4, "value": 255}, {"frame": 4.416666666666667, "value": 0}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.25, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.4166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.5, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.6666666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.75, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.9166666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 1, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 1.1666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 1.25, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 1.4166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 1.5, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 1.6666666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 1.75, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 1.9166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 2, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 2.1666666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 2.25, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 2.4166666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 2.5, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 2.6666666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 2.75, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 2.9166666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 3, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 3.1666666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 3.25, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 3.4166666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 3.5, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 3.6666666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 3.75, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 3.9166666666666665, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 4, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 4.166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 4.25, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 4.416666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h758", "texture": "19bab4923", "rect": [879, 711, 295, 88], "offset": [0, 0], "originalSize": [295, 88], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map161UavAtkAnim", "_duration": 0.9166666666666666, "wrapMode": 0, "curveData": {"paths": {"fire": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.06666666666666667, "value": 255}, {"frame": 0.15, "value": 255}, {"frame": 0.16666666666666666, "value": 0}, {"frame": 0.25, "value": 0}, {"frame": 0.31666666666666665, "value": 255}, {"frame": 0.4, "value": 255}, {"frame": 0.4166666666666667, "value": 0}, {"frame": 0.5, "value": 0}, {"frame": 0.5666666666666667, "value": 255}, {"frame": 0.65, "value": 255}, {"frame": 0.6666666666666666, "value": 0}, {"frame": 0.75, "value": 0}, {"frame": 0.8166666666666667, "value": 255}, {"frame": 0.9, "value": 255}, {"frame": 0.9166666666666666, "value": 0}], "scale": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.16666666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.25, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.4166666666666667, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.5, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.6666666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}, {"frame": 0.75, "value": {"__type__": "cc.Vec2", "x": 0.9, "y": 0.9}}, {"frame": 0.9166666666666666, "value": {"__type__": "cc.Vec2", "x": 1, "y": 1}}]}}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h756", "texture": "19bab4923", "rect": [681, 597, 192, 408], "offset": [0, 0], "originalSize": [192, 408], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h749", "texture": "1d737216c", "rect": [353, 535, 174, 176], "offset": [0, 0], "originalSize": [174, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map161towerbadAnim", "_duration": 0.5833333333333334, "wrapMode": 2, "curveData": {"paths": {"face/good": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "light/good": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "face/bad": {"props": {"opacity": [{"frame": 0, "value": 255}]}}, "light/bad": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 0.3333333333333333, "value": 255}, {"frame": 0.5, "value": 0}, {"frame": 0.5833333333333334, "value": 0}]}}}}}]