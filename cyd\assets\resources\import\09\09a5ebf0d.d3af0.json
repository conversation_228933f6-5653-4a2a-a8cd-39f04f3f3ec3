[[{"__type__": "cc.Prefab", "_name": "map122", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map122", "_children": [{"__type__": "cc.Node", "_name": "CC_nodeBg", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "d8z9eM54dMobB3dylBan1u"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 2}, {"__id__": 90}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 40}, {"__id__": 43}, {"__id__": 51}, {"__id__": 53}, {"__id__": 55}, {"__id__": 58}, {"__id__": 62}, {"__id__": 68}, {"__id__": 70}, {"__id__": 74}, {"__type__": "cc.Node", "_name": "CC_nodeTop", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "22plyH09VPAKbFv82Ih4Te"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.789, -122.554, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 76}, {"__id__": 83}, {"__id__": 84}, {"__id__": 87}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 700}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "groud", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 31}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 39}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 3}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 3}, "_offset": {"__type__": "cc.Vec2", "y": -96}, "_size": {"__type__": "cc.Size", "width": 1280, "height": 192}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "8eyKGFiTZEv7NAOl2iDc9B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 4}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "16tk/7mf1BuqFADcVSDn01"}, "_contentSize": {"__type__": "cc.Size", "width": 1152, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 5}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "f44tgvdC1ObZTGe6FLgCI2"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 3}, "_children": [{"__id__": 7}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 23}, {"__id__": 24}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 30}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 6}, "_layoutSize": {"__type__": "cc.Size", "width": 1152, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "ddc2EmCTlCdbmXCVzdFw96"}, "_contentSize": {"__type__": "cc.Size", "width": 1152, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 6}, "_children": [{"__id__": 8}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "a4/DiqnilC6bAASJhGu+Lj"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "9cUnxHX2RA2LP5/LfwMNRB"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "9bV/rW0adJ7Zv3rUBl4FYG"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "55IwJKFitHeYKaFXEGVdRs"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "6dIgiTfstB/655YLphn5Oq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 6}, "_children": [{"__id__": 13}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "25XbyqI49EVpDqcLNlRTCt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "822pi2V6VEL4SX+6Y3qKdN"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "cc4zERolJLApkvW+f5OreS"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "507bNpuIhJwp2VmmDwSQn0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_children": [{"__id__": 17}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "9ayMfbLqxIe7OMufeq0Qph"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "1fUcNcLpNMmZe4VL802LFW"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-131.51, 35.054, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "1cw6ZnHMZGnapJ8pgd3lh0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "e29WT4ZspAVrkd7QVFne0Q"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "b2OCzpHUtHP4mKG56sBDqa"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_children": [{"__id__": 22}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "5eTM/2hnZAUZ/JpdNgb5o8"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 21}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "4fHhAk7NpLGaltywPJALMP"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.9759999999999991, 23.812, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "07jAfxXPhKLasQQiQwgOS4"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_children": [{"__id__": 25}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "f6ExGXl0ZHe6EWSWI0omOO"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little04", "_parent": {"__id__": 24}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78LL5TK2RJ/7QmPvmVHdJr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "9fxZubRThBIJl9VveBBBIA"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 7.955, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "9a6FeTCnlOE7pFa2UqUL1U"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "93MqzrPMdC37TjMB1twLbo"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 6}, "_children": [{"__id__": 29}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "6dtKe+5kZD8r/L4tVO+xLk"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "5dWgdMlP9KeoRqZxbkNwID"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "84lu1CFcdG/KR0XhsNIF1s"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 31}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "95nZXSvghBgYlX5a47h/2j"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 32}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "2bci3i3odM6aJJ0WmUvrb+"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 33}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "1as5rV+SxBYIknWzrjkHU8"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06 copy", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 34}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "98o58eea9Kca19jX+woUyr"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06 copy", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 35}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "3eglDeAZdHGZ5AfwhdACba"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 36}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "a9X5nYo69LmKK9kAdgNUKL"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 37}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "22c4t9gwpMtpGwjUNgbDEy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08 copy", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 38}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "56BpoAYbpKqJpn7/FNiyy6"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08 copy", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 39}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "02Ib1L/rlKWpQehJ6el4n3"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleLight", "_parent": {"__id__": 2}, "_children": [{"__id__": 41}, {"__id__": 42}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "07cN6jXy9B9aNLa6fUz0Z5"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 927.554, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleFirst", "_parent": {"__id__": 40}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "15n4LWQ1FFJIGG8tP+/+C9"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "9eUESMi8dGFJXNC9stu8up"}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 152}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.52, "y": 0.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleTwo", "_parent": {"__id__": 40}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b4rZ4wLhdAppRhmaJN5fpx"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "44plCLW41KVoU8zQilIPlm"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLight", "_parent": {"__id__": 2}, "_children": [{"__id__": 44}, {"__id__": 45}, {"__id__": 49}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 43}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 43}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": 1.2, "y": -25.7}, "_size": {"__type__": "cc.Size", "width": 107.8, "height": 81.3}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 43}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "d5UeBcw7JLV7K9DvSnh2x0"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 247.421, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLighting", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a6uYmfCFhHCpVkNOZGbI2Z"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "ccHa/D75RJzY6oa49oM7h+"}, "_opacity": 180, "_contentSize": {"__type__": "cc.Size", "width": 184, "height": 123}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.439, -62.849, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBulb", "_parent": {"__id__": 43}, "_children": [{"__id__": 46}, {"__id__": 47}, {"__id__": 48}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "68sje/foJF5qa01hGrBKDS"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -39.715, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h210", "_parent": {"__id__": 45}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6aopnricxIj6JpYIpdkkUG"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "e33zVPu8tCf5tLwVGWaNMb"}, "_contentSize": {"__type__": "cc.Size", "width": 52, "height": 72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h216", "_parent": {"__id__": 45}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "4bnjPwJZhD1qm9tdGgfGXy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "ca5mR0p95DCZ1StyAiEVMH"}, "_contentSize": {"__type__": "cc.Size", "width": 103, "height": 102}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h145", "_parent": {"__id__": 45}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5aKTjGiVdDgLO5eYYoI2Dr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "0a1D2Q1NlPt7JWZ847Gcv5"}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 96}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.825, -4.514, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 45}}, {"__type__": "cc.Node", "_name": "210", "_parent": {"__id__": 43}, "_children": [{"__id__": 50}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "acxcTFwSJMXIzoEDgP2Caw"}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 142}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -21.02, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h211", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5a3NG0Xt9Df4j9q/DrR7wh"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "cfyXkIO6xOE7nLxinpDlU6"}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 1000}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 427, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonOpen", "_parent": {"__id__": 2}, "_children": [{"__id__": 52}], "_components": [{"__type__": "cc.<PERSON><PERSON>", "node": {"__id__": 51}, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "cfzw4qvyBJNoCbFVe2+Oyo"}, "_N$pressedSprite": {"__uuid__": "cfzw4qvyBJNoCbFVe2+Oyo"}, "pressedSprite": {"__uuid__": "cfzw4qvyBJNoCbFVe2+Oyo"}, "_N$hoverSprite": {"__uuid__": "cfzw4qvyBJNoCbFVe2+Oyo"}, "hoverSprite": {"__uuid__": "cfzw4qvyBJNoCbFVe2+Oyo"}, "_N$disabledSprite": {"__uuid__": "cfzw4qvyBJNoCbFVe2+Oyo"}, "_N$target": {"__id__": 52}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "61nBkV1VRHEICYDAVlDCUZ"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 104}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [153.82, -232.807, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 51}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "cfzw4qvyBJNoCbFVe2+Oyo"}, "_type": 1, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 52}, "alignMode": 0, "_alignFlags": 45, "_originalWidth": 100, "_originalHeight": 40}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "20jKTFFeBCfIJ+iFdGtDOo"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 104}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "nodeDoorFalse", "_parent": {"__id__": 2}, "_children": [{"__id__": 54}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 53}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 53}, "_offset": {"__type__": "cc.Vec2", "y": 62}, "_size": {"__type__": "cc.Size", "width": 100, "height": 124}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "83ZgAgTVtL54lV6Um0F00d"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-458.081, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeMenSpine", "_parent": {"__id__": 53}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 54}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "ab053xehcJIX48UHF40+IAw", "node": {"__id__": 54}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "13PqcKYdBLbJ6qLrNwzm1I"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeKaiGuan", "_parent": {"__id__": 2}, "_children": [{"__id__": 56}, {"__id__": 57}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 55}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 55}, "_offset": {"__type__": "cc.Vec2", "x": -1.5, "y": 17.3}, "_size": {"__type__": "cc.Size", "width": 78.8, "height": 44.9}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "03DyM9tedJ4brIdWj6Famt"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 86}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-231.72, 93.277, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "f146", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b5KOUFafJNlYARhf/lTf2i"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "d76abWxKRHT5peo+7Ke3RD"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 86}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeSwitchSprite", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "14aWMeMdxCrIt2lOTNPTfa"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "38FZikXbVJVrrVWhp4/52S"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 86}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBed", "_parent": {"__id__": 2}, "_children": [{"__type__": "cc.Node", "_name": "CC_nodeBedRoot", "_parent": {"__id__": 58}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "98XhTBuRVGYIIWI6tGXVZH"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-11, -59, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eb/bHcHOlMXYV/ilsHg7Ty"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 58}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 58}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": 13, "y": -3}, "_size": {"__type__": "cc.Size", "width": 138.6, "height": 121.9}}, {"__type__": "d0aabggwVlIIplF8BRBIFLQ", "node": {"__id__": 58}, "msgIdIn": 7, "msgIdOut": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "8dYkq37g1Et4SJKvsA7m+U"}, "_contentSize": {"__type__": "cc.Size", "width": 190, "height": 128}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [443.908, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "f157", "_parent": {"__id__": 58}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6f/Xv6a7FJo53Ks1FGyRF8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "0fZw2CG7FE1rKv9lcPq/hc"}, "_contentSize": {"__type__": "cc.Size", "width": 190, "height": 128}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "nodeQuiltSmall", "_parent": {"__id__": 58}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77/YBqcWJDZKnxNvxquup1"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "50wDF+4ehI5IQImQpyKKsP"}, "_contentSize": {"__type__": "cc.Size", "width": 190, "height": 128}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeSleep", "_parent": {"__id__": 58}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f4Rh9Hk3tPeoFbktE78eGc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "ba4AeB6z5FSplhQfKStyGS"}, "_contentSize": {"__type__": "cc.Size", "width": 190, "height": 128}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.005, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBox", "_parent": {"__id__": 2}, "_children": [{"__id__": 63}, {"__id__": 66}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8f2JFktwlIJL7KmyAFmdGl"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 62}, "_type": 0, "_gravityScale": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 62}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": -1.1, "y": 69}, "_size": {"__type__": "cc.Size", "width": 54.3, "height": 138}}, {"__type__": "d0aabggwVlIIplF8BRBIFLQ", "node": {"__id__": 62}, "msgIdIn": 7, "msgIdOut": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "d1gbmqjU1LZYpOmZj3i6IZ"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [125.261, -124, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBoxOpen", "_parent": {"__id__": 62}, "_children": [{"__id__": 64}, {"__id__": 65}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "77eM4CS/JDvqRMMb2ZF5W3"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h51", "_parent": {"__id__": 63}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 64}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5d7dT+4HtOcaOddCf0tCqZ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "1c8+RhFCRCxpWb8LCHf+Gu"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h50", "_parent": {"__id__": 63}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ddFI252FNOFKuPYub+NBuO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "e37+DuLBhNVKT216t3mJpY"}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [52, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBoxRole", "_parent": {"__id__": 62}, "_children": [{"__id__": 67}], "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 66}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fcgA4l+RBKiogVs3ICcIxU"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "99VcItpWtI75FmSNUQKnQL"}, "_contentSize": {"__type__": "cc.Size", "width": 57, "height": 55}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.433, 98.437, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeSweat", "_parent": {"__id__": 66}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8bxfwvaMdLdb9kQGkZyi1x"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "8cB4Iv/L9Dip7WLUUIqb8j"}, "_contentSize": {"__type__": "cc.Size", "width": 16, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [24.415, 27.627, 0, 0, 0, 0, 1, 1, 0, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePool", "_parent": {"__id__": 2}, "_children": [{"__type__": "cc.Node", "_name": "CC_nodePoolRoot", "_parent": {"__id__": 68}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "6dtPl4ryhIAoVsOusO3qS+"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -76.701, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 69}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 68}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 68}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": 4}, "_size": {"__type__": "cc.Size", "width": 47.81, "height": 166}}, {"__type__": "d0aabggwVlIIplF8BRBIFLQ", "node": {"__id__": 68}, "msgIdIn": 7, "msgIdOut": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "61SuEI7bdO3IpmV9Btgw6U"}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 166}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-122.217, -42.302, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h217", "_parent": {"__id__": 68}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fflTC9K6FDyKRtQNu5pJif"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "5667Sv9vpAM7s/qrp/wEZg"}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 166}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTrashcan", "_parent": {"__id__": 2}, "_children": [{"__id__": 71}, {"__id__": 72}, {"__id__": 73}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 70}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 70}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": -0.7}, "_size": {"__type__": "cc.Size", "width": 55.5, "height": 75}}, {"__type__": "d0aabggwVlIIplF8BRBIFLQ", "node": {"__id__": 70}, "msgIdIn": 7, "msgIdOut": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "73uavjjMhFPqKFC1HT+kYF"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 75}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [270.863, -87.415, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTrashUp", "_parent": {"__id__": 70}, "_children": [{"__type__": "cc.Node", "_name": "CC_nodeTong", "_parent": {"__id__": 71}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "f8eCjyC5FOa5Dlo0C20jWx"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -51.597, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 71}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ddRdOulq9Erphn07WW41LD"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "fcq3IlE6VAlIWXSttLBnOE"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 53.456, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h212", "_parent": {"__id__": 70}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 72}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "91Gyppdj9EhIRG84/aGRwj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "6d5qUzjKRHmYsEebyHpqaQ"}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 75}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTrashDown", "_parent": {"__id__": 70}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7f5Yq7xk1Btp9lSuJWP1g/"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "44r7VVySNJ5oKgU+/6mvu8"}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 25}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 34.287, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 75}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 74}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 74}, "aniNode": {"__id__": 74}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 74}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "dbrXSrp6RJf5GnliZQ2SX1"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-346.582, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 74}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 75}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "07B1uH5pdMWZKx1tQWmHSq"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "girl", "_parent": {"__id__": 2}, "_children": [{"__id__": 77}, {"__id__": 82}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "66abYrwdVGV75y9XeqABLF"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-455.483, -124.853, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeGirlSpine", "_parent": {"__id__": 76}, "_children": [{"__id__": 78}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 77}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "sheng<PERSON>_idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "sheng<PERSON>_idle", "_N$skeletonData": {"__uuid__": "7fSRdsXe1LqqY9XZTo+AVW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "c545fPq6hLf7YyB0SwvK4e"}, "_contentSize": {"__type__": "cc.Size", "width": 114.99, "height": 110}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 77}, "_children": [{"__id__": 79}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "ab7R65K0xJS64beeOBsBAQ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 78}, "_children": [{"__id__": 80}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "87wsGGLLVAQ5WOR5CCTDdo"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:shenti", "_parent": {"__id__": 79}, "_children": [{"__id__": 81}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "acRZEYVr1JSaF38RJYdUQf"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeFlashlight", "_parent": {"__id__": 80}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 81}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "67Rbgb9FBEcJfmEiWlCE3/"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "7fQDbmYNxNV4WNuS5em+kU"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [14.466, -43.947, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "CC_nodeJinYa", "_parent": {"__id__": 76}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 82}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d4pdH9lO5KHb6XfPfd68z8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "63+YEeSQ9NoJiq2hC/NZTC"}, "_contentSize": {"__type__": "cc.Size", "width": 153, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 44.768, 0, 0, 0, 0, 1, 0, 0, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeYan", "_parent": {"__id__": 2}, "_active": false, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 83}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "attack", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "attack", "_N$skeletonData": {"__uuid__": "2e56vzMcZNna/pT7Hy+Bk8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "d3VXOkDWpDIbmqd5vxg2Fx"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -104.874, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeNight", "_parent": {"__id__": 2}, "_children": [{"__id__": 85}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "79nnYaGylAWZTuIgp3FajV"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_maskFlashlight", "_parent": {"__id__": 84}, "_children": [{"__id__": 86}], "_components": [{"__type__": "cc.Mask", "node": {"__id__": 85}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "82PQO49f1H1pgsUVi7eJbe"}, "_type": 2, "_N$alphaThreshold": 0.5, "_N$inverted": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "3e0Xz2aB5NErCBnKGqVZn7"}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 580}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [607.225, -592.681, 0, 0, 0, 0.7071067811865475, 0.7071067811865477, 0.55556, 0.55556, 0.55556]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 89.99999999999999}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBlack", "_parent": {"__id__": 85}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 86}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "52ydZqEkJFGYxPtj70y12Y"}, "_opacity": 80, "_color": {"__type__": "cc.Color", "r": 38, "g": 38, "b": 38}, "_contentSize": {"__type__": "cc.Size", "width": 6400, "height": 6400}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2e-05, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865477, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -89.99999999999999}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBubble", "_parent": {"__id__": 2}, "_children": [{"__id__": 88}, {"__id__": 89}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "7eWIEd4YxOjprd5Kqi+1uL"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [41.142, -17.045, 0, 0, 0, 0, 1, 0, 0, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 87}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 88}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "51FuViwzlEXKejUDSz9R9y"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "baL0DJ6pRNTo7lUUOiM5Nt"}, "_contentSize": {"__type__": "cc.Size", "width": 183, "height": 125}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [25, 35, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_labelWord", "_parent": {"__id__": 87}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 89}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "人呢？？？", "_N$string": "人呢？？？", "_fontSize": 20, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "28dmbLnQ9DEJb8CNUbVkJX"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [25, 40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "LabelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 90}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "躲起来就不用逛街啦！", "_N$string": "躲起来就不用逛街啦！", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7ejscHhJhKr74WRe8bTKj9"}, "fileId": "25HTZWm5FERanBU+H8yax/"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288.142, 118.07, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], {"__type__": "cc.SpriteFrame", "content": {"name": "h215", "texture": "1447219f2", "rect": [581, 573, 70, 84], "offset": [0, 0], "originalSize": [72, 86], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h218", "texture": "1447219f2", "rect": [239, 697, 57, 55], "offset": [-0.5, 28.5], "originalSize": [78, 138], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h217", "texture": "1447219f2", "rect": [591, 143, 176, 166], "offset": [0, 0], "originalSize": [178, 168], "capInsets": [0, 0, 0, 0]}}]