[{"__type__": "cc.SpriteFrame", "content": {"name": "art01", "texture": "8fJEQB0IRLeL09nnukpILy", "rect": [0, 0, 368, 318], "offset": [0, 0], "originalSize": [368, 318], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vShortCutAdDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 10}, {"__id__": 11}, {"__id__": 14}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "8cm6yTau9DLa6EnJ+kQ+7R"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 12, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "71n6SqFN5PlInVLdMza4HB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "0eMb3pyZFMeb5yamAYU8/F"}, "_contentSize": {"__type__": "cc.Size", "width": 368, "height": 318}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "title", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "创建图标领取奖励", "_N$string": "创建图标领取奖励", "_fontSize": 26, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "ccBWXmLopCWam0hGpVMnIp"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_richTextContent", "_parent": {"__id__": 2}, "_children": [{"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "_components": [{"__type__": "cc.RichText", "node": {"__id__": 5}, "_N$string": "将<color=#eac516>“茶叶蛋大冒险”</c>添加到桌面，即可领取相应奖励~", "_N$fontSize": 20, "_N$maxWidth": 294, "_N$lineHeight": 24}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "23yVBwkCBJkYPMxivLLBps"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 294, "height": 54.239999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 74, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_string": "将", "_N$string": "将", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "b2svcyqPJFSYh9zCGnzGnH"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-147, -30.239999999999995, 0, 0, 0, 0, 1, 1, 1, 1]}, "_zIndex": -32768}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_string": "“茶叶蛋大冒险”", "_N$string": "“茶叶蛋大冒险”", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "0fKzUaxbdBo4b50rLASrCZ"}, "_color": {"__type__": "cc.Color", "r": 234, "g": 197, "b": 22}, "_contentSize": {"__type__": "cc.Size", "width": 133.32, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-127, -30.239999999999995, 0, 0, 0, 0, 1, 1, 1, 1]}, "_zIndex": -32768}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_string": "添加到桌面，即", "_N$string": "添加到桌面，即", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "1eU4jRzkRLx7rAp+xx9U9U"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [6.319999999999993, -30.239999999999995, 0, 0, 0, 0, 1, 1, 1, 1]}, "_zIndex": -32768}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_string": "可领取相应奖励~", "_N$string": "可领取相应奖励~", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "d67FTGw6VFSKpoWctsRxri"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 151.68, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-147, -54.239999999999995, 0, 0, 0, 0, 1, 1, 1, 1]}, "_zIndex": -32768}, {"__type__": "cc.Node", "_name": "tip", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "(可能是奖励/复活/解锁关卡等)", "_N$string": "(可能是奖励/复活/解锁关卡等)", "_fontSize": 16, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "2dU0E68ktLp7lUjjTbPWs+"}, "_color": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124}, "_contentSize": {"__type__": "cc.Size", "width": 211.55, "height": 25.2}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-142, 10, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonSure", "_parent": {"__id__": 2}, "_children": [{"__id__": 12}, {"__id__": 13}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 11}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "e79lkdHSRA7oRCjkTBz5Ip"}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c04", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d8S3ekLz5H+oXTMqQHukdG"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "f5rar5OoJHi5IXdBN5MWSQ"}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "创建并领取", "_N$string": "创建并领取", "_fontSize": 22, "_lineHeight": 26, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "65h+35si9HvY9c7ja1vnUx"}, "_color": {"__type__": "cc.Color", "r": 234, "g": 197, "b": 22}, "_contentSize": {"__type__": "cc.Size", "width": 110, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAd", "_parent": {"__id__": 2}, "_children": [{"__id__": 15}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 14}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "75I/yBlvRPBb/tEwxyOpoz"}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -112.906, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "观看视频后领取", "_N$string": "观看视频后领取", "_fontSize": 22, "_lineHeight": 26, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b1Dfk/ssVARbpGYYetI1D/"}, "fileId": "6f+z8wbvhN6Yy1RDSynZ9g"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "art02", "texture": "e6dvjGHb1EuZnR5EKfcqSV", "rect": [0, 0, 230, 52], "offset": [0, 0], "originalSize": [230, 52], "capInsets": [0, 0, 0, 0]}}]