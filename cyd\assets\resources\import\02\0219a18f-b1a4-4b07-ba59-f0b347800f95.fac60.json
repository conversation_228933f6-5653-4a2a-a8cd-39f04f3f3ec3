[{"__type__": "cc.Prefab", "_name": "map195", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map195", "_children": [{"__id__": 2}, {"__id__": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "乌鸦喝水", "_N$string": "乌鸦喝水", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "88z2cKI6JKU4R1ewxKBGip"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 4}, {"__id__": 7}, {"__id__": 9}, {"__id__": 16}, {"__id__": 23}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDoorEnd", "_parent": {"__id__": 3}, "_children": [{"__id__": 5}, {"__id__": 6}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 4}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 4}, "_offset": {"__type__": "cc.Vec2", "y": 62}, "_size": {"__type__": "cc.Size", "width": 100, "height": 124}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 4}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "430lJJyipDxr4dWxuLJoJL"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [374, -122, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "door", "_parent": {"__id__": 4}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 5}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 5}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "9e6sfTKB1G3IRlMM2uCi8i"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 6}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "f6uAhyKBBP3KmhlcFUcSl9"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 147, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 3}, "_children": [{"__id__": 8}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 7}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 7}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 7}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "4ckywZqwdG7YUQpN6jeLok"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-385, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "ceNp7bfmRGiYhUQBv/GABP"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeGround1", "_parent": {"__id__": 3}, "_children": [{"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 9}, "_gravityScale": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 9}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 128, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "22S3pLXltIsJTTQ5WZzTHp"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-385, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 10}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "55Ef0wcnpBvYKSE8dlhE4c"}, "_contentSize": {"__type__": "cc.Size", "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 11}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "62MjlxB6dFMKSl8BNC0wEF"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 12}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "26uXPDLgJJzoyfa+dYn3l9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 13}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "f77i4oai5NMLmLquQzT99P"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 14}, "_alignFlags": 13, "_top": 64, "_originalHeight": 256}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "9eIs2aTx9Bwo+WT2fqPAcx"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeTouchLeft", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "26boGrf5RBBZdJD+iM/Tnr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "77SrTlDgZM5bWyt1bSiOud"}, "_contentSize": {"__type__": "cc.Size", "width": 133, "height": 99}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -106, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeGround2", "_parent": {"__id__": 3}, "_children": [{"__id__": 17}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 16}, "_gravityScale": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 16}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 128, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "29QovmwDpJ+ov+omurlcJf"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-209, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 17}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "5aNQdhZl1EDK/1ZkT+wXQ9"}, "_contentSize": {"__type__": "cc.Size", "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 18}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "5a08zXvW5Hwa1SIG1Yxhf/"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 19}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "20a7eM4NtKNLtE7SwUgT3d"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 20}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "11fxldXvNDS7bb3jZ6Rcb4"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 21}, "_alignFlags": 13, "_top": 64, "_originalHeight": 256}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "adGUJuQf9PC5YxquE+7oli"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeTouchRight", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78HXp7GrZC8paPaf7VorCD"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "c8tuV33J9Dg7I6sA9tUOUX"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -104, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeGround3", "_parent": {"__id__": 3}, "_children": [{"__id__": 24}, {"__id__": 25}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 23}, "_gravityScale": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 23}, "_density": 0.2, "_offset": {"__type__": "cc.Vec2", "y": -800}, "_size": {"__type__": "cc.Size", "width": 128, "height": 1600}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "e28Dx4idJLS6hmy6cwzwQJ"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 1600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [374, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 24}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "b30gsCU8xJ3qI4eWY++YBN"}, "_contentSize": {"__type__": "cc.Size", "height": 1536}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 25}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "31caadf0lLFrpNcQQJJdlu"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 26}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "841NqxXnJIFZ20TeqyRNVe"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 27}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "29LNeT+z9DHYa0qrS8k42z"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 1536}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -832, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 28}, "_alignFlags": 13, "_top": 64, "_originalHeight": 256}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "33Yu/HbCtErae4Biv0hZc3"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 1536}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -832, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeTouchJump", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "944x7TrvtIK6vu268rYsfi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "02GaGPsaRLB7pZ8LNHgA+V"}, "fileId": "7bkyz/pilCobTgUK5xGsUE"}, "_contentSize": {"__type__": "cc.Size", "width": 146, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -103, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}]