[{"__type__": "cc.SpriteFrame", "content": {"name": "c78", "texture": "f25peUS+JHO5K0jq5/kNfQ", "rect": [0, 0, 36, 100], "offset": [0, 0], "originalSize": [36, 100], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c80", "texture": "be3YjQh59DS4UVgRPqXjbf", "rect": [0, 0, 36, 100], "offset": [0, 0], "originalSize": [36, 100], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c79", "texture": "9c4acfQohCJp79MTc7/FRZ", "rect": [0, 0, 36, 100], "offset": [0, 0], "originalSize": [36, 100], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c110", "texture": "94Aqih4fZAjIZYNtIBlY9V", "rect": [0, 0, 202, 48], "offset": [0, 0], "originalSize": [202, 48], "capInsets": [0, 0, 0, 0]}}]