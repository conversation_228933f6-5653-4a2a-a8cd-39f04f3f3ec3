[[{"__type__": "cc.Prefab", "_name": "map282", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map282", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 146}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "20tYTiQ2JOAaMlCg0q+HQu"}, "_contentSize": {"__type__": "cc.Size", "width": 2560, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [640, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "LabelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "夺得MVP", "_N$string": "夺得MVP", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "0aL9/CDxtE97pz1RqewfsO"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 166.68, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 180, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 5}, {"__id__": 8}, {"__id__": 11}, {"__id__": 73}, {"__id__": 74}, {"__id__": 75}, {"__id__": 76}, {"__id__": 89}, {"__id__": 92}, {"__id__": 95}, {"__id__": 97}, {"__id__": 112}, {"__id__": 131}, {"__id__": 133}, {"__id__": 138}, {"__id__": 144}, {"__type__": "cc.Node", "_name": "CC_nodeRoleFirePos", "_parent": {"__id__": 4}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "29JejyA/lPdZQbHSvF+6A9"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [25, 5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__id__": 145}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "dici", "_parent": {"__id__": 4}, "_children": [{"__id__": 6}, {"__id__": 7}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 5}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 5}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 11}, "_size": {"__type__": "cc.Size", "width": 64, "height": 22}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 5}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "5auG7N+yRM6pskSLnFvnOc"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [750, -124, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "sp1", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "11/bDyJFdGA675ki1JdPDB"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "b4BjtzK5xF3asnwsteibwd"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "sp2", "_parent": {"__id__": 5}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "4e32jvJMROJreUmKPGmy4P"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "a9z8hoKqVN4bwPmSqTvPif"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "dici", "_parent": {"__id__": 4}, "_children": [{"__id__": 9}, {"__id__": 10}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 8}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 8}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 11}, "_size": {"__type__": "cc.Size", "width": 64, "height": 22}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 8}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "56pVnC2oFN1pzDtnqiCw8/"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1205, -124, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "sp1", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "11/bDyJFdGA675ki1JdPDB"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "35qXkiBFVE34BIeQFBFm5B"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "sp2", "_parent": {"__id__": 8}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "4e32jvJMROJreUmKPGmy4P"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "849PqkdRFNL7W3LMbVVUOK"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeGroud", "_parent": {"__id__": 4}, "_children": [{"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 64}, {"__id__": 65}, {"__id__": 66}, {"__id__": 67}, {"__id__": 68}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}, {"__id__": 72}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 11}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 11}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 2560, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "8eyKGFiTZEv7NAOl2iDc9B"}, "_contentSize": {"__type__": "cc.Size", "width": 2560, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [640, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 12}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "16tk/7mf1BuqFADcVSDn01"}, "_contentSize": {"__type__": "cc.Size", "width": 2432, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 13}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "f44tgvdC1ObZTGe6FLgCI2"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1248, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 11}, "_children": [{"__id__": 15}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 31}, {"__id__": 32}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}, {"__id__": 43}, {"__id__": 44}, {"__id__": 45}, {"__id__": 46}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}, {"__id__": 58}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 63}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 14}, "_layoutSize": {"__type__": "cc.Size", "width": 2432, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "ddc2EmCTlCdbmXCVzdFw96"}, "_contentSize": {"__type__": "cc.Size", "width": 2432, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 14}, "_children": [{"__id__": 16}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "a4/DiqnilC6bAASJhGu+Lj"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1184, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 15}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9cUnxHX2RA2LP5/LfwMNRB"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9bV/rW0adJ7Zv3rUBl4FYG"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1120, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "55IwJKFitHeYKaFXEGVdRs"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1056, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "6dIgiTfstB/655YLphn5Oq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-992, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 14}, "_children": [{"__id__": 21}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "25XbyqI49EVpDqcLNlRTCt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-928, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "822pi2V6VEL4SX+6Y3qKdN"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "cc4zERolJLApkvW+f5OreS"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-864, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "507bNpuIhJwp2VmmDwSQn0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-800, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_children": [{"__id__": 25}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9ayMfbLqxIe7OMufeq0Qph"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-736, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 24}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "1fUcNcLpNMmZe4VL802LFW"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, 28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "1cw6ZnHMZGnapJ8pgd3lh0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-672, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "e29WT4ZspAVrkd7QVFne0Q"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "b2OCzpHUtHP4mKG56sBDqa"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_children": [{"__id__": 30}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "5eTM/2hnZAUZ/JpdNgb5o8"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 29}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "4fHhAk7NpLGaltywPJALMP"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.9759999999999991, 23.812, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "07jAfxXPhKLasQQiQwgOS4"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_children": [{"__id__": 33}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "f6ExGXl0ZHe6EWSWI0omOO"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little04", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78LL5TK2RJ/7QmPvmVHdJr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9fxZubRThBIJl9VveBBBIA"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 7.955, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9a6FeTCnlOE7pFa2UqUL1U"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "93MqzrPMdC37TjMB1twLbo"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 14}, "_children": [{"__id__": 37}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "6dtKe+5kZD8r/L4tVO+xLk"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "5dWgdMlP9KeoRqZxbkNwID"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "84lu1CFcdG/KR0XhsNIF1s"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "388BuBOIpFe6L8OAZ0+9mE"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9ci3HGx1BAMp3hnCAiYDGe"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 14}, "_children": [{"__id__": 42}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "5dXbXoUmBKVpzemUaElrhR"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9aYuAn6mpIlKYlcvqW428x"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "feKOVx6PpBdICgDoqcssRF"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "edOgbKg8RN/qcJdkK3jvNB"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "7a6PmM+LJKGahHrM3IDRpd"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 14}, "_children": [{"__id__": 47}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "43OK0G95lM1auBwkkeQvzG"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "baTkiO0eVDRrq+297a7pHK"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "04BRmRXmRCHaXRLfidaqD/"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "40bycPqKJF2aFhJ+jqzX0I"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "44fpthIzZOIqPee4tRzaJe"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 14}, "_children": [{"__id__": 52}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "e3IWhHpUxDWLy6ecxrOapD"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 51}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "3e+KksaBNOkplDbuqxCQ72"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "f1T0g3qb1KZJmu8KR7RTNy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [672, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "10W3tfjSVHpbq7KKxCIdPx"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [736, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 55}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "1bSAhnmOVCQIurvVaWBUgE"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [800, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 14}, "_children": [{"__id__": 57}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "47GcRjSqZC1o78t9/gtMol"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [864, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 56}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "c6dApdsFRFyqMoISGoSYbo"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "71YBMxjFVDfJf0vq8Nrd5a"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [928, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9etfAB9ldPJ7AQglwZH7wM"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [992, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "7dJ/GJsEpL/aD6Ou5++wII"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1056, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 14}, "_children": [{"__id__": 62}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "16qqTJV4NAtbJesjsQWQLS"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1120, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 61}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "554sZCWrlHD6zMbMLxYv9E"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 63}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9ftuuyyBFFjrj1nT6q8Amt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1184, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 64}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 64}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "95nZXSvghBgYlX5a47h/2j"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1248, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 65}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "2bci3i3odM6aJJ0WmUvrb+"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1248, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 66}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 66}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "1as5rV+SxBYIknWzrjkHU8"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1248, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 67}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "a9X5nYo69LmKK9kAdgNUKL"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1248, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 68}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 68}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "22c4t9gwpMtpGwjUNgbDEy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1248, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 69}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "8bCu1FF9pKerKjXEGG1zU+"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1248, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 70}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 70}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "b1RtjZU1xNRr231PhFjiyb"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1248, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 71}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 71}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "7eLChyBRVIw40mD/GxaoHa"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1248, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 72}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 72}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "96jNWM7sVIcaP9tFTACOqi"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1248, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "f237", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f4+A8R88NJGYaObKdM7fxC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "359bAOVUlFRoJVoIgsxOwd"}, "_contentSize": {"__type__": "cc.Size", "width": 224, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-200, -85.752, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "f238", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 74}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c0HXsF9ydEY7FxxxKEbKbU"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "e2rkblzedJwbjHjoUyU5Gl"}, "_contentSize": {"__type__": "cc.Size", "width": 224, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-200, -85.752, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeGunProp", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 75}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "70U/nqUEVBZYuw9RovvUWi"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 75}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 75}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": -10}, "_size": {"__type__": "cc.Size", "width": 60, "height": 50}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 75}, "msgId": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "e887Sb5HxDNJL+Wnz+jiMV"}, "_contentSize": {"__type__": "cc.Size", "width": 86, "height": 58}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [200, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleGirl", "_parent": {"__id__": 4}, "_children": [{"__id__": 77}, {"__type__": "cc.Node", "_name": "CC_nodeRoleGirlArea", "_parent": {"__id__": 76}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "77wCktAR9EJ4gfSSrNyEsP"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [8, 55, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 76}, "_type": 0, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 76}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 76}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "c8iv2LxsNGZqcfTUko+LI5"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-260, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeRoleGirlSpine", "_parent": {"__id__": 76}, "_children": [{"__id__": 78}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 77}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "shafa_idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "shafa_idle", "_N$skeletonData": {"__uuid__": "7fSRdsXe1LqqY9XZTo+AVW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "53vFGKPctI2LJxenp/pQux"}, "_contentSize": {"__type__": "cc.Size", "width": 114.99, "height": 110}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 77}, "_children": [{"__id__": 79}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "baTnorVZRDa44xMYJ0ZCN5"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 78}, "_children": [{"__id__": 80}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "c2r7vLemlEFr+U3WNiVM6j"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:shenti", "_parent": {"__id__": 79}, "_children": [{"__id__": 81}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "dd2qOPJflGb7gp9D/JZPnS"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl", "_parent": {"__id__": 80}, "_children": [{"__id__": 82}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "d7Fyb3ULtI+6AFphtVMkEa"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl2", "_parent": {"__id__": 81}, "_children": [{"__id__": 83}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "d2g+0uaKdHB7Dh4sqmsBIT"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl3", "_parent": {"__id__": 82}, "_children": [{"__id__": 84}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "27WX+VQCZG16tLP8zTol50"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl4", "_parent": {"__id__": 83}, "_children": [{"__id__": 85}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "b1aVgT3kVCVqPJneRmGWqr"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl5", "_parent": {"__id__": 84}, "_children": [{"__id__": 86}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "4chhi6gCNDl5qexzXja1Ep"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl6", "_parent": {"__id__": 85}, "_children": [{"__id__": 87}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "dbkU8HbpVFUI0euQ2NlU8Y"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:normal/gong", "_parent": {"__id__": 86}, "_children": [{"__id__": 88}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "bfuewlQ0FNWINEA1MN56lM"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeGunRoleGirl", "_parent": {"__id__": 87}, "_children": [{"__type__": "cc.Node", "_name": "CC_nodeRoleGirlFirePos", "_parent": {"__id__": 88}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "b0kAXZ2QxOSagUaE1O6xXZ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 88}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6a+5VX55VAH7sm5eXttm2D"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "5bQ2dPBPNHyr5O1cMniMQi"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 52}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5, 10, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBubble", "_parent": {"__id__": 4}, "_children": [{"__id__": 90}, {"__id__": 91}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "912wJ56qVLEIcZzNX2HPt/"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-215, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 90}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "51FuViwzlEXKejUDSz9R9y"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "bdk4/XEb1O5o77F8Wih50q"}, "_contentSize": {"__type__": "cc.Size", "width": 183, "height": 125}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [30, 41.157, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "word", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 91}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "加油", "_N$string": "加油", "_fontSize": 24, "_lineHeight": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "80d/QdgkdJzKkfMZZ4VqWU"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 156, "height": 37.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [28, 51.157, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeInvite", "_parent": {"__id__": 4}, "_children": [{"__id__": 93}, {"__id__": 94}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 92}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e1DnI5A21PsKWSzlmr9We8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "ceqNBc3Y1MX40M/f+EacBj"}, "_contentSize": {"__type__": "cc.Size", "width": 382, "height": 102}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-250, 30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_buttonCloseInvite", "_parent": {"__id__": 92}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 93}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9H1paS0hLuLiX5qpHcLD9"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "22RheMbfdA6pxbCWQQibUr"}, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 43}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, 45, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_buttonInvite", "_parent": {"__id__": 92}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 94}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "cf56mo+nJBN5XxYJ2xKKUk"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "82t8u973NE4qGCFJ4Yg7q4"}, "_contentSize": {"__type__": "cc.Size", "width": 119, "height": 72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [110, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 4}, "_children": [{"__id__": 96}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 95}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 95}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 95}, "aniNode": {"__id__": 95}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "4ckywZqwdG7YUQpN6jeLok"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-430, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 95}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 96}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "ceNp7bfmRGiYhUQBv/GABP"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeEnemy", "_parent": {"__id__": 4}, "_children": [{"__id__": 98}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 97}, "_type": 0, "_gravityScale": 3, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 97}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9d8tX9DvlITpGohRiog4LG"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1280, 434, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "CC_nodeEnemySpine", "_parent": {"__id__": 97}, "_children": [{"__id__": 99}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 98}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "d6PuohEB1BK6IoFZyGvKDM"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "b1G7pk2s5MzKgMyrToTykM"}, "_contentSize": {"__type__": "cc.Size", "width": 149, "height": 143.99}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 98}, "_children": [{"__id__": 100}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "46U4bXWBpNQYfYKw5JuwRF"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 99}, "_children": [{"__id__": 101}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "earql/RCtIRaJbuPkvSP08"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zong", "_parent": {"__id__": 100}, "_children": [{"__id__": 102}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "5fDeYDCEVFH5sMXg36/apb"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:shenti", "_parent": {"__id__": 101}, "_children": [{"__id__": 103}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "2bn3ohEP9GZrkZpvyyU8Lc"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl", "_parent": {"__id__": 102}, "_children": [{"__id__": 104}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "20neRtwBhDBLP3coTv+zjf"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl2", "_parent": {"__id__": 103}, "_children": [{"__id__": 105}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "050WZaFzdM9pttFApUanKy"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl3", "_parent": {"__id__": 104}, "_children": [{"__id__": 106}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "ffZnJvsDZPIKywIE3DoJSa"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl4", "_parent": {"__id__": 105}, "_children": [{"__id__": 107}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "f2U/ATKpxGma3fQpobmSNb"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl5", "_parent": {"__id__": 106}, "_children": [{"__id__": 108}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "27jPQcsGRPkaGaaakBSnHc"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:zs_jl6", "_parent": {"__id__": 107}, "_children": [{"__id__": 109}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "8aNXFToNpIO5iiYXzd8Yl0"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:chegngan2", "_parent": {"__id__": 108}, "_children": [{"__id__": 110}, {"__id__": 111}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "09Dfi27AFDko9eX/edvALW"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "CC_nodeBulletEnemy", "_parent": {"__id__": 109}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 110}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3bDDIpEIxOM4IDB+JngqrV"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "9cHuh2EGxBFo1u6IRKoSPh"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 41}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [30, 16, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "gun", "_parent": {"__id__": 109}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 111}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "70U/nqUEVBZYuw9RovvUWi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "97P8ErNb1O24q+oYhji4k3"}, "_contentSize": {"__type__": "cc.Size", "width": 86, "height": 58}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [20, 5, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 12, "groupIndex": 12}, {"__type__": "cc.Node", "_name": "CC_nodeMonsterParent", "_parent": {"__id__": 4}, "_children": [{"__id__": 113}, {"__id__": 115}, {"__id__": 117}, {"__id__": 119}, {"__id__": 121}, {"__id__": 123}, {"__id__": 125}, {"__id__": 127}, {"__id__": 129}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "f3z9UCkt1Pl4SI+qacJhsq"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 114}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 113}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 113}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 113}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "20mjiT/vpIcLOQds48FqmB"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [680, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 113}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 114}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "745d7r5tJPDbFf0UOxOAp9"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 116}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 115}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 115}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 115}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "abcNUf7N5I8rXgjdk/kury"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [830, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 115}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 116}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "01HQdYwx5DK5pg6zK63Gyp"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 118}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 117}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 117}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 117}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "23F1i3jJFD85X1Gqb25eW1"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [980, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 117}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 118}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "4bf0ZMCRtBQL3pI5hr8Y9A"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 120}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 119}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 119}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 119}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "97hHfP751DA4tNvTtfsEb4"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1130, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 119}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 120}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "cfvq03FP1P4ZeWgI07JwbL"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 122}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 121}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 121}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 121}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "3b307wAK1BaqdWtdYby46D"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1280, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 121}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 122}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "d0EWyq/rtHErbulcFEwU7i"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 124}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 123}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 123}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 123}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "b0Re1McTVI76W+YhkfM9R7"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1430, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 123}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 124}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "fdDAwkbG5HG5X5rom1pShe"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 126}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 125}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 125}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 125}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "e6aL1ZPWtFZ73PfN1sDxfK"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1580, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 125}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 126}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "74JCKkfvVJH5rq/aKI7nn1"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 128}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 127}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 127}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 127}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "85QKT4BeBMZIc/w16eh+pj"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1730, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 127}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 128}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "44JtBCaSFCkZDRKv+qR+Ah"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeMonsterSmall", "_parent": {"__id__": 112}, "_children": [{"__id__": 130}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 129}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 129}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 129}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "22XiplT8lMXZyjeX0x7Wk1"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1880, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 129}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 130}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "8b85Af/aNMiJfj1efDY+9E"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "CC_nodeMonsterSmall", "_parent": {"__id__": 4}, "_children": [{"__id__": 132}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 131}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 131}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 45}, "_size": {"__type__": "cc.Size", "width": 60, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 131}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "d7L8KiwSBInoJUz+idcqRe"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2030, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 131}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 132}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "642XUH7DpIJJWwouDDwQyT"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -0.4, 0.4, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "CC_nodeMonsterBig", "_parent": {"__id__": 4}, "_children": [{"__id__": 134}, {"__id__": 135}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 133}, "_type": 0, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 133}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 90}, "_size": {"__type__": "cc.Size", "width": 160, "height": 180}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 133}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "17N4UlCWJI2bPohgZeQ/id"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 180}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1780, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "nodeSpine", "_parent": {"__id__": 133}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 134}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "9d+W9xkNlF8I7ANfuONzKy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "2e7dHnO+tKwaUfB0+GgU9q"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 237}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "progressBar", "_parent": {"__id__": 133}, "_children": [{"__id__": 136}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 135}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "20EOJw/SNIp5P6VQePvqjs"}, "_type": 1, "_sizeMode": 0}, {"__type__": "cc.ProgressBar", "node": {"__id__": 135}, "_N$totalLength": 176, "_N$barSprite": {"__id__": 137}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "02/WzdQyRIJ6RPqNzkr4VO"}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 206.964, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Node", "_name": "bar", "_parent": {"__id__": 135}, "_components": [{"__id__": 137}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "8dwVuEEZFCfb6D1QvDd+hR"}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-88, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 11, "groupIndex": 11}, {"__type__": "cc.Sprite", "node": {"__id__": 136}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "67Hz2guDxB+rE11uuuPvZS"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Node", "_name": "CC_nodeBulletParent", "_parent": {"__id__": 4}, "_children": [{"__id__": 139}, {"__id__": 140}, {"__id__": 143}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "17+Gwy8sZKcYRm0Ng1lu2i"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeBullet", "_parent": {"__id__": 138}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 139}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3bDDIpEIxOM4IDB+JngqrV"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 139}, "_allowSleep": false, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 139}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 46, "height": 30}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 139}, "msgId": 21}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "a2qX/yZ8pE3Y2JZ9CAwHPZ"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 41}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeBulletRoleGirl", "_parent": {"__id__": 138}, "_children": [{"__id__": 141}, {"__id__": 142}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 140}, "_allowSleep": false, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 140}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 54, "height": 26}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 140}, "msgId": 22}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "d4FmP9v4FJI6R4YBzSfAhf"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, -50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "light", "_parent": {"__id__": 140}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 141}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}]}, {"__type__": "cc.Animation", "node": {"__id__": 141}, "_defaultClip": {"__uuid__": "73sRWp2ZRHS52sPtz+SGII"}, "_clips": [{"__uuid__": "73sRWp2ZRHS52sPtz+SGII"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "5eOSZe2OpL8LCslgn04zcQ"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-40, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "a672", "_parent": {"__id__": 140}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 142}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5fjxePC/9B2ZK2YmO3iy3W"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "08/q1uPthEGoz50mxaF+vK"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeZD", "_parent": {"__id__": 138}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 143}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "bong", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "bong", "_N$skeletonData": {"__uuid__": "6bpLZA37lKE47Gex6Egnzs"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 143}, "_type": 1, "_allowSleep": false, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 143}, "_sensor": true, "_radius": 180}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 143}, "msgId": 17}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "b3/qx3ntZHPbgHOVGGJM0F"}, "_contentSize": {"__type__": "cc.Size", "width": 572, "height": 376}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, -50, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_buttonFire", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 144}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "67G+Fg7f5EiIfbNm4CCBl/"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "d3WPEDLnlD+Kg7t8Zdiv05"}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 104}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [29.78, -219.43, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGun", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 145}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "70U/nqUEVBZYuw9RovvUWi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "7eT57NAUtJOoi3WQXvtxFv"}, "_contentSize": {"__type__": "cc.Size", "width": 86, "height": 58}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [15, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeDialog", "_parent": {"__id__": 1}, "_children": [{"__id__": 147}, {"__id__": 148}, {"__id__": 151}, {"__id__": 152}, {"__id__": 153}, {"__id__": 154}, {"__id__": 156}, {"__id__": 157}, {"__id__": 158}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "6f7l3WTIBGRZzG0qhZxn9C"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -650, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 146}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 147}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7ePjMiC3VBnJxgrD/NXf3m"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "0b3RTcNzxKD6hihiGWphG/"}, "_contentSize": {"__type__": "cc.Size", "width": 545, "height": 345}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 80, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeHeadPos1", "_parent": {"__id__": 146}, "_children": [{"__id__": 149}, {"__id__": 150}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "faseGT+ahKy5epnAQ6lNlu"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-180, 90, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeHeadEnemy", "_parent": {"__id__": 148}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 149}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c4JZE8Yg9EnoDhNMYMYs0v"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "4fyEhm3OxBvohiH+PY8KnB"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeHeadDouble", "_parent": {"__id__": 148}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 150}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8ccRTwgwhFTZB9kUkScFzC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "f7M7LsgexKvqLsiOESR1ux"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tag1", "_parent": {"__id__": 146}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 151}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "36+3Ug5gpA2qB1o6vUOMYB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "fbyKczVL9ES6jFZVObukd3"}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-180, 55, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelScore1", "_parent": {"__id__": 146}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 152}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "1/0/0", "_N$string": "1/0/0", "_fontSize": 30, "_lineHeight": 32, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "33Oje86QdEqKvZdf/+9Gwo"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 66.72, "height": 40.32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 88, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelScore12", "_parent": {"__id__": 146}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 153}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "13.0", "_N$string": "13.0", "_fontSize": 30, "_lineHeight": 32, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "1dp6OImidEp4sEPcZObmY7"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 58.39, "height": 40.32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [195.384, 88, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeHeadPos2", "_parent": {"__id__": 146}, "_children": [{"__id__": 155}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "cb422MmApKVrfNS9692KO2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-180, -15, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeHeadRole", "_parent": {"__id__": 154}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 155}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e6g5iQ4oZPQJZCrQq+WUAT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "f2jBE5cg9IN7xQ5mP1H1n3"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tag2", "_parent": {"__id__": 146}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 156}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fbE8W100ZP8blydB7RR8DO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "42+4P9zMNFw6onVvnnrOR8"}, "_contentSize": {"__type__": "cc.Size", "width": 73, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-180, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelScore2", "_parent": {"__id__": 146}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 157}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "9/0/1", "_N$string": "9/0/1", "_fontSize": 30, "_lineHeight": 32, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "40CDgdHHNFgoBTi0CCXoWc"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 66.72, "height": 40.32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -17, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelScore22", "_parent": {"__id__": 146}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 158}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "3.0", "_N$string": "3.0", "_fontSize": 30, "_lineHeight": 32, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "13gH1kC2BBi70KKZsfR2V4"}, "fileId": "17Z9NtaFlCPq4vjaZ/c47M"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 41.7, "height": 40.32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [195.384, -17, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "a666", "texture": "1c735b164", "rect": [695, 995, 73, 24], "offset": [0, 0], "originalSize": [73, 24], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a673", "texture": "1c735b164", "rect": [777, 501, 32, 22], "offset": [0, 0], "originalSize": [32, 22], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a672", "texture": "1c735b164", "rect": [635, 995, 54, 26], "offset": [0, 0], "originalSize": [54, 26], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a671", "texture": "1c735b164", "rect": [111, 683, 128, 52], "offset": [0, 0], "originalSize": [128, 52], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a662", "texture": "1c735b164", "rect": [3, 3, 545, 345], "offset": [0, 0], "originalSize": [545, 345], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a664", "texture": "1c735b164", "rect": [536, 715, 76, 76], "offset": [0, 0], "originalSize": [76, 76], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a663", "texture": "1c735b164", "rect": [618, 715, 76, 76], "offset": [0, 0], "originalSize": [76, 76], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a669", "texture": "1c735b164", "rect": [237, 807, 119, 72], "offset": [0, 0], "originalSize": [119, 72], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a668", "texture": "1c735b164", "rect": [3, 549, 382, 102], "offset": [0, 0], "originalSize": [382, 102], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a665", "texture": "1c735b164", "rect": [635, 913, 76, 76], "offset": [0, 0], "originalSize": [76, 76], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a670", "texture": "1c735b164", "rect": [717, 769, 44, 43], "offset": [0, 0], "originalSize": [44, 43], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a667", "texture": "1c735b164", "rect": [774, 715, 73, 24], "offset": [0, 0], "originalSize": [73, 24], "capInsets": [0, 0, 0, 0]}}]