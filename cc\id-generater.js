module.exports = function(require, module, exports) {
    var NonUuidMark = ".";
    function IdGenerater(category) {
      this.id = 0 | 998 * Math.random();
      this.prefix = category ? category + NonUuidMark : "";
    }
    IdGenerater.prototype.getNewId = function() {
      return this.prefix + ++this.id;
    };
    IdGenerater.global = new IdGenerater("global");
    module.exports = IdGenerater;
  }