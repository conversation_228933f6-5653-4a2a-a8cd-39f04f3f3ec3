[{"__type__": "cc.SpriteFrame", "content": {"name": "a7", "texture": "17aa323ec", "rect": [3, 733, 122, 138], "offset": [0, 0], "originalSize": [122, 138], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a6", "texture": "17aa323ec", "rect": [57, 493, 136, 120], "offset": [0, 0], "originalSize": [136, 120], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a8", "texture": "17aa323ec", "rect": [57, 350, 124, 137], "offset": [0, 0], "originalSize": [124, 137], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a9", "texture": "17aa323ec", "rect": [3, 877, 138, 120], "offset": [0, 0], "originalSize": [138, 120], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vCollectionItem", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vCollectionItem", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 12}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "d216HczsZFS7I8CttciK0a"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 350}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteBg", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "1cUXhRYxVMtbUkMCkuC08t"}, "_contentSize": {"__type__": "cc.Size", "width": 266, "height": 266}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeClip1", "_parent": {"__id__": 1}, "_children": [{"__type__": "cc.Node", "_name": "CC_buttonClip1", "_parent": {"__id__": 3}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "b37L18gnRCjbB5/DFdExCg"}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 115}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2, 10, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "45fRfK+OBAkZxP9+/WNip9"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "fdW4OypZRKt4z6TxsGuARa"}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 137}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-55, 49, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeClip2", "_parent": {"__id__": 1}, "_children": [{"__type__": "cc.Node", "_name": "CC_buttonClip2", "_parent": {"__id__": 4}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "88dT4KbrdNqKHp6s25DhRp"}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 115}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [12, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "44WOWY+7pMJL7fbusIrsMZ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "33EqHoXcdFnoyknXykXV9U"}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 120}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 57.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeClip3", "_parent": {"__id__": 1}, "_children": [{"__type__": "cc.Node", "_name": "CC_buttonClip3", "_parent": {"__id__": 5}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "db1fhFbUtMv47IbiCiiiBd"}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 115}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "92swiSbqhMX55xbUSFVZMt"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "8aQ9IMsxFEk5QPjCUDThCx"}, "_contentSize": {"__type__": "cc.Size", "width": 138, "height": 120}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-48.5, -58, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeClip4", "_parent": {"__id__": 1}, "_children": [{"__type__": "cc.Node", "_name": "CC_buttonClip4", "_parent": {"__id__": 6}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "d40e3nS0tFHaJNAQqQaiTv"}, "_contentSize": {"__type__": "cc.Size", "width": 115, "height": 115}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02Sig8Z4hBNKH2gWOY4wVN"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "88BIJ/X11OGrCgueeQEuvd"}, "_contentSize": {"__type__": "cc.Size", "width": 122, "height": 138}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [57, -49, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeCollectRedPoint", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "04KxIVs/hLsJI0Riwam0uk"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "d25/dmaThGb4NVq8BniE93"}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 27}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [110, 110, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLock", "_parent": {"__id__": 1}, "_children": [{"__id__": 9}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c2TJRTbSlFgoy+0Fhvx1GX"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "f5Rol3CnNHZLIbZHrMNADP"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 47}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [115, -105, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "layout", "_parent": {"__id__": 8}, "_children": [{"__id__": 10}, {"__id__": 11}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 9}, "_layoutSize": {"__type__": "cc.Size", "width": 144.43, "height": 50}, "_resize": 1, "_N$layoutType": 1, "_N$affectedByScale": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "cfzkYOL0pFZ6m1ritWMXKl"}, "_contentSize": {"__type__": "cc.Size", "width": 144.43, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-115, -45, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a17", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "94r/x21HRD5psFReGIUygz"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "20z3jcBJtJQ4JrgXyNTntP"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 59}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-56.215, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelUnlockStar", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "30 / 100", "_N$string": "30 / 100", "_fontSize": 30, "_lineHeight": 34, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}, {"__type__": "cc.LabelOutline", "node": {"__id__": 11}, "_color": {"__type__": "cc.Color"}, "_width": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "5bHg8t6KlHpornZvouFCLS"}, "_contentSize": {"__type__": "cc.Size", "width": 112.43, "height": 46.84}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [16, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelClip", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "3 / 4", "_N$string": "3 / 4", "_fontSize": 30, "_lineHeight": 34, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}, {"__type__": "cc.LabelOutline", "node": {"__id__": 12}, "_color": {"__type__": "cc.Color"}, "_width": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b8xiSbzy5FD5DoQ12VuQVD"}, "fileId": "9bQ48dAwVHvKfGvK5Y6E+X"}, "_contentSize": {"__type__": "cc.Size", "width": 62.37, "height": 46.84}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -150, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "a5", "texture": "17aa323ec", "rect": [97, 270, 40, 47], "offset": [0, 0], "originalSize": [40, 47], "rotated": 1, "capInsets": [0, 0, 0, 0]}}]