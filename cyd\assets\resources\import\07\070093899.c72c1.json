[[{"__type__": "cc.Prefab", "_name": "vGameCutLineTipDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "85Lw8QrzBMWo/cLjQMgXT+"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "art3", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d591vAh/dOoItAE/eqbB7y"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "85Lw8QrzBMWo/cLjQMgXT+"}, "fileId": "90rn3fCWhN9J+39ZUqIaMS"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteTips", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "85Lw8QrzBMWo/cLjQMgXT+"}, "fileId": "7aiN+dryRJVaycMAQsuZ22"}, "_contentSize": {"__type__": "cc.Size", "width": 534, "height": 320}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c0tghPhFHK5nyunAeaJzq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "85Lw8QrzBMWo/cLjQMgXT+"}, "fileId": "10IGAZmwNOkqcg5t52pnXt"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [410, 175, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "art4", "texture": "1f500dd69", "rect": [640, 645, 54, 53], "offset": [0, 0], "originalSize": [54, 53], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "art3", "texture": "1f500dd69", "rect": [3, 3, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}]