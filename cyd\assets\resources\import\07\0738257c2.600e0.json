[{"__type__": "cc.SpriteFrame", "content": {"name": "a210", "texture": "1ae7fb5a8", "rect": [3, 443, 84, 48], "offset": [0, 0], "originalSize": [84, 48], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "map235", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map235", "_children": [{"__id__": 2}, {"__id__": 86}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 40}, {"__id__": 69}, {"__id__": 71}, {"__id__": 73}, {"__id__": 82}, {"__id__": 84}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groud", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 31}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 39}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 3}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 3}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 1280, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "092najum9JkLmT7dfEn8P8"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 4}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "1e+3MlJPtO1YubySJA2wf2"}, "_contentSize": {"__type__": "cc.Size", "width": 1152, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 5}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "346XH3HYxEmY7fx4S+wVjP"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 3}, "_children": [{"__id__": 7}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 23}, {"__id__": 24}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 30}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 6}, "_layoutSize": {"__type__": "cc.Size", "width": 1152, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "a5FkdRomFO/65H2xM2aCIa"}, "_contentSize": {"__type__": "cc.Size", "width": 1152, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 6}, "_children": [{"__id__": 8}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "37Nzy+GUZIxrax9YI4+XG0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "4eiEMtduNKL7gR1TfPGLul"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "80OmgtKd5BWpUL2kIJe9mp"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "86KSd3+TVJ9ajnK35rbJJM"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "fe2cijugVCErejXQvgswQc"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 6}, "_children": [{"__id__": 13}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "06ar7y9ktE5JapXYK2Ho2y"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "c9l2yv9+ZJJLUcWbHSy3Ta"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "3boLM5KgRDZ7Wi4vRCVCuQ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "730Dd39ztOrp01umcX9viS"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_children": [{"__id__": 17}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "e1tHT5hJBA6IUmmnqJPsqQ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "15TTtSGEFHwoGUOUkMuRzb"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, 28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "4eUotxeC1Na7UgssXcgNfm"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "8bop0+NVJJQJq7ExHDQ/S7"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "7cOFF7DF1D/aeVJvysr78l"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_children": [{"__id__": 22}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "8flLG7yI9Gl6hrav5uupj9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 21}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "c1YNkrEktPhIAVG+FWIu0Y"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.9759999999999991, 23.812, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "7blr3w+GxJWYU33nN37HLt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_children": [{"__id__": 25}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "e9IzODPhtNTb5G6Rv8JNTE"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little04", "_parent": {"__id__": 24}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78LL5TK2RJ/7QmPvmVHdJr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "dbPxGch5VDrLZfUPovxyGy"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 7.955, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "a82xYhzSFGhbetVzFEk6+a"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "7dJI843oNDhq5aUwxbkNzn"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 6}, "_children": [{"__id__": 29}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "efoZ1GWTBAS5TRIZEXxdmQ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "8fuw/Q2fBDQLINe31Wu5WG"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "a3K4EcmdhM8Yr4R8JJoO6F"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 31}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "3dtMeV6z1OnK/aADPa0/Aa"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 32}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "11rxLLsOdM6K6dW3yRXnO2"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 33}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "8crN6kHmhDoLx/ZtJ+lGTF"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 34}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "86lNy4YvZAB6oX74F5AF4W"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 35}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "9dSa8KPD5F0LQAB2Si5Ilr"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 36}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "d5cLryYUBMpogSaHOwBejy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 37}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "7aUgNdTwhPxaWdRPzHjEMm"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 38}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "97dTro/DxK95e7vhqQB3rq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 39}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "71UaZki5lHzZpKdbZMIbqJ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeBoxParent", "_parent": {"__id__": 2}, "_children": [{"__id__": 41}, {"__id__": 43}, {"__id__": 45}, {"__id__": 47}, {"__id__": 49}, {"__id__": 51}, {"__id__": 53}, {"__id__": 55}, {"__id__": 57}, {"__id__": 59}, {"__id__": 61}, {"__id__": 63}, {"__id__": 65}, {"__id__": 67}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "dddS31agdCCa8qqgzzoolS"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "11", "_parent": {"__id__": 40}, "_children": [{"__id__": 42}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 41}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 41}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 41}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "c1sdVrol1FwL+cmYwb6wP6"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, 64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 41}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 42}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "95+8YLWXpP8ZuQD0IShkmZ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "12", "_parent": {"__id__": 40}, "_children": [{"__id__": 44}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 43}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 43}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 43}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "f3NKQGOzVOZYu9MWJnr6Fe"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-64, 64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 43}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 44}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "edPI8dahlCZouhcW+e4aVf"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "13", "_parent": {"__id__": 40}, "_children": [{"__id__": 46}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 45}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 45}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 45}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "f8m+UpgHhNZrEcceh+FWmz"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 45}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 46}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "d34o0WDcNIwYdrIQm75wgU"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "15", "_parent": {"__id__": 40}, "_children": [{"__id__": 48}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 47}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 47}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 47}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "e6UdCk1c9OxqmO8q8aWR6x"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, 64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 47}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 48}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "50dG1cq4tE176HhzmMUsxY"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "21", "_parent": {"__id__": 40}, "_children": [{"__id__": 50}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 49}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 49}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 49}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "a67sk1uLBCua7ApL0ryjkr"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 49}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 50}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "f3FyNo1pJD+I/761yz7tGt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "22", "_parent": {"__id__": 40}, "_children": [{"__id__": 52}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 51}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 51}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 51}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "bdSJk+TjFEuK2vCVxdTt+e"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-64, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 51}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 52}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "aecFdxLDVIJIHxjKFk0LmM"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "23", "_parent": {"__id__": 40}, "_children": [{"__id__": 54}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 53}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 53}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 53}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "baxnfA8XtD56cxJjtPife7"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 53}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 54}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "10VqF7GCNOoLs7co3Xe/Mn"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "24", "_parent": {"__id__": 40}, "_children": [{"__id__": 56}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 55}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 55}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 55}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "9fJ6Eb63lHhr9hdMT/k9xm"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [64, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 55}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 56}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "20bbbuuERHkYEr6vrT9752"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "25", "_parent": {"__id__": 40}, "_children": [{"__id__": 58}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 57}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 57}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 57}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "42JaIwOXpKnJjnW+iHo9Vf"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 57}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 58}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "5aMt6sHkFF76qf7ICrqzFc"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "31", "_parent": {"__id__": 40}, "_children": [{"__id__": 60}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 59}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 59}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 59}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "71km6ijlVELqZgGuZI2VXD"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 59}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 60}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "20/7Lqy4ZMqbJb3nKBUJU0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "32", "_parent": {"__id__": 40}, "_children": [{"__id__": 62}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 61}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 61}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 61}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "73lQuvB+9FPZ7PdQ0lTUib"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-64, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 61}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 62}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "c3tl9Ip21G+auMif2YIqQN"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "33", "_parent": {"__id__": 40}, "_children": [{"__id__": 64}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 63}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 63}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 63}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "b5/EcnQ4lGJL1WvXD99Wbu"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 63}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 64}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "62iKbxhgdLeYgwE3tUuEs0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "34", "_parent": {"__id__": 40}, "_children": [{"__id__": 66}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 65}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 65}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 65}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "42U6EOPtdCSLdD0KJknMFI"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [64, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 65}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 66}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "ffBng2rZRN1plSrczI/2zw"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "35", "_parent": {"__id__": 40}, "_children": [{"__id__": 68}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 67}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 67}, "_friction": 0, "_size": {"__type__": "cc.Size", "width": 60, "height": 60}}, {"__type__": "da79aYaTVNNboQCLF7+nrA4", "node": {"__id__": 67}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "cegebEiAFMEawTmMlOlgoF"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 67}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 68}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "98rd2eXb5A27GxMZcoOZWB"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "2b29nlYk5GfKWrANuHUl9T"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDoorEnd", "_parent": {"__id__": 2}, "_children": [{"__id__": 70}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 69}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 69}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 62}, "_size": {"__type__": "cc.Size", "width": 100, "height": 124}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 69}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "33SFYFGF9JJ4aGHD2LP+YK"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [430, -122, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 69}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 70}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 70}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "3ban9fcGlJvLjbco1WBc+Y"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeDoorLock", "_parent": {"__id__": 2}, "_children": [{"__id__": 72}, {"__type__": "cc.Node", "_name": "CC_nodeKeyEndPos", "_parent": {"__id__": 71}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "f4rZHdMh5IWLxafwA+SwXm"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-7.285, 51.604, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "dbdsC9wJlAqLZ+NQiIPP5Y"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [430, -130, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b03", "_parent": {"__id__": 71}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 72}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2c662jxA5N9KNcJLsuU5BI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "e2muCdFwdNwq82A0mR6JaV"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeItemParent", "_parent": {"__id__": 2}, "_children": [{"__id__": 74}, {"__id__": 75}, {"__id__": 76}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}, {"__id__": 80}, {"__id__": 81}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "18bZCI841HM5LeIK8n33Rn"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "hat", "_parent": {"__id__": 73}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 74}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "918UQTTFVN0KIwJzhD451L"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 74}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 74}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 28}, "_size": {"__type__": "cc.Size", "width": 84, "height": 30}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 74}, "msgId": 9, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "01RhoWdTNOVISfg7IKF0ZI"}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-10, -140.464, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cloth", "_parent": {"__id__": 73}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 75}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7d+IUwV+1PLaK3pwh12Tvx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 75}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 75}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 60, "height": 40}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 75}, "msgId": 9, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "1fF7TK1LdAvbMmxABUcNNJ"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-205, -110.211, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "glass", "_parent": {"__id__": 73}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 76}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a3JBwU0kVHUqefbfVujcDM"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 76}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 76}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 10}, "_size": {"__type__": "cc.Size", "width": 60, "height": 20}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 76}, "msgId": 9, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "5c5PzCqBdPV51FGTiUiNyv"}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [138, -125, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "key1", "_parent": {"__id__": 73}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 77}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "62Uyr4PtNFhJ4yk8wLgs9G"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "90aRiP5dFOeq7MRAHT0r6o"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -111.166, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "key2", "_parent": {"__id__": 73}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 78}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f3ObqTO0FKwY7e5nkZ+3X6"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "7b7r0lKPFHGIYPFNcf+o/6"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -119.91, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "stone", "_parent": {"__id__": 73}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 79}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6fO7KMgFhCg7A1KcE8S5z0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "c1U+qqt+lEoZRk3Uwvcyxr"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ball", "_parent": {"__id__": 73}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 80}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "94rHPrbLREQI8I6QrRnaQ9"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 80}, "_type": 0, "bullet": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 80}, "_radius": 30}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "43/KMkWcZCebtnXXSTju44"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "lock", "_parent": {"__id__": 73}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 81}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2c662jxA5N9KNcJLsuU5BI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "dfliQAmgJAxIevlhSk0krk"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_node<PERSON>ey", "_parent": {"__id__": 2}, "_children": [{"__id__": 83}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 82}, "_type": 0, "_gravityScale": 2.9, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 82}, "_size": {"__type__": "cc.Size", "width": 70, "height": 40}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 82}, "msgId": 21, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "3b2oWsQnhHSpbkO4nIpmT0"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "b02", "_parent": {"__id__": 82}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 83}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3cnLoM3mNAjY9WmEUUMW7m"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "acVVSywi9FRLoj3F56oLFO"}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 85}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 84}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 84}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 84}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "8eCjbVtYhJ/7+EeY+k6PFz"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-430, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 85}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "f7XNNWWadDC56UU/m0WVVI"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 86}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "击碎砖块找到钥匙", "_N$string": "击碎砖块找到钥匙", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a6Op12AXpFhp9JGT9sifbl"}, "fileId": "b44OYsbIlJKJO4KhyFS9GI"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 190, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}]]