[{"__type__": "cc.SpriteFrame", "content": {"name": "s012", "texture": "56egeSdA5Ia7eyLMJuPPyZ", "rect": [0, 0, 134, 101], "offset": [0, 0], "originalSize": [134, 101], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s015", "texture": "239ya0EK1OJ6oyae1xZE1f", "rect": [0, 0, 44, 44], "offset": [0, 0], "originalSize": [44, 44], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "mapSlideBlock", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map001", "_children": [{"__id__": 2}, {"__id__": 59}, {"__id__": 60}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 17}, {"__id__": 18}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 44}, {"__id__": 46}, {"__id__": 47}, {"__id__": 53}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "55QMo6CWdLjZeLElGPQGL4"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s009", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "66YpSVyFhPbqI3wocU/lI2"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "443ST6+e5KPqhm/gMBdl8+"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [314.985, 140, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s009", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "66YpSVyFhPbqI3wocU/lI2"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "d3UyEf+uhGMrw29++92MJq"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [314.985, -140, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s006", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b9e/0oW1NFzJ9F/6TNPOb3"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "c2Eir+FO5FY7WgaxtBtTYg"}, "_contentSize": {"__type__": "cc.Size", "width": 736, "height": 498}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, -7, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s007", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "deDqzz5RlBDLXSqJ9T0EFV"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "e3fCrqJ8lPJ7DSDWkmu5V2"}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 10}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [37.799, 234.267, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s008", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "58V+FRiYRDLbCiOPb+GNmJ"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "51i2cI6C9Jv7CUFJTzhkaC"}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 900}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [512.333, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s007", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "deDqzz5RlBDLXSqJ9T0EFV"}, "_type": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "54gKHDZMZISrV1mTjpI8Dz"}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 10}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [300.287, -127.75, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s007", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "deDqzz5RlBDLXSqJ9T0EFV"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "285UtIbixOoYgTqcQGEzK3"}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 14}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [262.317, 55.66, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, -1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "s007", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "deDqzz5RlBDLXSqJ9T0EFV"}, "_type": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "651490Jd1Gip1xgLpf07C7"}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 10}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [300.829, 127.586, 0, 0, 0, 0, 1, -1, -1, 1]}}, {"__type__": "cc.Node", "_name": "s010", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2dXfwJfRBnK3Xe2hHg2bO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "0env+A7WhHnIijSwiMR7Vi"}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [230, 200, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s010", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2dXfwJfRBnK3Xe2hHg2bO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "d1R+foSxdHqLzEeUpNNn/m"}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [200, 200, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s010", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2dXfwJfRBnK3Xe2hHg2bO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "16lRwug1tMQagKMmlH/UqU"}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [170, 200, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s011", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ddBNafh71ATJCpW4v4cxDy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "3fX+OiR0NGGK1g0PehPH7s"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-414, -210, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s011", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ddBNafh71ATJCpW4v4cxDy"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "0bCehQJgFLuoFvCrJftmo9"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [216.16, -210, 0, 0, 0, 0, 1, -1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTitle", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "Label", "_N$string": "Label", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "50eTmIAQtGYImLHXH0PixH"}, "_color": {"__type__": "cc.Color", "r": 88, "g": 88, "b": 88}, "_contentSize": {"__type__": "cc.Size", "width": 97.87, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 178.848, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeShowPos", "_parent": {"__id__": 2}, "_children": [{"__id__": 19}, {"__id__": 31}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "14zaKQkytEQZar+qInKsB2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 18}, "_children": [{"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 19}, "_layoutSize": {"__type__": "cc.Size", "width": 200, "height": 64}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "51pJm0S+pOg4BYZJy1RdsF"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-346, 190, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "0", "_N$string": "0", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "69ZzUSktJP/JUUDM+2U4hn"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-68, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "1", "_N$string": "1", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "d8jrYlLPRDmZr3B2oIRvR5"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "2", "_N$string": "2", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "355JIIZZVNpoD3MCWLLqnB"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [60, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "3", "_N$string": "3", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "04CPjx/5hEaYgmtuZthZ03"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [124, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "4", "_N$string": "4", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "97GEEwYA5LJqrnO1xhtZK8"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [188, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "5", "_N$string": "5", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "131vTzG7ZNeI7qWmUAKeO/"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [252, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "6", "_N$string": "6", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "0cbeRSPwtEBpn3wMBAvHfX"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [316, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "7", "_N$string": "7", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "ca/Z351CtJwIUB/XWqYIGz"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [380, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "8", "_N$string": "8", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "11jJMVJzBExoRkrGXocc4x"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [444, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "9", "_N$string": "9", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "fcT/chg5pO0YM0OpQTq1Rw"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [508, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "10", "_N$string": "10", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "6bB2xFE3VFSb7jgjovlJr9"}, "_color": {"__type__": "cc.Color", "r": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [572, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 18}, "_children": [{"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 31}, "_layoutSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "e5GsHlHZ1A0a6xpOweepSY"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-414, 126.18, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "1", "_N$string": "1", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "a8lpBakuxNZ4gRwvWe1Q6s"}, "_color": {"__type__": "cc.Color", "g": 46, "b": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "2", "_N$string": "2", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "2atWkoyjVBWrhvCXTwybzX"}, "_color": {"__type__": "cc.Color", "g": 46, "b": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "3", "_N$string": "3", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "d30MEhDEpHK5JO6S9/yLoU"}, "_color": {"__type__": "cc.Color", "g": 46, "b": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -128, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "4", "_N$string": "4", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "9aiRRQDyBAdZDTn7c7dHi/"}, "_color": {"__type__": "cc.Color", "g": 46, "b": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -192, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "5", "_N$string": "5", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "d2c/J5EDJHcYZn2nlZW2Xf"}, "_color": {"__type__": "cc.Color", "g": 46, "b": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -256, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "6", "_N$string": "6", "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "1dyNFsct1LWbLVFez1yV47"}, "_color": {"__type__": "cc.Color", "g": 46, "b": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -320, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeMapGrid", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Graphics", "node": {"__id__": 38}, "_materials": [{"__uuid__": "a1U5RdJRFMFL57BdJC9H1X"}], "_fillColor": {"__type__": "cc.Color", "r": 245, "g": 255}}, {"__type__": "d33abrIs7VKRINIWw2zqpZV", "node": {"__id__": 38}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "ebZpwIFCVBy46r+PqwQY+h"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTemplateBlock", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc5lKR4eFJSIXXsSTjYlbh"}, "_sizeMode": 0}, {"__type__": "234884mmrxBSKwqFi+xbzaU", "node": {"__id__": 39}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "b5L/Q42xtGcLo9tD1amfQX"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 58}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1364, -99, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTemplateGear", "_parent": {"__id__": 2}, "_children": [{"__id__": 41}], "_components": [{"__type__": "4d7bbqjG19IGJSTmYDpNP0d", "node": {"__id__": 40}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "84QtwM1kJBcIaB8NGdHT3v"}, "_color": {"__type__": "cc.Color", "g": 255, "b": 5}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1363.998, -230.859, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "slidblockgear", "_parent": {"__id__": 40}, "_children": [{"__id__": 42}, {"__id__": 43}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 41}, "_defaultClip": {"__uuid__": "afnFbhEWNMHryzMt9zRTz5"}, "_clips": [{"__uuid__": "afnFbhEWNMHryzMt9zRTz5"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "281Jyj+FhNn5sPtXhKgjPR"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s021", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "80eloXLShPMI6Du9ky41oe"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "aaTfp1LRZAK6Mc9bdctzEw"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s020", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2fhvT3WJhDOrErfoVRA53L"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "de/WT7rYxAt6WooTDz91t+"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTemplateBomb", "_parent": {"__id__": 2}, "_children": [{"__id__": 45}], "_components": [{"__type__": "f0024DUZhxLY7HGX2a4amCa", "node": {"__id__": 44}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "baGc9RUypNRbJl3Z1uGYE+"}, "_color": {"__type__": "cc.Color", "r": 86, "g": 86, "b": 86}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1363.998, -418.821, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s022", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9dzg8KCO9BcKJ5VWuKqoZ9"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "9bk/xp5EhBAYaT91qCOJTk"}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTempDir", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98FBPVr2pHvpjTZ0k/AGuI"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "1dmqVanrhMHIskvK3Iq8D4"}, "_contentSize": {"__type__": "cc.Size", "width": 36.8, "height": 43.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1364, -99, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRandomClean", "_parent": {"__id__": 2}, "_children": [{"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 52}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "5aivcIoLtFAID4nidT7nat"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 102}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [409.56, 48.731, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s012", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "06G/oWMMVLR6/PCmrG1Kx+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "130PI1vLZBUoaLW/IAngSW"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s017", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2+uULdyBEuJWGl8RjVvhW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "beT/n/T31Cdp81YR/bb7Zu"}, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -19, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s014", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f4YCb00sRE8Y8Onj9zGlpF"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "93RnUo0OFOxIBahEMTvxW+"}, "_contentSize": {"__type__": "cc.Size", "width": 56, "height": 57}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 27, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s015", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "11RI9L5ZRGZqtGMwQbEnaq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "2eUy/2gFxAXKy4Hl18kXhN"}, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [54, -24, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "50", "_N$string": "50", "_fontSize": 25, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "04yD1vQVlNrb4U69+EqZM2"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 27.81, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [54, -24, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRandomUnset", "_parent": {"__id__": 2}, "_children": [{"__id__": 54}, {"__id__": 55}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "226TyxnD9EGbnJilwZU8y9"}, "_color": {"__type__": "cc.Color", "g": 255, "b": 240}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 102}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [409.56, -117.398, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s012", "_parent": {"__id__": 53}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "06G/oWMMVLR6/PCmrG1Kx+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "1eCOSs1XFF+J+2/AUTKa9R"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s013", "_parent": {"__id__": 53}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 55}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7beKJAuiZClaAk2RS0f6Tp"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "22c8nHrCdNfIy8fysyG85D"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 66}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 34, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s016", "_parent": {"__id__": 53}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6bEp3FYWlDopbh6sbPzAI7"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "d4B2pcvJhEO7Irl0O4gH1V"}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 29}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -16.88, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s015", "_parent": {"__id__": 53}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "11RI9L5ZRGZqtGMwQbEnaq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "6aBw+ErPhKqLajyUgL3MQD"}, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [54, -23, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 53}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "50", "_N$string": "50", "_fontSize": 25, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "2c1oHrsndGRLdZJ1yzxpIO"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 27.81, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [54, -24, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTipIcon", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2dXfwJfRBnK3Xe2hHg2bO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "97KaytAnVO+K9EDsVAkjRe"}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1360.28, -655.25, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_progressBar", "_parent": {"__id__": 1}, "_children": [{"__id__": 61}, {"__id__": 63}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "26x5wYlTFGP4MecOqxROwz"}, "_sizeMode": 0}, {"__type__": "cc.ProgressBar", "node": {"__id__": 60}, "_N$totalLength": 240, "_N$barSprite": {"__id__": 62}, "_N$progress": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "90se4k3mJOi40RgXXpjWC/"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-74.001, -204.64, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 60}, "_components": [{"__id__": 62}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "9cYrC7ibNB0pgyeH2fpC+u"}, "_contentSize": {"__type__": "cc.Size", "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f5S/6Lbx1KGYijPu+j/N7P"}, "_type": 2, "_sizeMode": 0, "_fillRange": 1}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 60}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 63}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "Label", "_N$string": "Label", "_fontSize": 20, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1247qAqXxOC7gjoG+dj9or"}, "fileId": "49G0WuuVlABIzzHb5wfkwM"}, "_contentSize": {"__type__": "cc.Size", "width": 52.24, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "s019", "texture": "c6HExJRDxE56BLmHhe/f//", "rect": [0, 0, 240, 30], "offset": [0, 0], "originalSize": [240, 30], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s024", "texture": "dfIznMrRtL9Z/dSgHHi9lJ", "rect": [0, 0, 58, 64], "offset": [0, 0], "originalSize": [58, 64], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s016", "texture": "6flUC3bARJqLQn2fxbty3M", "rect": [0, 0, 62, 29], "offset": [0, 0], "originalSize": [62, 29], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s013", "texture": "bdctFbI1xGO76u44/afUgK", "rect": [0, 0, 66, 66], "offset": [0, 0], "originalSize": [66, 66], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s023", "texture": "94M6L5p8JFzJ6sdkTy6AE0", "rect": [0, 0, 58, 64], "offset": [0, 0], "originalSize": [58, 64], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s003", "texture": "916fUFnN1KEYlNLxRa36k4", "rect": [9, 5, 46, 54], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s022", "texture": "1csIvX21VPjZesiNXoYucm", "rect": [0, 0, 58, 64], "offset": [0, 0], "originalSize": [58, 64], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s010", "texture": "15KsqDrB5IA6WyT1ZNWA41", "rect": [0, 0, 20, 20], "offset": [0, 0], "originalSize": [20, 20], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s017", "texture": "53owHiDxJOH48b+HCZoxax", "rect": [0, 0, 59, 32], "offset": [0, 0], "originalSize": [59, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s011", "texture": "c19IeuRZVL+JBk6e0H/DiP", "rect": [0, 0, 50, 24], "offset": [0, 0], "originalSize": [50, 24], "capInsets": [20, 0, 20, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s014", "texture": "e2Mb7G82dL5Kv9G7rheeR3", "rect": [0, 0, 56, 57], "offset": [0, 0], "originalSize": [56, 57], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s018", "texture": "871ePZQwhN7pjiPJpJvboN", "rect": [0, 0, 240, 30], "offset": [0, 0], "originalSize": [240, 30], "capInsets": [0, 0, 0, 0]}}]