[{"__type__": "cc.AnimationClip", "_name": "map11034trap03Anim", "_duration": 0.13333333333333333, "curveData": {"props": {"scaleX": [{"frame": 0, "value": 1}, {"frame": 0.016666666666666666, "value": 0.5625}, {"frame": 0.11666666666666667, "value": 1.25}, {"frame": 0.13333333333333333, "value": 1}]}}}, {"__type__": "cc.AnimationClip", "_name": "map11034doorDownAnim", "_duration": 0.3, "curveData": {"props": {"y": [{"frame": 0, "value": 400, "curve": "cubicOut"}, {"frame": 0.25, "value": 160}, {"frame": 0.3, "value": 175}]}}}, {"__type__": "cc.AnimationClip", "_name": "map11033portal01Anim", "curveData": {"props": {"x": [{"frame": 0, "value": 440.17}], "y": [{"frame": 0, "value": 158.817}]}}}, {"__type__": "cc.AnimationClip", "_name": "map11034trap02Anim", "_duration": 0.2, "curveData": {"paths": {"men": {"props": {"scaleY": [{"frame": 0, "value": 0.8}, {"frame": 0.016666666666666666, "value": 0.45}, {"frame": 0.11666666666666667, "value": 0.9}, {"frame": 0.13333333333333333, "value": 0.8}]}}, "box1": {"props": {"y": [{"frame": 0, "value": 140}, {"frame": 0.03333333333333333, "value": 120}, {"frame": 0.2, "value": 140}]}}, "box2": {"props": {"y": [{"frame": 0, "value": 120}, {"frame": 0.016666666666666666, "value": 140}, {"frame": 0.03333333333333333, "value": 120}]}}}}}, {"__type__": "cc.AnimationClip", "_name": "map11034trap03RoleAnim", "_duration": 0.3333333333333333, "curveData": {"props": {"x": [{"frame": 0, "value": 358.84, "curve": [0.5653439153439153, -0.009920634920635107, 0.5958994708994709, 0.44047619047619047]}, {"frame": 0.3333333333333333, "value": -161.382}]}}}, [{"__type__": "cc.Prefab", "_name": "map11034", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "11034", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 12}, {"__id__": 16}, {"__id__": 24}, {"__id__": 25}, {"__id__": 33}, {"__id__": 31}, {"__id__": 54}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTips", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "24+f2lyxREZ62fHEZWB+8m"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23.423, -27.327, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "tips", "_parent": {"__id__": 3}, "_children": [{"__id__": 5}, {"__id__": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "92PLK2GmhOV41moSdABxEo"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [182.379, 0, 219.97045, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 4}, "_children": [{"__id__": 6}, {"__id__": 7}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 5}, "_enabled": false, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "b4Mfjbj+tOnpiOSQzN5rHA"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-428.667, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a20", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fd4iXgx4hNG7rVTsSGSEWm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "78Jra+tTdLvI0HlflbaLz7"}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "向右跳到门上", "_N$string": "向右跳到门上", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "c8dQ6KTYhLeIv3XsNs//F5"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 74.21, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 4}, "_children": [{"__id__": 9}, {"__id__": 10}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 8}, "_enabled": false, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "8cNMUP9/pFGatj788ZrkgJ"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [110.092, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a20", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fd4iXgx4hNG7rVTsSGSEWm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "d4Hgz7t81Ow6pt5/CKntL2"}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "向右跳到门上", "_N$string": "向右跳到门上", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "164a05zwpJ567Tb5vFJv0r"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 74.21, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "还想我上当？", "_N$string": "还想我上当？", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "a3Z0S6kvJIfpSMwPKHvl1i"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 180, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "door", "_parent": {"__id__": 2}, "_children": [{"__id__": 13}, {"__id__": 14}, {"__id__": 15}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 12}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 12}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 50}, "_size": {"__type__": "cc.Size", "width": 80, "height": 100}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 12}}, {"__type__": "cc.Animation", "node": {"__id__": 12}, "_clips": [{"__uuid__": "12P0ELll5MmYv4pX6TSW3d"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "4cIEq4dZVKs6OYU9fQrn8Q"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [409.408, 590.126, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "tie", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19enN3cblO7K+WZJGTLBIW"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "76x5KrXlFGyrJ2DtIf8uj4"}, "_contentSize": {"__type__": "cc.Size", "width": 22, "height": 640}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 397.44, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 12}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 14}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 14}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "ed9zuWCilNZa29NFvGzvrc"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 15}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "8dazftxM5HEKqcDcsnN2kO"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 118.66, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap02", "_parent": {"__id__": 2}, "_children": [{"__id__": 17}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 16}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 16}, "_offset": {"__type__": "cc.Vec2", "y": 30}, "_size": {"__type__": "cc.Size", "width": 60, "height": 100}}, {"__type__": "cc.Animation", "node": {"__id__": 16}, "_defaultClip": {"__uuid__": "76o9QBPNxNrIuV9I2YuNVQ"}, "_clips": [null, {"__uuid__": "79gHqGlMRN/LN+ANvXdlpI"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "adaX5K6ndHuY1OzOhAxGVG"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -322, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "platform", "_parent": {"__id__": 16}, "_children": [{"__id__": 18}, {"__id__": 19}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "62CPPyW8xIMLx6z8S34R0L"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.272, -15.432, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h57", "_parent": {"__id__": 17}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8bqREWFvpOuJw4h2zxDOnT"}}, {"__type__": "cc.Widget", "node": {"__id__": 18}, "_alignFlags": 8, "_left": 40}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "5bpwfdngZBxbMrahmUog8F"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-20, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h57", "_parent": {"__id__": 17}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8bqREWFvpOuJw4h2zxDOnT"}}, {"__type__": "cc.Widget", "node": {"__id__": 19}, "_alignFlags": 32, "_right": 40}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "46foc0prBNf52hATns0oSD"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [20, 0, 0, 0, 0, 0, 1, -1, 1, -1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 16}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 20}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 20}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "7dyXUiOalFDb1hNhCr3W+n"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 21}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "d04RC2aBNIP6wi+DD5YzCK"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 118.66, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "box1", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 22}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 22}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -60}, "_size": {"__type__": "cc.Size", "width": 60, "height": 20}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 22}, "tagetNode": {"__id__": 16}, "animClips": {"__uuid__": "79gHqGlMRN/LN+ANvXdlpI"}, "count": 99}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "560UO2hS9LA4Vd9Rr4ddhq"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 140, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "box2", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 23}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 23}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -60}, "_size": {"__type__": "cc.Size", "width": 60, "height": 20}}, {"__type__": "984a8I7wqVKmpewbDENDooS", "node": {"__id__": 23}, "forece": {"__type__": "cc.Vec2", "y": 2100}, "forceGap": 0.4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "26egcM3uxAdqt3SZypII4g"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "tiggerActionNode03", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 24}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 24}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 100}, "_size": {"__type__": "cc.Size", "width": 10, "height": 300}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 24}, "tagetNode": {"__id__": 12}, "animClips": {"__uuid__": "12P0ELll5MmYv4pX6TSW3d"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "85xwdwBcdOU41lMA9i2mHL"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [365.647, -91.866, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap03", "_parent": {"__id__": 2}, "_children": [{"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 25}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_offset": {"__type__": "cc.Vec2", "x": -35, "y": 30}, "_size": {"__type__": "cc.Size", "width": 60, "height": 100}}, {"__type__": "cc.Animation", "node": {"__id__": 25}, "_defaultClip": {"__uuid__": "76o9QBPNxNrIuV9I2YuNVQ"}, "_clips": [{"__uuid__": "10OnYliHpHWqESrAmWTZoy"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "6df/dnb/hHLIvJk2KjCR2n"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [443.988, -126.482, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 25}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 26}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 26}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "b260tmhWlJoLGR3+x1lSxP"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-35, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 27}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "ab+6spf4pKdpFq9kBOHhxu"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 118.66, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "box1", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 28}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 28}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -60}, "_size": {"__type__": "cc.Size", "width": 56, "height": 20}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 28}, "tagetNode": {"__id__": 25}, "animClips": {"__uuid__": "79gHqGlMRN/LN+ANvXdlpI"}, "count": 99}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "d8fJx0hLhDvIKqnzLR2RND"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-35, 140, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "box2", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 29}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 29}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -60}, "_size": {"__type__": "cc.Size", "width": 56, "height": 20}}, {"__type__": "984a8I7wqVKmpewbDENDooS", "node": {"__id__": 29}, "forece": {"__type__": "cc.Vec2", "y": 2100}, "forceGap": 0.4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "ccz7bvMsVAb7mlyp2Wtt8Y"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-35, 120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "box3", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 30}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 30}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 20, "height": 70}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 30}, "tagetNode": {"__id__": 25}, "animClips": {"__uuid__": "10OnYliHpHWqESrAmWTZoy"}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 30}, "tagetNode": {"__id__": 31}, "animClips": {"__uuid__": "e7TatOlqRApZDB00Lkbvei"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "4eEK79aUtN5botUH7oh0O8"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-65, 40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 32}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 31}, "_allowSleep": false, "_gravityScale": 4.5, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 31}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 26, "height": 70}}, {"__type__": "bb3e1rlAgNFSI8IZkNdeApj", "node": {"__id__": 31}}, {"__type__": "cc.Animation", "node": {"__id__": 31}, "_defaultClip": {"__uuid__": "39IqoaWbpCH5TmGk+kaupb"}, "_clips": [null, {"__uuid__": "e7TatOlqRApZDB00Lkbvei"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "8dFZdjiwZGmqIlAq99b9uQ"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-477.779, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "f5e61cK15AC7VskjnmDq3e"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "ground", "_parent": {"__id__": 2}, "_children": [{"__id__": 34}, {"__id__": 44}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "6dGLBABsZHkJcIxqUBUkyg"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groud01", "_parent": {"__id__": 33}, "_children": [{"__id__": 35}, {"__id__": 36}, {"__id__": 42}, {"__id__": 43}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 34}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 34}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 320, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "500oc4IKdI553L/hdcNJQP"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 34}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 35}, "_alignFlags": 45, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "19jNhx4tlFg5+nBqrvJJe8"}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 34}, "_children": [{"__id__": 37}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 36}, "_layoutSize": {"__type__": "cc.Size", "width": 256, "height": 64}, "_N$layoutType": 1}, {"__type__": "cc.Widget", "node": {"__id__": 36}, "_alignFlags": 40, "_right": 64, "_originalWidth": 512}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "9akfKy0WBF6qh03JWYXHMR"}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 36}, "_children": [{"__id__": 38}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "68Xr58A4pMyJIq+KZAGkoO"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 37}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "0ajOZz3DJCCIsuJShApeZ3"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "bbsrbgBw1FoJsU03B5GWSJ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "49O2xyIMJCDIfYfZ+G30PS"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "e4q0O7T/JOHKI7w8q9YIOW"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 34}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 42}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "55SEdrMJ9DZ42zdxU3VHiH"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 34}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 43}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "77aTkJwuRPyJU7QD9dUPHs"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groud01", "_parent": {"__id__": 33}, "_children": [{"__id__": 45}, {"__id__": 46}, {"__id__": 52}, {"__id__": 53}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 44}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 44}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 320, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "a7ylgmeGNNKZZwBFsq0/tW"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 45}, "_alignFlags": 45, "_left": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "99IlpRnCdPh5bNKLs2yiIC"}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 44}, "_children": [{"__id__": 47}, {"__id__": 49}, {"__id__": 50}, {"__id__": 51}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 46}, "_layoutSize": {"__type__": "cc.Size", "width": 256, "height": 64}, "_N$layoutType": 1, "_N$affectedByScale": true}, {"__type__": "cc.Widget", "node": {"__id__": 46}, "_alignFlags": 40, "_left": 64, "_originalWidth": 768}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "afCgzz3yZNJZVpTm4jnpUX"}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 46}, "_children": [{"__id__": 48}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "7fBEp7GbtKsoVoqFk/Qz9d"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "d2afiysPJFMq33fwMyeT8s"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "f1LIUztktGAbYBvBsQP+1t"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "540SowDCNFu6unmvt5Lzoq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "7bS4M4C5xLZosPbdPpt5YI"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 52}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "f8byJ1aItNXZLt73HWOk9f"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 53}, "_alignFlags": 13, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "55B4dtghJIJprNWgO+bK28"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "Dead", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 54}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 54}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 1280, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 54}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "ecXO8YPVVKbpBSbZYEDohY"}, "fileId": "08l77MLFZEs4q8rkitB9Lk"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -430, 0, 0, 0, 0, 1, 1.25, 1.25, 1.25]}, "_groupIndex": 2, "groupIndex": 2}]]