[[{"__type__": "cc.Prefab", "_name": "map11048", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "11048", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}, {"__id__": 10}, {"__id__": 13}, {"__id__": 19}, {"__id__": 14}, {"__id__": 27}, {"__id__": 61}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTips", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "24+f2lyxREZ62fHEZWB+8m"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23.423, -27.327, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "tips", "_parent": {"__id__": 3}, "_children": [{"__id__": 5}, {"__id__": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "92PLK2GmhOV41moSdABxEo"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [182.379, 0, 219.97045, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 4}, "_children": [{"__id__": 6}, {"__id__": 7}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 5}, "_enabled": false, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "b4Mfjbj+tOnpiOSQzN5rHA"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-109.174, -52.75, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc copy", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "向右跳", "_N$string": "向右跳", "_fontSize": 32, "_lineHeight": 32, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "78Bc7tTMxAq4nCpsA7ibAy"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 40.32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-23.635, 100.566, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a20", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fd4iXgx4hNG7rVTsSGSEWm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "d4cOsEcCpDIa+7qYhsC2Xu"}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-22.191, 48.198, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "1 copy", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Layout", "node": {"__id__": 8}, "_enabled": false, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "fbDH3vI6JGnL5ohVCxXmTM"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 168.656, 0, 0, 0, 1, 6.123233995736766e-17, 1.25, 1.25, 1.25]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "蹦蹦跳跳2", "_N$string": "蹦蹦跳跳2", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "a3Z0S6kvJIfpSMwPKHvl1i"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 182.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 260, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "door", "_parent": {"__id__": 2}, "_children": [{"__id__": 11}, {"__id__": 12}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 10}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 10}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 50}, "_size": {"__type__": "cc.Size", "width": 80, "height": 100}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 10}}, {"__type__": "cc.Animation", "node": {"__id__": 10}, "_clips": [{"__uuid__": "e4HoUIZ09GXL4SMcW5fMqP"}, {"__uuid__": "7bAoGk9chGP5SkZdFvsbek"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "4cIEq4dZVKs6OYU9fQrn8Q"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [460, -122, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 10}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 11}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 11}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "ed9zuWCilNZa29NFvGzvrc"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 12}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "8dazftxM5HEKqcDcsnN2kO"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 118.66, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "tiggerActionNode01", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 13}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 13}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 20, "height": 150}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 13}, "tagetNode": {"__id__": 14}, "animClips": {"__uuid__": "21K6soD+JJ8ZEdfkPyXs53"}, "delayTime": 0.2}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 13}, "animClips": {"__uuid__": "19/RSR1ulC7LBJNBxy0FBt"}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 13}, "animClips": {"__uuid__": "6ceoHN0chAM4/MpibAL6qg"}, "delayTime": 12}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "e02F6GNZpN3LAGOUU9jSXY"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [115, -44, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap98", "_parent": {"__id__": 2}, "_children": [{"__id__": 15}, {"__id__": 18}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 14}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 14}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 128, "height": 320}}, {"__type__": "cc.Animation", "node": {"__id__": 14}, "_clips": [{"__uuid__": "21K6soD+JJ8ZEdfkPyXs53"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "86AiNk7mFOJYv5qGwZ+N7R"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [152, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 14}, "_children": [{"__id__": 16}, {"__id__": 17}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 15}, "_layoutSize": {"__type__": "cc.Size", "width": 128, "height": 64}, "_resize": 1, "_N$layoutType": 1}, {"__type__": "cc.Widget", "node": {"__id__": 15}, "_alignFlags": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "b5MQOL/slDmZBn0NnrHsKS"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 15}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "8cG83gUdFP8Z5WnipziX+E"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 15}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "0ev2oC6qtE05pwIJhXhqay"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 18}, "_alignFlags": 45, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "19E0akvFdEhLMSQSyZG9m2"}, "_contentSize": {"__type__": "cc.Size", "width": 128, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap01", "_parent": {"__id__": 2}, "_children": [{"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 19}, "_layoutSize": {"__type__": "cc.Size", "width": 980, "height": 100}, "_N$layoutType": 1, "_N$paddingLeft": 30, "_N$spacingX": 80}, {"__type__": "cc.Animation", "node": {"__id__": 19}, "_defaultClip": {"__uuid__": "9dpZvfnu1JR5Hx+yuUqBEB"}, "_clips": [{"__uuid__": "9dpZvfnu1JR5Hx+yuUqBEB"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "22bteKKhZJYoMeg83WTxDA"}, "_contentSize": {"__type__": "cc.Size", "width": 980, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 20}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 20}, "_sensor": true, "_radius": 30}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 20}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "96z+5ebBFJTqBBa9eJhHaD"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-428, 200, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "2", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 21}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 21}, "_sensor": true, "_radius": 30}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 21}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "1eNJxOndFFS59zShTAzsLD"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-284, -156, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "3", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 22}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 22}, "_sensor": true, "_radius": 30}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 22}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "1dIZRpCZFGOZtWn9aqFrm0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-140, 200, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "4", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 23}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 23}, "_sensor": true, "_radius": 30}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 23}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "6fDvIXhvBDfL8NrHH3NZBy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [4, -156, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "5", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 24}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 24}, "_sensor": true, "_radius": 30}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 24}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "28Sg1ySV1Aoqo1Hg8HpxFI"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [148, 200, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "6", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 25}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 25}, "_sensor": true, "_radius": 30}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 25}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "840drhi+lB74xv/weLGTN1"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [292, -156, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "7", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 26}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 26}, "_sensor": true, "_radius": 30}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 26}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "bcKXA1EZJKOp3/HdmECS0e"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [436, 200, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "ground", "_parent": {"__id__": 2}, "_children": [{"__id__": 28}, {"__id__": 41}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "6dGLBABsZHkJcIxqUBUkyg"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groud01", "_parent": {"__id__": 27}, "_children": [{"__id__": 29}, {"__id__": 30}, {"__id__": 39}, {"__id__": 40}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 28}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 28}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 512, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "500oc4IKdI553L/hdcNJQP"}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [472, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 29}, "_alignFlags": 45, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "19jNhx4tlFg5+nBqrvJJe8"}, "_contentSize": {"__type__": "cc.Size", "width": 448, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 28}, "_children": [{"__id__": 31}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 30}, "_layoutSize": {"__type__": "cc.Size", "width": 512, "height": 64}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "9akfKy0WBF6qh03JWYXHMR"}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 30}, "_children": [{"__id__": 32}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "fa8Kpf+RNHb5N+BujwTNml"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "20z9G2wlRGBav6ujdto7+7"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "47kzW0JfxE6oIkdglB4Ho5"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "4aCsip9XFLaqZTHTGiRj3N"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "fcocgnSgpG6YBvY7DmDgT0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "96hvzNjjRKaLCTX/YZPNzl"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "cejI71/vFBi5iArEUcrWbw"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "20btQ9BjxP6p2fXa+5dKu3"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 39}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "55SEdrMJ9DZ42zdxU3VHiH"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 40}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "77aTkJwuRPyJU7QD9dUPHs"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groud01", "_parent": {"__id__": 27}, "_children": [{"__id__": 42}, {"__id__": 43}, {"__id__": 59}, {"__id__": 60}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 41}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 41}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 896, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "a7ylgmeGNNKZZwBFsq0/tW"}, "_contentSize": {"__type__": "cc.Size", "width": 896, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-360, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 42}, "_alignFlags": 45, "_left": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "99IlpRnCdPh5bNKLs2yiIC"}, "_contentSize": {"__type__": "cc.Size", "width": 832, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 41}, "_children": [{"__id__": 44}, {"__id__": 46}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 51}, {"__id__": 52}, {"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 43}, "_layoutSize": {"__type__": "cc.Size", "width": 768, "height": 64}, "_N$layoutType": 1, "_N$affectedByScale": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "afCgzz3yZNJZVpTm4jnpUX"}, "_contentSize": {"__type__": "cc.Size", "width": 768, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 43}, "_children": [{"__id__": 45}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "7fBEp7GbtKsoVoqFk/Qz9d"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "d2afiysPJFMq33fwMyeT8s"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "f1LIUztktGAbYBvBsQP+1t"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "540SowDCNFu6unmvt5Lzoq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "7bS4M4C5xLZosPbdPpt5YI"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 43}, "_children": [{"__id__": 50}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "e1Kfxk7cFCl5w8XaGn+0NN"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "18Lfy6pydIv4p2i7G4D2yk"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "5es0r/NJNOGJ0/Wje6F+m0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "26MWrV5ItEwpgaAW3Qr/9p"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "e8Sqn3iAlBaZjTxg5TbzDU"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "cbavi8hddCm6P8pVqoNZJo"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 55}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "77eGRWdHpERZ7wykkDayVP"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "7bmbYSY+RGp6jW3naa2WtL"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "c0fCrZTrpLzKS5Njv9nbUY"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "0bU0uKvPhNU53p3TbTh6bq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 59}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "f8byJ1aItNXZLt73HWOk9f"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 60}, "_alignFlags": 13, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "55B4dtghJIJprNWgO+bK28"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 62}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 61}, "_allowSleep": false, "_gravityScale": 4.5, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 61}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 26, "height": 70}}, {"__type__": "bb3e1rlAgNFSI8IZkNdeApj", "node": {"__id__": 61}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "8dFZdjiwZGmqIlAq99b9uQ"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-524, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 61}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4bxfgaw7ZJQ6n9yYouPvUq"}, "fileId": "f5e61cK15AC7VskjnmDq3e"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}], {"__type__": "cc.AnimationClip", "_name": "map11048trap01Anim", "_duration": 1.1666666666666667, "wrapMode": 22, "curveData": {"paths": {"1": {"props": {"y": [{"frame": 0, "value": 200, "curve": "sineIn"}, {"frame": 1.1666666666666667, "value": -156}]}}, "2": {"props": {"y": [{"frame": 0, "value": -156, "curve": "sineOut"}, {"frame": 1.1666666666666667, "value": 200}]}}, "3": {"props": {"y": [{"frame": 0, "value": 200, "curve": "sineIn"}, {"frame": 1.1666666666666667, "value": -156}]}}, "4": {"props": {"y": [{"frame": 0, "value": -156, "curve": "sineOut"}, {"frame": 1.1666666666666667, "value": 200}]}}, "5": {"props": {"y": [{"frame": 0, "value": 200, "curve": "sineIn"}, {"frame": 1.1666666666666667, "value": -156}]}}, "6": {"props": {"y": [{"frame": 0, "value": -156, "curve": "sineOut"}, {"frame": 1.1666666666666667, "value": 200}]}}, "7": {"props": {"y": [{"frame": 0, "value": 200, "curve": "sineIn"}, {"frame": 1.1666666666666667, "value": -156}]}}}}}]