const fs = require('fs');
const path = require('path');
const https = require('https');
const decodeUuid = require("./cc/decode-uuid");

/**
 * 配置和全局变量
 */
class DownloadConfig {
    constructor() {
        this.downloadQueue = [];
        this.activeDownloads = 0;
        this.maxConcurrentDownloads = 5;
        this.downloadStats = {
            total: 0,
            completed: 0,
            failed: 0,
            skipped: 0
        };
        this.pendingFiles = []; // 存储待处理的文件信息
        this.downloadingFiles = new Set(); // 正在下载的文件路径集合
        this.maxRetries = 3;
        this.retryDelay = 1000;
        this.bundleStats = {}; // 存储bundle统计信息
        this.skippedCount = 0; // 跳过的文件计数
        
        this.validateArgs();
        this.initPaths();
    }
    
    validateArgs() {
        if (process.argv.length < 3) {
            console.log("请输入json文件路径");
            process.exit(1);
        }
        
        const fileType = process.argv[2].split(".").pop();
        if (fileType !== "json") {
            console.log("请输入json文件路径");
            process.exit(1);
        }
    }
    
    initPaths() {
        this.useMD5 = process.argv[3] ? process.argv[3] == "-noMd5" ? false : true : true;
        this.outPath = path.dirname(process.argv[2]);
        this.bundleDir = "cyd";
        this.urlDir = "assets";
        this.serverUrl = "https://h5games.joyapi.com/webgame/testgames/cyd/assets";
        // this.serverUrl = "http://127.0.0.1:8088/assets";		
        this.localPath = process.argv[2].substring(0, process.argv[2].lastIndexOf("/")) + "/assets";
        
        this.configData = JSON.parse(fs.readFileSync(process.argv[2]));
        this.bundleVers = this.configData.bundleVers;
        this.bundleConfigs = {};
    }
}

/**
 * 工具函数
 */
class Utils {
    static decodeUUID(uid) {
        if (uid.length > 9) {
            return decodeUuid(uid);
        }
        return uid;
    }
    
    static handleVersionMd5(data) {
        let result = {};
        for (let i = 0, len = data.length; i < len; i += 2) {
            result[data[i]] = data[i + 1];
        }
        return result;
    }
    
    static async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * 主下载管理器
 */
class DownloadManager {
    constructor(config) {
        this.config = config;
    }
    
    async start() {
        console.log("------------------开始下载------------------");
        console.log(`🚀 启用多线程下载，最大并发数: ${this.config.maxConcurrentDownloads}`);

        try {
            await this.loadAllBundles();
            console.log("✓ 所有bundle配置加载完成");

            await this.handleConfigs();

            // 在开始下载前打印统计信息
            this.printPreDownloadStats();

            // 等待所有下载完成
            await this.waitForAllDownloads();

            this.printFinalStats();

        } catch (error) {
            console.error("下载过程中发生错误:", error);
        }
    }
    
    async loadAllBundles() {
        return new Promise((resolve) => {
            if (this.config.bundleVers) {
                let bundleCount = Object.keys(this.config.bundleVers).length;
                
                for (let k in this.config.bundleVers) {
                    const configp = path.join(this.config.localPath, k, `config.${this.config.bundleVers[k]}.json`);
                    const configurl = `${this.config.serverUrl}/${k}/config.${this.config.bundleVers[k]}.json`;
                    
                    this.downloadFile(configurl, configp, (result) => {
                        if (result === true || result === "skipped") {
                            this.config.bundleConfigs[k] = JSON.parse(fs.readFileSync(configp));
                        }
                        bundleCount--;
                        if (bundleCount == 0) {
                            resolve();
                        }
                    });
                }
            } else {
                resolve();
            }
        });
    }
    
    async handleConfigs() {
        for (let k in this.config.bundleConfigs) {
            await this.handleConfig(k);
        }
    }
    async handleConfig(bundlename) {
        console.log(`✓ 处理bundle: ${bundlename}`);
        let bundleConfigData = this.config.bundleConfigs[bundlename];

        let bundleImportMd5 = Utils.handleVersionMd5(bundleConfigData.versions.import);
        let bundleNativeMd5 = Utils.handleVersionMd5(bundleConfigData.versions.native);

        let packids = [];
        let redirectids = [];

        // 处理redirect
        for(let i = 0; i < bundleConfigData.redirect.length; i += 2) {
            redirectids.push(bundleConfigData.redirect[i]);
        }

        // 处理packs
        const packPromises = [];
        for (let uid in bundleConfigData.packs) {
            packids = packids.concat(bundleConfigData.packs[uid]);
            let md5 = bundleImportMd5[uid];
            if (md5) {
                packPromises.push(this.handleDownloadFileDirect(bundlename + "/import", `${uid}.${md5}.json`));
            } else {
                console.log(`${bundlename}里的UID:${uid}未匹配md5`);
            }
        }

        // 处理uuids
        const uuidPromises = [];
        let processedCount = 0;
        for(let i = 0, len = bundleConfigData.uuids.length; i < len; i++) {
            let uid = bundleConfigData.uuids[i];
            let md5import = bundleImportMd5[i];
            let md5native = bundleNativeMd5[i];

            if(md5import) {
                uuidPromises.push(this.handleDownloadFile(bundlename + "/import", {id: Utils.decodeUUID(uid), md5: md5import}));
                processedCount++;
            }
            if(md5native) {
                uuidPromises.push(this.handleDownloadFile(bundlename + "/native", {id: Utils.decodeUUID(uid), md5: md5native}));
                processedCount++;
            }
        }

        // 等待所有下载任务准备完成
        await Promise.all([...packPromises, ...uuidPromises]);

        // 存储bundle统计信息，稍后统一打印
        this.config.bundleStats = this.config.bundleStats || {};
        this.config.bundleStats[bundlename] = {
            uuidsCount: bundleConfigData.uuids ? bundleConfigData.uuids.length : 0,
            importCount: bundleConfigData.versions.import ? bundleConfigData.versions.import.length / 2 : 0,
            nativeCount: bundleConfigData.versions.native ? bundleConfigData.versions.native.length / 2 : 0,
            processedCount: processedCount
        };
    }
    
    async handleDownloadFileDirect(filedir, filename) {
        const url = `${this.config.serverUrl}/${filedir}/${filename.substr(0, 2)}/${filename}`;
        const localpath = path.join(this.config.localPath, filedir, filename.substr(0, 2), filename);
        const exists = await this.checkUrlExists(url);
        
        if(exists) {
            // 检查文件是否已存在
            if (fs.existsSync(localpath)) {
                let file = fs.readFileSync(localpath);
                if (file.length > 0) {
                    this.config.pendingFiles.push({ status: 'skipped', path: localpath });
                    this.config.skippedCount++;
                    this.updateSkippedProgress();
                    return;
                }
            }
            
            // 检查文件是否正在下载中
            if (this.config.downloadingFiles.has(localpath)) {
                console.log(`⏳ 文件正在下载中: ${path.basename(localpath)}`);
                return;
            }
            
            this.config.pendingFiles.push({ status: 'pending', path: localpath });
            this.config.downloadingFiles.add(localpath);
            
            this.downloadFile(url, localpath, (result) => {
                // 从下载中集合移除
                this.config.downloadingFiles.delete(localpath);
                
                // 更新文件状态
                const fileInfo = this.config.pendingFiles.find(f => f.path === localpath);
                if (fileInfo) {
                    if (result === true) {
                        fileInfo.status = 'completed';
                    } else if (result === "skipped") {
                        fileInfo.status = 'skipped';
                    } else {
                        fileInfo.status = 'failed';
                    }
                }
            });
        }
    }
    
    async handleDownloadFile(filedir, filedata) {
        let ext = ["json"];
        if (filedir.endsWith("native")) {
            ext = ["png", "jpg", "jpeg", "gif", "webp", "mp3", "atlas", "plist"];
        }
        
        let filename = null;
        let url = null;
        let foundValidUrl = false;

        for (let k in ext) {
            if (foundValidUrl) break;

            filename = `${filedata.id}.${filedata.md5}.${ext[k]}`;
            url = `${this.config.serverUrl}/${filedir}/${filedata.id.substr(0, 2)}/${filename}`;

            const exists = await this.checkUrlExists(url);
            if (exists) {
                foundValidUrl = true;
            }
        }

        if (foundValidUrl) {
            const localpath = path.join(this.config.localPath, filedir, filedata.id.substr(0, 2), filename);
            
            // 检查文件是否已存在
            if (fs.existsSync(localpath)) {
                let file = fs.readFileSync(localpath);
                if (file.length > 0) {
                    this.config.pendingFiles.push({ status: 'skipped', path: localpath });
                    this.config.skippedCount++;
                    this.updateSkippedProgress();
                    return;
                }
            }
            
            // 检查文件是否正在下载中
            if (this.config.downloadingFiles.has(localpath)) {
                console.log(`⏳ 文件正在下载中: ${path.basename(localpath)}`);
                return;
            }
            
            this.config.pendingFiles.push({ status: 'pending', path: localpath });
            this.config.downloadingFiles.add(localpath);
            
            this.downloadFileWithRetry(url, localpath, (result) => {
                // 从下载中集合移除
                this.config.downloadingFiles.delete(localpath);
                
                // 更新文件状态
                const fileInfo = this.config.pendingFiles.find(f => f.path === localpath);
                if (fileInfo) {
                    if (result === true) {
                        fileInfo.status = 'completed';
                    } else if (result === "skipped") {
                        fileInfo.status = 'skipped';
                    } else {
                        fileInfo.status = 'failed';
                        console.log("✗ 下载失败:", path.basename(localpath));
                    }
                }
            });
        }
    }

    // 等待所有下载完成
    async waitForAllDownloads() {
        // 清除跳过文件的进度显示
        if (this.config.skippedCount > 0) {
            console.log(`\n✓ 跳过了 ${this.config.skippedCount} 个已存在的文件`);
        }

        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                // 检查是否所有文件都已处理完成（没有pending状态的文件）
                const pendingCount = this.config.pendingFiles.filter(f => f.status === 'pending').length;
                const hasActiveDownloads = this.config.downloadQueue.length > 0 || this.config.activeDownloads > 0;

                if (!hasActiveDownloads && pendingCount === 0) {
                    clearInterval(checkInterval);
                    console.log(`\n📥 所有下载任务已完成，正在生成统计报告...`);
                    resolve();
                }
            }, 100);
        });
    }
    
    // 打印下载前统计信息
    printPreDownloadStats() {
        console.log("\n==================== 下载统计 ====================");

        // 打印各bundle统计信息
        for (let bundlename in this.config.bundleStats) {
            const stats = this.config.bundleStats[bundlename];
            console.log(`📦 Bundle: ${bundlename}`);
            console.log(`   - uuids数量: ${stats.uuidsCount}`);
            console.log(`   - import版本数量: ${stats.importCount}`);
            console.log(`   - native版本数量: ${stats.nativeCount}`);
            console.log(`   - 处理了 ${stats.processedCount} 个文件`);
        }

        console.log(`\n📥 总计待下载文件: ${this.config.pendingFiles.length}`);
        console.log("==================== 开始下载 ====================\n");
    }

    // 打印最终统计信息
    printFinalStats() {
        // 从pendingFiles中统计最终结果
        const stats = {
            total: this.config.pendingFiles.length,
            completed: this.config.pendingFiles.filter(f => f.status === 'completed').length,
            failed: this.config.pendingFiles.filter(f => f.status === 'failed').length,
            skipped: this.config.pendingFiles.filter(f => f.status === 'skipped').length
        };

        console.log("\n==================== 下载完成 ====================");
        console.log(`📊 下载统计:`);
        console.log(`   总计: ${stats.total}`);
        console.log(`   成功: ${stats.completed}`);
        console.log(`   失败: ${stats.failed}`);
        console.log(`   跳过: ${stats.skipped}`);

        const successRate = stats.total > 0
            ? ((stats.completed / stats.total) * 100).toFixed(1)
            : 0;
        console.log(`   成功率: ${successRate}%`);

        if (stats.failed > 0) {
            console.log(`\n⚠️  有 ${stats.failed} 个文件下载失败，建议重新运行脚本`);
        } else {
            console.log(`\n🎉 所有文件下载成功！`);
        }
        console.log("\n======================== END ========================");
    }

    // 更新跳过文件的进度显示
    updateSkippedProgress() {
        process.stdout.write(`\r⏭️  已跳过 ${this.config.skippedCount} 个已存在的文件...`);
    }
    
    // 带重试的下载函数
    downloadFileWithRetry(url, filePath, callback, retryCount = 0) {
        this.downloadFile(url, filePath, async (result) => {
            if (result === false && retryCount < this.config.maxRetries) {
                console.log(`⚠️  重试下载 (${retryCount + 1}/${this.config.maxRetries}): ${path.basename(filePath)}`);
                await Utils.sleep(this.config.retryDelay * (retryCount + 1));
                this.downloadFileWithRetry(url, filePath, callback, retryCount + 1);
            } else {
                callback(result);
            }
        });
    }
    
    // 下载文件到队列
    downloadFile(url, filePath, callback = null) {
        this.config.downloadQueue.push({ url, filePath, callback });
        this.processDownloadQueue();
    }
    
    // 处理下载队列（支持并发）
    processDownloadQueue() {
        // 启动多个并发下载，直到达到最大并发数或队列为空
        while (this.config.activeDownloads < this.config.maxConcurrentDownloads && 
               this.config.downloadQueue.length > 0) {
            
            const { url, filePath, callback } = this.config.downloadQueue.shift();
            this.config.activeDownloads++;
            
            this.downloadFileImpl(url, filePath, (result) => {
                this.config.activeDownloads--;
                
                if (callback) {
                    callback(result);
                }
                
                // 下载完成后，继续处理队列中的下一个任务
                this.processDownloadQueue();
            });
        }
    }
    
    // 实际下载实现
    async downloadFileImpl(url, filePath, callback = null) {
        // 文件存在检查已在上层处理，这里直接下载
        
        try {
            const exists = await this.checkUrlExists(url);
            if (exists) {
                this.existsDist(filePath, () => {
                    const file = fs.createWriteStream(filePath);
                    https.get(url, (response) => {
                        response.pipe(file);
                        file.on('finish', () => {
                            file.close();
                            console.log(`✓ 下载完成: ${path.basename(filePath)}`);
                            callback?.(true);
                        });
                    }).on('error', (err) => {
                        console.error(`下载错误: ${err.message}`);
                        fs.unlink(filePath, () => {});
                        callback?.(false);
                    });
                });
            } else {
                console.log(`✗ URL不存在: ${url}`);
                callback?.(false);
            }
        } catch (error) {
            console.error(`下载异常: ${error.message}`);
            callback?.(false);
        }
    }
    
    // 检查URL是否存在
    async checkUrlExists(url) {
        return new Promise((resolve) => {
            try {
                const req = https.request(url, { method: 'HEAD' }, (res) => {
                    resolve(res.statusCode >= 200 && res.statusCode < 400);
                });
                
                req.on('error', () => resolve(false));
                req.setTimeout(5000, () => {
                    req.destroy();
                    resolve(false);
                });
                
                req.end();
            } catch (err) {
                resolve(false);
            }
        });
    }
    
    // 确保目录存在
    existsDist(dst, callback) {
        const distdir = dst.substr(0, dst.lastIndexOf("\\"));
        if (fs.existsSync(distdir)) {
            callback();
        } else {
            this.mkPath(distdir.split('\\'));
            callback();
        }
    }
    
    // 创建目录路径
    mkPath(patharr) {
        for (let i in patharr) {
            let pt = "";
            for (let k = 0; k <= i; k++) {
                pt += patharr[k] + "/";
            }
            if (!fs.existsSync(pt)) {
                fs.mkdirSync(pt, 0x777);
            }
        }
    }
}

/**
 * 程序入口点
 */
async function main() {
    try {
        const config = new DownloadConfig();
        const downloadManager = new DownloadManager(config); 
        await downloadManager.start();
    } catch (error) {
        console.error("程序执行失败:", error);
        process.exit(1);
    }
}

// 启动程序
main();