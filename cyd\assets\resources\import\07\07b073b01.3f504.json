[{"__type__": "cc.SpriteFrame", "content": {"name": "c12", "texture": "575PrEEbNMZaDI3U/gxWVG", "rect": [0, 0, 216, 180], "offset": [0, 0], "originalSize": [216, 180], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c11", "texture": "fbfpFPyNpNNbSOH8Bj4otp", "rect": [0, 0, 216, 180], "offset": [0, 0], "originalSize": [216, 180], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c31", "texture": "fdSUsm9AtDf4NpcTDLkkw2", "rect": [0, 0, 74, 56], "offset": [0, 0], "originalSize": [74, 56], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c14", "texture": "6438QDSp5FWrqgcASm6h+a", "rect": [0, 0, 13, 5], "offset": [0, 0], "originalSize": [13, 5], "capInsets": [5, 1, 6, 1]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c10", "texture": "08/9dBxFFCT6BnGdpOo2JE", "rect": [0, 0, 216, 180], "offset": [0, 0], "originalSize": [216, 180], "capInsets": [90, 95, 115, 76]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c13", "texture": "db+2a8+7lCsYxogLcoukXi", "rect": [0, 0, 13, 5], "offset": [0, 0], "originalSize": [13, 5], "capInsets": [5, 1, 5, 1]}}]