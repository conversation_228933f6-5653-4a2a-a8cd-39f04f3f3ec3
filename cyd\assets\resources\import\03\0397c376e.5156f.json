[{"__type__": "cc.SpriteFrame", "content": {"name": "wall02", "texture": "9bVwQvBHtE+p7T4Hc7BWpS", "rect": [0, 0, 80, 100], "offset": [0, 0], "originalSize": [80, 100], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s004", "texture": "87IsH/Nm9OfLwqv8i0N2dY", "rect": [0, 0, 50, 50], "offset": [0, 0], "originalSize": [50, 50], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "wall01", "texture": "34Sj7fzhdLYo+otK7b1yW0", "rect": [0, 0, 50, 50], "offset": [0, 0], "originalSize": [50, 50], "capInsets": [21, 0, 21, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "wall04", "texture": "5f9W6m8iNNZ6fnJc0l4h28", "rect": [0, 0, 80, 100], "offset": [0, 0], "originalSize": [80, 100], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "wall03", "texture": "c51XIBuA9DRqMtU8m6BFN9", "rect": [0, 0, 30, 50], "offset": [0, 0], "originalSize": [30, 50], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "s009", "texture": "4aFlBnBD1Me4mUTK7bkXzU", "rect": [0, 0, 50, 50], "offset": [0, 0], "originalSize": [50, 50], "capInsets": [0, 0, 0, 0]}}]