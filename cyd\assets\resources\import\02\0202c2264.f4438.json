[{"__type__": "cc.AnimationClip", "_name": "map1007jianxianAnim", "_duration": 0.016666666666666666, "curveData": {"paths": {}, "props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.016666666666666666, "value": 255}]}, "comps": {"cc.RigidBody": {"enabledContactListener": [{"frame": 0, "value": false}, {"frame": 0.016666666666666666, "value": true}]}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "08", "texture": "c1Hf8faddGCboJJEeTKmsp", "rect": [0, 0, 84, 40], "offset": [0, 0], "originalSize": [84, 40], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "06", "texture": "8dHYnzM5pCMqd8OmFhmQ+z", "rect": [0, 0, 130, 80], "offset": [0, 0], "originalSize": [130, 80], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "09", "texture": "f8NzWsELBImonPJiD5ucJK", "rect": [0, 0, 32, 40], "offset": [0, 0], "originalSize": [32, 40], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map1007SaobaAnim", "_duration": 0.2, "curveData": {"props": {"y": [{"frame": 0, "value": -470.887}, {"frame": 0.2, "value": -136.846}]}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "07", "texture": "dcjISjUbtHOLpYHi0h4B7L", "rect": [0, 0, 84, 40], "offset": [0, 0], "originalSize": [84, 40], "capInsets": [0, 0, 0, 0]}}]