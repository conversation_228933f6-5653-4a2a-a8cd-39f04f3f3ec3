[[{"__type__": "cc.Prefab", "_name": "vStageIWannerTransferGuideDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "59miQTXQ5Kj41KYYB1scwP"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "59miQTXQ5Kj41KYYB1scwP"}, "fileId": "8cm6yTau9DLa6EnJ+kQ+7R"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 5}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "940EQN9ZFEYr5DCsNqhNqo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "59miQTXQ5Kj41KYYB1scwP"}, "fileId": "0eMb3pyZFMeb5yamAYU8/F"}, "_contentSize": {"__type__": "cc.Size", "width": 611, "height": 470}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "在茶叶蛋当前的位置\n放置一个传送点。", "_N$string": "在茶叶蛋当前的位置\n放置一个传送点。", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "59miQTXQ5Kj41KYYB1scwP"}, "fileId": "fa67rlijhBNps8K+1iA0QJ"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 54.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-140, -70, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label copy", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "将茶叶蛋传送到已放\n置的传送点。", "_N$string": "将茶叶蛋传送到已放\n置的传送点。", "_fontSize": 20, "_lineHeight": 24, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "59miQTXQ5Kj41KYYB1scwP"}, "fileId": "cdHuxQtCNAdbWiGYO6kWxz"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 54.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [145, -70, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 2}, "_children": [{"__id__": 7}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 6}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "59miQTXQ5Kj41KYYB1scwP"}, "fileId": "2ccLAoZalC+o/rls+d2CgC"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -160, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "e09", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3cZOflBIRNx6vca4Kr5dry"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "59miQTXQ5Kj41KYYB1scwP"}, "fileId": "4egljx97pDYpHh1uG3nKsT"}, "_contentSize": {"__type__": "cc.Size", "width": 211, "height": 86}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "e15", "texture": "152b39a93", "rect": [3, 3, 611, 470], "offset": [0, 0], "originalSize": [611, 470], "capInsets": [0, 0, 0, 0]}}]