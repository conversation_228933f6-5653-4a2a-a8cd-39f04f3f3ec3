[{"__type__": "cc.SpriteFrame", "content": {"name": "t055", "texture": "aeO/sSWlpNOaP8jCa+j0Ee", "rect": [0, 0, 394, 310], "offset": [0, 0], "originalSize": [394, 310], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t056", "texture": "3eXkXrhLZOQKHEQ077dTCh", "rect": [0, 0, 394, 310], "offset": [0, 0], "originalSize": [394, 310], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "t038", "texture": "3c7BhZy6NKNLtfL6ICKu2y", "rect": [0, 0, 235, 312], "offset": [0, 0], "originalSize": [235, 312], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "mapReaction", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "mapReaction", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 14}, {"__id__": 17}, {"__id__": 29}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "04K1SPfjBIYKDdKhbpOpl9"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_labelTitle", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "在绿灯亮起时按下按钮", "_N$string": "在绿灯亮起时按下按钮", "_fontSize": 36, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "63PA53nMxDAIHfCWrGP638"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [99.606, 140.377, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLight", "_parent": {"__id__": 2}, "_children": [{"__id__": 5}, {"__id__": 8}, {"__id__": 11}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 4}, "_layoutSize": {"__type__": "cc.Size", "width": 260, "height": 100}, "_resize": 1, "_N$layoutType": 1, "_N$spacingX": 25}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "8bhWNf7PBCLo+5e5nj9vwd"}, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [83.861, 238.598, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "item", "_parent": {"__id__": 4}, "_children": [{"__id__": 6}, {"__id__": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "4376I0nrpMNoOPFpvX39TL"}, "_contentSize": {"__type__": "cc.Size", "width": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-95, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "green", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "95o+YPMI5ChqQuLC1D5rNo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "06uSlmGNxBhpwnR9ElNbJZ"}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 96}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "red", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5eakwH8b9C5JqcAFQHmnH0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "f4AfROkeBF1qY9zA8fjFdg"}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 96}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "item", "_parent": {"__id__": 4}, "_children": [{"__id__": 9}, {"__id__": 10}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "40jBu46ttBEpgyMMExuWGU"}, "_contentSize": {"__type__": "cc.Size", "width": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "green", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "95o+YPMI5ChqQuLC1D5rNo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "981R8iQwFCkKz0hwsQytLw"}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 96}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "red", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5eakwH8b9C5JqcAFQHmnH0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "d3Y5uCpuNDSqKtUN4clO5R"}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 96}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "item", "_parent": {"__id__": 4}, "_children": [{"__id__": 12}, {"__id__": 13}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "520jFbshxH67LnsR4VlHRW"}, "_contentSize": {"__type__": "cc.Size", "width": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [95, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "green", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "95o+YPMI5ChqQuLC1D5rNo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "80YjqJhfdKLZ5E4u1lUd9o"}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 96}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "red", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5eakwH8b9C5JqcAFQHmnH0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "23igcQSaJOW4mktlhtMQOr"}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 96}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeEnter", "_parent": {"__id__": 2}, "_children": [{"__id__": 15}, {"__id__": 16}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "60pOk9xOxAlp1kKS5qSmdn"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "9168UTwdZIfKADixJkECuD"}, "_contentSize": {"__type__": "cc.Size", "width": 394, "height": 310}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [95.084, -85.016, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "up", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "debSj3yrdBtZp8rax2Ofbr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "00lL5hfhNMLIOqUzYSkMpK"}, "_contentSize": {"__type__": "cc.Size", "width": 394, "height": 310}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "down", "_parent": {"__id__": 14}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5bDFb6xrNHnJUZbNLoNAQm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "9bJhyXAwlK3J04Mi8ae7jH"}, "_contentSize": {"__type__": "cc.Size", "width": 394, "height": 310}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "recore", "_parent": {"__id__": 2}, "_children": [{"__id__": 18}, {"__id__": 28}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a4hYnnXJhGHoeCbuU6Utbv"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "f4rp0xXrZGR4ADxo2ZNBzM"}, "_contentSize": {"__type__": "cc.Size", "width": 235, "height": 312}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-316.569, -50.251, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLayer", "_parent": {"__id__": 17}, "_children": [{"__id__": 19}, {"__id__": 22}, {"__id__": 25}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 18}, "_layoutSize": {"__type__": "cc.Size", "width": 200, "height": 150}, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "441xqLVl1Dk465xV/qZN+j"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 150}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -16.82, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 18}, "_children": [{"__id__": 20}, {"__id__": 21}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 19}, "_layoutSize": {"__type__": "cc.Size", "width": 200, "height": 40}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "511PAoVA1MEZE9QbA/6pAW"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [26.454, 55, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "1.  ", "_N$string": "1.  ", "_fontSize": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "caAC9yIx9MvYVAi7LXrHbh"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 33.35, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-83.325, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "text", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "44.5", "_N$string": "44.5", "_fontSize": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "45q5dQjk9HeK+sjRPEUxF4"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 58.39, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-37.455000000000005, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 18}, "_children": [{"__id__": 23}, {"__id__": 24}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 22}, "_layoutSize": {"__type__": "cc.Size", "width": 200, "height": 40}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "eaMQp/npZJ/LAdfoP3u6OI"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [26.454, 15, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "2.  ", "_N$string": "2.  ", "_fontSize": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "4bO+F8IoFATo2U5mRu7GYO"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 33.35, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-83.325, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "text", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "44.5", "_N$string": "44.5", "_fontSize": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "77yoAhRg5FOLONVGRuuUAI"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 58.39, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-37.455000000000005, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 18}, "_children": [{"__id__": 26}, {"__id__": 27}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 25}, "_layoutSize": {"__type__": "cc.Size", "width": 200, "height": 40}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "2a5oIuuJJIfYFmLamFjsM6"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [26.454, -25, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "3.  ", "_N$string": "3.  ", "_fontSize": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "deMzuqmipPBqecXqENZvHY"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 33.35, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-83.325, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "text", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "44.5", "_N$string": "44.5", "_fontSize": 30, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "2355hDj4JLT7PzToOd+Zv2"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 58.39, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-37.455000000000005, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 17}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "取最佳成绩计入数据", "_N$string": "取最佳成绩计入数据", "_fontSize": 18, "_lineHeight": 18, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "cfSH7hbLRCmZ+E4K8RsxoP"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 162, "height": 22.68}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [4.018, -95.512, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTime", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "30", "_N$string": "30", "_N$file": {"__uuid__": "d8B0jG8/lJtqyVhktHaz3+"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "c3J0TGZndIDok1pAIvx2oG"}, "fileId": "5c1b3qeupAH7VDR9GoyIf0"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 125, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [359.058, -0.867, 0, 0, 0, 0, 1, 0.8, 0.8, 0.8]}, "_groupIndex": 2, "groupIndex": 2}], {"__type__": "cc.SpriteFrame", "content": {"name": "t054", "texture": "21vaoOmLNNfLTUknt/JpgI", "rect": [0, 0, 394, 310], "offset": [0, 0], "originalSize": [394, 310], "capInsets": [0, 0, 0, 0]}}]