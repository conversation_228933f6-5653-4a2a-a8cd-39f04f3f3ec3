[{"__type__": "cc.SpriteFrame", "content": {"name": "default_scrollbar_vertical", "texture": "d608qFRoFHwbXd0Dap056i", "rect": [0, 0, 15, 30], "offset": [0, 0], "originalSize": [15, 30], "capInsets": [4, 10, 4, 10]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "default_scrollbar_vertical_bg", "texture": "61cyPdEfRN047sDK9rO0W5", "rect": [0, 0, 15, 30], "offset": [0, 0], "originalSize": [15, 30], "capInsets": [4, 10, 4, 10]}}, [{"__type__": "cc.Prefab", "_name": "vDevTestScene", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "scene", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 5}, {"__id__": 6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "115zljGM1OAac7ocLXxk+z"}, "_contentSize": {"__type__": "cc.Size", "width": 576, "height": 1024}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_layoutBg", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_spriteFrame": {"__uuid__": "9bvaMerUlDyary99mJa6xp"}, "_type": 1, "_sizeMode": 0}, {"__type__": "cc.Layout", "node": {"__id__": 2}, "_layoutSize": {"__type__": "cc.Size", "width": 576, "height": 1024}}, {"__type__": "cc.Widget", "node": {"__id__": 2}, "alignMode": 2, "_alignFlags": 45, "_originalWidth": 200, "_originalHeight": 150}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "411RiUTdpK95mhe0T+swJw"}, "_color": {"__type__": "cc.Color", "r": 73, "g": 73, "b": 73}, "_contentSize": {"__type__": "cc.Size", "width": 576, "height": 1024}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonBack", "_parent": {"__id__": 1}, "_children": [{"__id__": 4}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 3}, "alignMode": 2, "_alignFlags": 33, "_right": 5, "_top": 5}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 3}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "19JVvYiG1O2LORjDvRk/uJ"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [233, 487, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 4}, "_useOriginalSize": false, "_string": "Back", "_N$string": "Back", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "a8zVtlXEdGv6OoGP3+vZaH"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTitle", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 5}, "_useOriginalSize": false, "_string": "vDevTestScene", "_N$string": "vDevTestScene", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}, {"__type__": "cc.Widget", "node": {"__id__": 5}, "alignMode": 2, "_alignFlags": 1, "_top": 10}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "ff34/Ls89EgqVkJGIW0wWw"}, "_contentSize": {"__type__": "cc.Size", "width": 277.91, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 482, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_scrollViewList", "_parent": {"__id__": 1}, "_children": [{"__id__": 7}, {"__id__": 13}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_spriteFrame": {"__uuid__": "9bvaMerUlDyary99mJa6xp"}, "_type": 1, "_sizeMode": 0}, {"__id__": 11}, {"__type__": "cc.Widget", "node": {"__id__": 6}, "alignMode": 2, "_alignFlags": 45, "_left": 5, "_right": 5, "_top": 60, "_bottom": 5, "_originalWidth": 600, "_originalHeight": 250}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "11gEn5INxIr4RP1JJOXydb"}, "_color": {"__type__": "cc.Color", "r": 184, "g": 184, "b": 184}, "_contentSize": {"__type__": "cc.Size", "width": 566, "height": 959}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -27.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "scrollBar", "_parent": {"__id__": 6}, "_children": [{"__id__": 8}], "_components": [{"__id__": 10}, {"__type__": "cc.Widget", "node": {"__id__": 7}, "alignMode": 0, "_alignFlags": 37, "_left": 350.07654921020657, "_originalHeight": 237}, {"__type__": "cc.Sprite", "node": {"__id__": 7}, "_spriteFrame": {"__uuid__": "5f5dyqtRNNxaFmVzYns6FZ"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "5dskGBRttJNYbVMkgWthFf"}, "_color": {"__type__": "cc.Color", "r": 238, "g": 217, "b": 227}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 959}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [283, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bar", "_parent": {"__id__": 7}, "_components": [{"__id__": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "a24898Gk1IrphMFxQRl1IY"}, "_color": {"__type__": "cc.Color", "r": 236, "g": 241, "b": 245}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "node": {"__id__": 8}, "_spriteFrame": {"__uuid__": "5cO7kybDxGj4ipyMYdRYZB"}, "_type": 1, "_sizeMode": 0}, {"__type__": "cc.<PERSON>", "node": {"__id__": 7}, "_scrollView": {"__id__": 11}, "_N$handle": {"__id__": 9}, "_N$direction": 1}, {"__type__": "<PERSON><PERSON>", "node": {"__id__": 6}, "horizontal": false, "brake": 0.75, "bounceDuration": 0.23, "_N$content": {"__id__": 12}, "content": {"__id__": 12}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": {"__id__": 10}}, {"__type__": "cc.Node", "_name": "CC_nodeScrollViewContent", "_parent": {"__id__": 13}, "_children": [{"__id__": 14}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 12}, "_layoutSize": {"__type__": "cc.Size", "width": 536, "height": 80}, "_resize": 1, "_N$layoutType": 3, "_N$spacingX": 4, "_N$spacingY": 4}, {"__type__": "cc.Widget", "node": {"__id__": 12}, "alignMode": 0, "_alignFlags": 45, "_bottom": 859, "_originalWidth": 220, "_originalHeight": 148}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "dbrXB8mjVMRrlr78EZkY5U"}, "_contentSize": {"__type__": "cc.Size", "width": 536, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 469.5, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "view", "_parent": {"__id__": 6}, "_children": [{"__id__": 12}], "_components": [{"__type__": "cc.Mask", "node": {"__id__": 13}, "_N$alphaThreshold": 1}, {"__type__": "cc.Widget", "node": {"__id__": 13}, "alignMode": 0, "_alignFlags": 45, "_left": 10, "_right": 20, "_top": 10, "_bottom": 10, "_originalWidth": 240, "_originalHeight": 250}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "4eAP71OyJDpJuTz/d9OLdJ"}, "_contentSize": {"__type__": "cc.Size", "width": 536, "height": 939}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeButtonItemTemplate", "_parent": {"__id__": 12}, "_children": [{"__id__": 15}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 14}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "dfbpCSK15IaYdAS9i3mm3f"}, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-181, -40, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 15}, "_useOriginalSize": false, "_string": "TemplateName", "_N$string": "TemplateName", "_fontSize": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}, {"__type__": "cc.Widget", "node": {"__id__": 15}, "alignMode": 0, "_alignFlags": 45, "_originalWidth": 135.5, "_originalHeight": 40}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "d1zitEJwtFeqGhrORjlg/4"}, "fileId": "6eht3EMZRCBpwxZFMjhufw"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}]]