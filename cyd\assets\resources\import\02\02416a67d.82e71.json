[{"__type__": "cc.Material", "_name": "sprite-outline", "_effectAsset": {"__uuid__": "e24xD4osJMCqPLE/FsVknU"}, "_techniqueData": {"0": {"defines": {"USE_TEXTURE": true, "SHOW_OUT_LINE": true}, "props": {"outlineColor": {"__type__": "cc.Color", "r": 194, "g": 29, "b": 241}, "outlineWidth": 0.005}}}}, {"__type__": "cc.EffectAsset", "_name": "sprite-outline", "techniques": [{"passes": [{"blendState": {"targets": [{"blend": true}]}, "rasterizerState": {"cullMode": 0}, "properties": {"texture": {"value": "white", "type": 29}, "alphaThreshold": {"value": [0.5], "type": 13}, "outlineColor": {"value": [1, 0, 0, 1], "editor": {"type": "color", "tooltip": "描边颜色"}, "type": 16}, "outlineWidth": {"value": [0.002], "editor": {"tooltip": "描边宽度", "range": [0, 1]}, "type": 13}}, "program": "sprite-outline|vs|fs"}]}], "shaders": [{"hash": 984141415, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_color;\n#if USE_TEXTURE\nin vec2 v_uv0;\nuniform sampler2D texture;\n#endif\n#if SHOW_OUT_LINE\nuniform Outline {\n  vec4 outlineColor;\n  float outlineWidth;\n};\nfloat getBgAlpha() {\n  vec4 color_up = texture(texture, v_uv0 + vec2(0, outlineWidth));\n  vec4 color_down = texture(texture, v_uv0 - vec2(0, outlineWidth));\n  vec4 color_left = texture(texture, v_uv0 - vec2(outlineWidth, 0));\n  vec4 color_right = texture(texture, v_uv0 + vec2(outlineWidth, 0));\n  vec4 color_up_left = texture(texture, v_uv0 + vec2(outlineWidth, -outlineWidth));\n  vec4 color_up_right = texture(texture, v_uv0 + vec2(outlineWidth, outlineWidth));\n  vec4 color_down_left = texture(texture, v_uv0 + vec2(-outlineWidth, -outlineWidth));\n  vec4 color_down_right = texture(texture, v_uv0 + vec2(-outlineWidth, outlineWidth));\n  float total = color_right.a + color_left.a + color_down.a + color_up.a + color_up_left.a + color_up_right.a + color_down_left.a + color_down_right.a;\n  return clamp(total, 0.0, 1.0);\n}\n#endif\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  o *= texture(texture, v_uv0);\n    #if CC_USE_ALPHA_ATLAS_TEXTURE\n    o.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n    #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  gl_FragColor = o;\n  #if SHOW_OUT_LINE\n    if (outlineWidth == 0.0) {\n      return;\n    }\n    vec4 color_dest = outlineColor * getBgAlpha();\n    vec4 color_src = o;\n    gl_FragColor = color_src * color_src.a + color_dest * (1.0 - color_src.a);\n  #endif\n}"}, "glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_color;\n#if USE_TEXTURE\nvarying vec2 v_uv0;\nuniform sampler2D texture;\n#endif\n#if SHOW_OUT_LINE\nuniform vec4 outlineColor;\nuniform float outlineWidth;\nfloat getBgAlpha() {\n  vec4 color_up = texture2D(texture, v_uv0 + vec2(0, outlineWidth));\n  vec4 color_down = texture2D(texture, v_uv0 - vec2(0, outlineWidth));\n  vec4 color_left = texture2D(texture, v_uv0 - vec2(outlineWidth, 0));\n  vec4 color_right = texture2D(texture, v_uv0 + vec2(outlineWidth, 0));\n  vec4 color_up_left = texture2D(texture, v_uv0 + vec2(outlineWidth, -outlineWidth));\n  vec4 color_up_right = texture2D(texture, v_uv0 + vec2(outlineWidth, outlineWidth));\n  vec4 color_down_left = texture2D(texture, v_uv0 + vec2(-outlineWidth, -outlineWidth));\n  vec4 color_down_right = texture2D(texture, v_uv0 + vec2(-outlineWidth, outlineWidth));\n  float total = color_right.a + color_left.a + color_down.a + color_up.a + color_up_left.a + color_up_right.a + color_down_left.a + color_down_right.a;\n  return clamp(total, 0.0, 1.0);\n}\n#endif\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  o *= texture2D(texture, v_uv0);\n    #if CC_USE_ALPHA_ATLAS_TEXTURE\n    o.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n    #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  gl_FragColor = o;\n  #if SHOW_OUT_LINE\n    if (outlineWidth == 0.0) {\n      return;\n    }\n    vec4 color_dest = outlineColor * getBgAlpha();\n    vec4 color_src = o;\n    gl_FragColor = color_src * color_src.a + color_dest * (1.0 - color_src.a);\n  #endif\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}], "samplers": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplers": []}}, "defines": [{"name": "USE_TEXTURE", "type": "boolean", "defines": []}, {"name": "CC_USE_MODEL", "type": "boolean", "defines": []}, {"name": "USE_ALPHA_TEST", "type": "boolean", "defines": []}, {"name": "SHOW_OUT_LINE", "type": "boolean", "defines": []}, {"name": "CC_USE_ALPHA_ATLAS_TEXTURE", "type": "boolean", "defines": ["USE_TEXTURE"]}], "blocks": [{"name": "ALPHA_TEST", "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"], "binding": 0}, {"name": "Outline", "members": [{"name": "outlineColor", "type": 16, "count": 1}, {"name": "outlineWidth", "type": 13, "count": 1}], "defines": ["SHOW_OUT_LINE"], "binding": 1}], "samplers": [{"name": "texture", "type": 29, "count": 1, "defines": ["USE_TEXTURE"], "binding": 30}], "record": null, "name": "sprite-outline|vs|fs"}]}]