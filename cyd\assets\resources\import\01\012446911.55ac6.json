[{"__type__": "cc.SpriteFrame", "content": {"name": "a383", "texture": "1dfa72800", "rect": [526, 479, 46, 56], "offset": [0, 0], "originalSize": [46, 56], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "map264", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map264", "_children": [{"__id__": 2}, {"__id__": 3}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "穿越6个隐形平台", "_N$string": "穿越6个隐形平台", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "20l53IN4JEdKaZAm+ilTTj"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 302.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 150.205, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 4}, {"__id__": 14}, {"__id__": 16}, {"__id__": 18}, {"__id__": 20}, {"__id__": 25}, {"__id__": 27}, {"__id__": 49}, {"__type__": "cc.Node", "_name": "CC_nodeWaterParent", "_parent": {"__id__": 3}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "5b5dMB0JtLo67VUJr0GFFk"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__id__": 52}, {"__id__": 54}, {"__id__": 77}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "view", "_parent": {"__id__": 3}, "_children": [{"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 4}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 4}, "_offset": {"__type__": "cc.Vec2", "y": -30}, "_size": {"__type__": "cc.Size", "width": 70, "height": 120}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 4}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "a3BnWiEgFDFossuHjdwDtG"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [434.492, -71.492, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a382", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eakr/5cpdChJyJY+if6X65"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "80l2xsLUlMEp0TiKBwwe28"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 120}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5, -28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a382", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eakr/5cpdChJyJY+if6X65"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "85zVQyjylK8KeSJZCdGpVR"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, -28, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a382", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eakr/5cpdChJyJY+if6X65"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "493oyT+QdMs7/acY2mtb9u"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 120}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-74.015, -54.545, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a382", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eakr/5cpdChJyJY+if6X65"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "f1khjRE0JMJ6Q1N5BzVJWg"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-64.015, -54.545, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a382", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eakr/5cpdChJyJY+if6X65"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "c68td8hVhAXokv1S2ZQ1Wi"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 120}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [64.352, -82.816, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a382", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eakr/5cpdChJyJY+if6X65"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "dfSoOH7HxJ3oJKdzGU6Ois"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [74.352, -82.816, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a385", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "51ia08AbFC8pIbfp8ddnEM"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "390FBBRrVO9pWXUS2VeXqi"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 56}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [70.076, -56.657, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a383", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0bVUbc04dAhJG3mSQl7HXK"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "37ez6F9JZAlYB3H562Y3Fv"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 56}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -13.916, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a384", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8f4Bh2CxlG9rNydRgSNpoZ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "63hedIiZ5GnIJZC1O43MDM"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 56}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-69.082, -41.25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 3}, "_children": [{"__id__": 15}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 14}, "_allowSleep": false, "_gravityScale": 3.75, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 14}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 14}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "8eCjbVtYhJ/7+EeY+k6PFz"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-390, -140, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "f7XNNWWadDC56UU/m0WVVI"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeCup", "_parent": {"__id__": 3}, "_children": [{"__id__": 17}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a6hV8TRSpHsKm6QxOMwswW"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 16}, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 16}, "_offset": {"__type__": "cc.Vec2", "y": 44}, "_size": {"__type__": "cc.Size", "width": 60, "height": 74}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 16}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "39WRwaJ8VCAZ+plZh2QGa2"}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [434.365, -51, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeCupWater", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "90NOiK2ONOvLnydWZF8CIy"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "fa27H9QFVEL6Cz/hC5DVm8"}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 3}, "_children": [{"__id__": 19}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 18}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 18}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -9}, "_size": {"__type__": "cc.Size", "width": 12, "height": 18}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 18}, "msgId": 5}, {"__type__": "cc.Layout", "node": {"__id__": 18}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 12.8, "height": 19}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "81e5b/VbtHtqSeJsuWBaoR"}, "_contentSize": {"__type__": "cc.Size", "width": 12.8, "height": 19}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-273.969, -123.502, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b19", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "24byRl395KxbpceGgMnHGm"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 19}, "_left": -860.1851851851853, "_right": -860.1851851851853, "_originalWidth": 384}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "258MEcDK1AjZG/5eYDqKTd"}, "_contentSize": {"__type__": "cc.Size", "width": 12.8, "height": 19.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 3}, "_children": [{"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 20}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 20}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -9}, "_size": {"__type__": "cc.Size", "width": 48, "height": 18}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 20}, "msgId": 5}, {"__type__": "cc.Layout", "node": {"__id__": 20}, "_layoutSize": {"__type__": "cc.Size", "width": 51.2, "height": 19}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "c2U9rV1vNAE6/YR0Qk7jsW"}, "_contentSize": {"__type__": "cc.Size", "width": 51.2, "height": 19}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [325.741, -124.503, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b19", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "24byRl395KxbpceGgMnHGm"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 21}, "_left": -860.1851851851853, "_right": -860.1851851851853, "_originalWidth": 384}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "cdDosIOnhI663R5l9H3w0J"}, "_contentSize": {"__type__": "cc.Size", "width": 12.8, "height": 19.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-19.200000000000003, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b19", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "24byRl395KxbpceGgMnHGm"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 22}, "_left": -860.1851851851853, "_right": -860.1851851851853, "_originalWidth": 384}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "94OGQNIcVPa4FoVG3VML7M"}, "_contentSize": {"__type__": "cc.Size", "width": 12.8, "height": 19.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-6.400000000000002, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b19", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "24byRl395KxbpceGgMnHGm"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 23}, "_left": -860.1851851851853, "_right": -860.1851851851853, "_originalWidth": 384}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "e5AwOZWOBFMbL9w1t1ZhSJ"}, "_contentSize": {"__type__": "cc.Size", "width": 12.8, "height": 19.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [6.399999999999999, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b19", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "24byRl395KxbpceGgMnHGm"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 24}, "_left": -860.1851851851853, "_right": -860.1851851851853, "_originalWidth": 384}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "98UG/1tMVDuYSgjQCibaDc"}, "_contentSize": {"__type__": "cc.Size", "width": 12.8, "height": 19.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [19.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "tiggerActionNode01", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 25}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": -20}, "_size": {"__type__": "cc.Size", "width": 2, "height": 2}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": 20}, "_size": {"__type__": "cc.Size", "width": 2, "height": 2}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": 20, "y": 40}, "_size": {"__type__": "cc.Size", "width": 2, "height": 2}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": -20, "y": 40}, "_size": {"__type__": "cc.Size", "width": 2, "height": 2}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": 20, "y": 80}, "_size": {"__type__": "cc.Size", "width": 2, "height": 2}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "x": -20, "y": 80}, "_size": {"__type__": "cc.Size", "width": 2, "height": 2}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 25}, "tagetNode": {"__id__": 26}, "animClips": {"__uuid__": "31yAXABTdEQ6FNkXxcXyfv"}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 25}, "tagetNode": {"__id__": 36}, "animClips": {"__uuid__": "b5TkBrTkRHbKaHYABivGUN"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "7djlpNBWNOdYbJEgJNQn3s"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-390, -140, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform05", "_parent": {"__id__": 27}, "_children": [{"__id__": 46}, {"__id__": 47}, {"__id__": 48}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 26}, "_defaultClip": {"__uuid__": "31yAXABTdEQ6FNkXxcXyfv"}, "_clips": [{"__uuid__": "31yAXABTdEQ6FNkXxcXyfv"}]}, {"__type__": "cc.RigidBody", "node": {"__id__": 26}, "_type": 1, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 26}, "_offset": {"__type__": "cc.Vec2", "y": -10}, "_size": {"__type__": "cc.Size", "width": 180, "height": 20}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "19Fx297J5IhYCj25NX+Vba"}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-240, 85, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatformParent", "_parent": {"__id__": 3}, "_children": [{"__id__": 28}, {"__id__": 32}, {"__id__": 26}, {"__id__": 36}, {"__id__": 40}, {"__id__": 43}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "abfacwwRVKRL0RESt5VNBr"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform04", "_parent": {"__id__": 27}, "_children": [{"__id__": 29}, {"__id__": 30}, {"__id__": 31}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 28}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 28}, "_offset": {"__type__": "cc.Vec2", "y": -10}, "_size": {"__type__": "cc.Size", "width": 244, "height": 20}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "42DjdP3NJPjo30pXCTcLdt"}, "_color": {"__type__": "cc.Color", "r": 111, "g": 95, "b": 95}, "_contentSize": {"__type__": "cc.Size", "width": 244, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-306.457, -22.51, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 28}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 29}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle04_05", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle04_05", "_N$skeletonData": {"__uuid__": "0dUdBb3flB9pGB+O/NbvFr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "9eISyaiNpMgJZizxVKrj2e"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, -12, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 30}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 30}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -8}, "_size": {"__type__": "cc.Size", "width": 50, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 30}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "9eWJ905pVF/bB2Rz7vrUeM"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-91.085, -16, 0, 0, 0, 0.008726535498373935, 0.9999619230641713, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 1}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 31}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 31}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 8}, "_size": {"__type__": "cc.Size", "width": 50, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 31}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "dd0+/X6RNM9bPQFK9EWJfU"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [92, -1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform02", "_parent": {"__id__": 27}, "_children": [{"__id__": 33}, {"__id__": 34}, {"__id__": 35}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 32}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 32}, "_offset": {"__type__": "cc.Vec2", "y": -10}, "_size": {"__type__": "cc.Size", "width": 180, "height": 20}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "b7G2NG+F5F17iCGMuHekzu"}, "_color": {"__type__": "cc.Color", "r": 111, "g": 95, "b": 95}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-435.515, 85, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 32}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 33}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle02_05", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle02_05", "_N$skeletonData": {"__uuid__": "0dUdBb3flB9pGB+O/NbvFr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "c5aCoCCeNAQIMMNH0PllE1"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, -12.384, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 34}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 34}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -8}, "_size": {"__type__": "cc.Size", "width": 12, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 34}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "2cUDy/ovVEqI7Uye6WBPbF"}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [72.778, -16, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 35}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 35}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -8}, "_size": {"__type__": "cc.Size", "width": 76, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 35}, "msgId": 5}, {"__type__": "cc.Layout", "node": {"__id__": 35}, "_layoutSize": {"__type__": "cc.Size", "width": 76, "height": 16}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "73Mnybj15Moo+P+CeKE6eG"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-48, 15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform06", "_parent": {"__id__": 27}, "_children": [{"__id__": 37}, {"__id__": 38}, {"__id__": 39}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 36}, "_type": 1, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 36}, "_offset": {"__type__": "cc.Vec2", "y": -10}, "_size": {"__type__": "cc.Size", "width": 180, "height": 20}}, {"__type__": "cc.Animation", "node": {"__id__": 36}, "_clips": [{"__uuid__": "b5TkBrTkRHbKaHYABivGUN"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "59BViojudOtIAKHzysDuvq"}, "_color": {"__type__": "cc.Color", "r": 111, "g": 95, "b": 95}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [240, 85, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 36}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 37}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle05_05", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle05_05", "_N$skeletonData": {"__uuid__": "0dUdBb3flB9pGB+O/NbvFr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "bfHz2wI2hPA40KgmHjhalF"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, -12, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "dead1", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 38}, "_type": 1, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 38}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -8}, "_size": {"__type__": "cc.Size", "width": 170, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 38}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "cemHtMWBpImJ7OvWKkXa85"}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, -16, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "dead2", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 39}, "_type": 1, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 39}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 8}, "_size": {"__type__": "cc.Size", "width": 76, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 39}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "f2l/lDfJ1BWakj7yjEBh9b"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-47, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform01", "_parent": {"__id__": 27}, "_children": [{"__id__": 41}, {"__id__": 42}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 40}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 40}, "_offset": {"__type__": "cc.Vec2", "y": -10}, "_size": {"__type__": "cc.Size", "width": 148, "height": 20}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "57lH23SM5JRopOJuHei2qS"}, "_color": {"__type__": "cc.Color", "r": 111, "g": 95, "b": 95}, "_contentSize": {"__type__": "cc.Size", "width": 148, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [435.785, 85, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 40}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 41}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle01_05", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle01_05", "_N$skeletonData": {"__uuid__": "0dUdBb3flB9pGB+O/NbvFr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "cf/gmCi4FM2rF8rW2hlO0M"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, -12, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 40}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 42}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 42}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -8}, "_size": {"__type__": "cc.Size", "width": 130, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 42}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "b1dY7+a3BH2bke6IuxtPS1"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 13, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform03", "_parent": {"__id__": 27}, "_children": [{"__id__": 44}, {"__id__": 45}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 43}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 43}, "_offset": {"__type__": "cc.Vec2", "y": -10}, "_size": {"__type__": "cc.Size", "width": 180, "height": 20}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "ac3LvZFbVBpovbdyaJc7BX"}, "_color": {"__type__": "cc.Color", "r": 111, "g": 95, "b": 95}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [320.081, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 43}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 44}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle03_05", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle03_05", "_N$skeletonData": {"__uuid__": "0dUdBb3flB9pGB+O/NbvFr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "4e/qsE7X1JxLgCZoSSnGko"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, -12, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "dead", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 45}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 45}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 8}, "_size": {"__type__": "cc.Size", "width": 54, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 45}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "1crOLmq6NGV7YER4Hw8l6R"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-60, -1, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spine", "_parent": {"__id__": 26}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 46}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle05_05", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle05_05", "_N$skeletonData": {"__uuid__": "0dUdBb3flB9pGB+O/NbvFr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "ddtGa2EmlNUJgImtKArmm5"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, -12, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "dead1", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 47}, "_type": 1, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 47}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -8}, "_size": {"__type__": "cc.Size", "width": 170, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 47}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "e8i1kvQtlAQbhCS8XaEPnc"}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, -16, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "dead2", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 48}, "_type": 1, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 48}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 8}, "_size": {"__type__": "cc.Size", "width": 76, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 48}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "d3r6Uh1HFFz5JH4M6zDA4Z"}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-47, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWaterRoot", "_parent": {"__id__": 3}, "_children": [{"__id__": 50}, {"__id__": 51}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 49}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 49}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": -128}, "_size": {"__type__": "cc.Size", "width": 640, "height": 256}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "b29nzwVGlEE6yq3VWMf3yb"}, "_opacity": 200, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWave", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e71lsajAhFdqy/7GmTWm0S"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "03BYj94RJB+7ZhrteY9MQJ"}, "_contentSize": {"__type__": "cc.Size", "width": 704, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b12", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "67Fjl1ABZBybi990drEiVO"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "00tK06TJlARYUNps9BV2zD"}, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 324}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeWater", "_parent": {"__id__": 3}, "_children": [{"__id__": 53}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 52}, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 52}, "_sensor": true, "_friction": 0, "_radius": 8}, {"__type__": "49f10FPm2FNJ7haO3rGbozI", "node": {"__id__": 52}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 52}, "msgId": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "d3vn7yBkZHzpGqJHEK0oD5"}, "_contentSize": {"__type__": "cc.Size", "width": 20, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "b20", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "43LPYyA3JDkJCNgRsbklNk"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "d5ujK7KQZCG50z0p0EFq8c"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeGroundLeft", "_parent": {"__id__": 3}, "_children": [{"__id__": 55}, {"__id__": 56}, {"__id__": 57}, {"__id__": 64}, {"__id__": 65}, {"__id__": 66}, {"__id__": 67}, {"__id__": 68}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}, {"__id__": 74}, {"__id__": 75}, {"__id__": 76}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 54}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 54}, "_offset": {"__type__": "cc.Vec2", "y": -224}, "_size": {"__type__": "cc.Size", "width": 512, "height": 448}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "ddYyYKbsZOwb9c2MuSZyar"}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 448}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-448, -140, 0, 0, 0, 0, 1, 0.8, 0.8, 0.8]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 55}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 55}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "cfPpQzx9dKZ4tss1oZTTlv"}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 384}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 56}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "18vpguUxVJ9Y1dVds3xRgW"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 54}, "_children": [{"__id__": 58}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 57}, "_layoutSize": {"__type__": "cc.Size", "width": 384, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "c2y6/KmM9MPZurzvJciZkY"}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 57}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "bblo0J7AdGp4KG/xp+R0Aq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 57}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "2d+TWpU75MFJE9ugKr/bvX"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 57}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "7bZuGCVZhGhox3T7+roRM4"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 57}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "2fn3RTqu9N84KvLy0kRk7B"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 57}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "b0zR+coXlFNpsGKopUk7/V"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 57}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 63}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "99244uKJ1PgrsNyAGlgI2c"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 64}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 64}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "6c0bQIb35LO79NhsXt8DwL"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 65}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "73lJqrS3BKZJd2LKtMw1uo"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 66}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 66}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "98h5kr2qZGNY1Le6LuY8gV"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 67}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "394+jTOoNEiKSAUDFw8s0I"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 68}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 68}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "7fTJjRT8pJSqOjaseROehg"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 69}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "ffLIFxzxJIQYGmUu/8v/A+"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 70}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 70}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "f8KJ0eEF1O3ZHYaoQ77jzs"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 71}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 71}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "15/2HYebhOMrfDMiYJizgP"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 72}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 72}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "2fGVPG6q1INq/kwnddxatO"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 73}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "597In38rlOBZe53L790TB1"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, -352, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 74}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 74}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "99hWXRlWhNW6pr/4UhmfNk"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, -416, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 75}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 75}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "98Gh0WplVPrqUoIdCaTdLF"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -352, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 76}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 76}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "46nTzRD95P5pcbbtijtEpP"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -416, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeGroundRight", "_parent": {"__id__": 3}, "_children": [{"__id__": 78}, {"__id__": 79}, {"__id__": 80}, {"__id__": 85}, {"__id__": 86}, {"__id__": 87}, {"__id__": 88}, {"__id__": 89}, {"__id__": 90}, {"__id__": 91}, {"__id__": 92}, {"__id__": 93}, {"__id__": 94}, {"__id__": 95}, {"__id__": 96}, {"__id__": 97}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 77}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 77}, "_offset": {"__type__": "cc.Vec2", "y": -224}, "_size": {"__type__": "cc.Size", "width": 384, "height": 448}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "16fxhgs0BOlLmCUnt/RZf7"}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 448}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [448, -140, 0, 0, 0, 0, 1, 0.8, 0.8, 0.8]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 78}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 78}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "a5bBDgzIpJ8ZyYEU6MMeVi"}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 384}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 79}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 79}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "8dnQ8wKqhLO4gjcb0UUghu"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 77}, "_children": [{"__id__": 81}, {"__id__": 82}, {"__id__": 83}, {"__id__": 84}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 80}, "_layoutSize": {"__type__": "cc.Size", "width": 256, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "deAmh1+oZCWLdWx4KmFvmm"}, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 80}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 81}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "a1q9R9TLNEKabZr9BCbFRR"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 80}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 82}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "e7F9ep6o5IlK5R2DzfOFtG"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 80}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 83}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "833ejvc6tJ/4zJPoR2NLrV"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 80}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 84}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "2f/CljNAxEZ6cdrK3Qac9U"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 85}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 85}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "38o6bCIGRJtpZc8oHtR3ez"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 86}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 86}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "4a48Duc/tJHaVwspd0eeao"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 87}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 87}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "93ZWn9LgZMJoJiKs1rmLQk"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 88}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 88}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "f73XAMpLpKXLLEdb0zEwHz"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 89}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 89}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "88RNpUHeRESKpS1JTGctag"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 90}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 90}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "8dTvjDGG9PAbFJ9W8T8oLJ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 91}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 91}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "70QZcMKONGNou/7fp71oka"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 92}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 92}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "5dVFMxoBtFoqLKX8Inkdm+"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 93}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 93}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "e7PkkugUtGPY62UYOQP3Rl"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 94}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 94}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "0bsGwLnaNE+ozvKOkyHRyt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -352, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 95}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 95}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "06ws+FtZJOGLban4+FwPJp"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -416, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 96}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 96}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "19Va0DvAVDmZ6NU939Ti18"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, -352, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 77}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 97}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 97}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0e2KjXkNxMT6U3QXyhB329"}, "fileId": "4eq/l8ZL9BuoCXli5mOnVd"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, -416, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], {"__type__": "cc.AnimationClip", "_name": "map264PlatLeft", "_duration": 10, "wrapMode": 2, "curveData": {"paths": {"spine": {"props": {"x": []}}, "dead1": {"props": {"x": []}, "comps": {"cc.RigidBody": {"linearVelocity": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 4, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 4.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 4.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 9, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 9.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 9.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 40}}]}}}, "dead2": {"props": {"x": []}, "comps": {"cc.RigidBody": {"linearVelocity": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 4, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 4.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 4.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 9, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 9.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 9.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 40}}]}}}}, "comps": {"cc.RigidBody": {"linearVelocity": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 4, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 4.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 4.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 9, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 9.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 9.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 40}}]}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a385", "texture": "1dfa72800", "rect": [588, 479, 46, 56], "offset": [0, 0], "originalSize": [46, 56], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a384", "texture": "1dfa72800", "rect": [849, 301, 46, 56], "offset": [0, 0], "originalSize": [46, 56], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a364", "texture": "1dfa72800", "rect": [492, 915, 98, 92], "offset": [0, 0], "originalSize": [98, 92], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a363", "texture": "1dfa72800", "rect": [596, 915, 98, 92], "offset": [0, 0], "originalSize": [98, 92], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.AnimationClip", "_name": "map264PlatRight", "_duration": 10, "wrapMode": 2, "curveData": {"comps": {"cc.RigidBody": {"linearVelocity": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 4, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 4.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 4.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 9, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 9.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 9.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 40}}]}}, "paths": {"dead1": {"comps": {"cc.RigidBody": {"linearVelocity": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 4, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 4.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 4.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 9, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 9.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 9.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 40}}]}}}, "dead2": {"comps": {"cc.RigidBody": {"linearVelocity": [{"frame": 0, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 4, "value": {"__type__": "cc.Vec2", "x": -40}}, {"frame": 4.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 4.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 5, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 9, "value": {"__type__": "cc.Vec2", "x": 40}}, {"frame": 9.016666666666667, "value": {"__type__": "cc.Vec2"}}, {"frame": 9.983333333333333, "value": {"__type__": "cc.Vec2"}}, {"frame": 10, "value": {"__type__": "cc.Vec2", "x": 40}}]}}}}}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a382", "texture": "1dfa72800", "rect": [526, 73, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [20, 20, 20, 20]}}]