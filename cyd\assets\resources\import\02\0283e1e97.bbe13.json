[{"__type__": "cc.SpriteFrame", "content": {"name": "art04", "texture": "1651102e6", "rect": [834, 3, 478, 106], "offset": [0, 0], "originalSize": [478, 106], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vStageIWannerChooseDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}, {"__type__": "cc.Node", "_name": "CC_nodeList", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a3v+5rGEJM/pzy62FS9mWS"}, "fileId": "db7tuMcwtOabEq8WRoU4LR"}, "_contentSize": {"__type__": "cc.Size", "width": 754.8, "height": 300}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 4}, {"__type__": "cc.Node", "_name": "CC_nodeTitleBar", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a3v+5rGEJM/pzy62FS9mWS"}, "fileId": "92hF1flfhIeY/xAAX4i7ov"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 251, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a3v+5rGEJM/pzy62FS9mWS"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "art05", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3fbapAA+BFhpIZYA1Gefl2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a3v+5rGEJM/pzy62FS9mWS"}, "fileId": "dfNn3hoO5EyYIaa3coX1+F"}, "_contentSize": {"__type__": "cc.Size", "width": 825, "height": 427}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "art04", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dyiAzHHxBYJyI0giqccU/"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a3v+5rGEJM/pzy62FS9mWS"}, "fileId": "0ezgkPo8hKM5I5fcSwL/wX"}, "_contentSize": {"__type__": "cc.Size", "width": 478, "height": 106}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 1}, "_children": [{"__id__": 5}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 4}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a3v+5rGEJM/pzy62FS9mWS"}, "fileId": "8erXG4TNxAJJGdfI6cxUC0"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [380, 200, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b09", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48+6YuBptMr4rHwXDDBRsQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a3v+5rGEJM/pzy62FS9mWS"}, "fileId": "9er29umqJEi5s25D1F0dPd"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTouchMask", "_parent": {"__id__": 1}, "_active": false, "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 6}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "a3v+5rGEJM/pzy62FS9mWS"}, "fileId": "9cGHb+xupIr5Q/yk81npzs"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}]]