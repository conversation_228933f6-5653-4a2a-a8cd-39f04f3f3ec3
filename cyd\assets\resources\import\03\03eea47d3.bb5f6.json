[{"__type__": "cc.SpriteFrame", "content": {"name": "a6", "texture": "14290bb4b", "rect": [3, 3, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a19", "texture": "14290bb4b", "rect": [3, 249, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a18", "texture": "14290bb4b", "rect": [495, 859, 237, 152], "offset": [0, 0], "originalSize": [237, 152], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a3", "texture": "14290bb4b", "rect": [185, 3, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vStageEnterButtonNew", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vStageEnterButtonNew", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "f26qyoIYZPJrJCCFCagVBK"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-389.5, 104, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonEnter", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 2}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "e4zKKGIUxAjKLbCsphkxtQ"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeSkating", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3fIVzNLgdBQ55qWM8YoN5a"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "5dRcrhqA5De6r3LnJTujAJ"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeSlideBlock", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "94GIdqyHJHhKZ9ARQdQB0q"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "10weQNW/JPtYKlJv7Bw47P"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePortal", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2ehfsdVqNPF5wR6ZAdzFUR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "dc3PiJnvpMV7g+T8BqNO7G"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node<PERSON>ewyear", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b5+6HfjihOhozB116nGzFT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "59/GCw42NOWZwDrNG2UKtw"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGame186", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5eUgZ1/ZxC8Jo9jXcZ1dzL"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "8e2903F35ABYXHVfYpayXX"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeFeralCat", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a3xK6TKYFJbJ2MhmKcdRbJ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "d7JpEGbsRH+4Os3A4G6WOT"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePointLine", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "cfSTFCFU5Esosju+F/WyHo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "19UF2I5ndNqqz06y5tI5Vs"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDFinder", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00STnTWkVLsJ8hkcwbkG9K"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "6daB/XbjhD1rFbBBnLlr3x"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGame198", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "26Z3GUhI9Gr7zsV7kSAoFt"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "7dybLUG8hJ4bno0HSVrGaY"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node<PERSON><PERSON>ner", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "afGSUVPKNHYpClHDaw73ca"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "e8/Oo6VH9Leob/xAfl2cO+"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeWordGame", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "24ZvjOXChDNoXxa7cgW4DS"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "9cKvnuFvVGx5grkf+Op43a"}, "_contentSize": {"__type__": "cc.Size", "width": 185, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePuzzle", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "57CukSPSBF8olAln1jaOzV"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "caj9f7rZhLc7v4/rRRlpdi"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePFinder", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "73GvtxoyBHc7zHcwosgxDQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "f5b7PndDNAoJHe0gQVeDTi"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBlackWhite", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "61XzDsr/xDrYNgGlkHFhg2"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "fbgI5kbYNF0rzU9WvoElR4"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeWanShengJie", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "beCwlyUixGT77XuGje5btC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "84mNN3hL9PYoBv/mn3KAvc"}, "_contentSize": {"__type__": "cc.Size", "width": 237, "height": 152}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9.5, -12, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGridMove", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6fa6xJ25hA8LPz4pacJOQi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "26b+KcT2xHvpE+KyVYVNOz"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLine", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f8ecqm5ZJEYouwEV7phHYt"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "16bIHCDIdCzpVH8m/4tgzu"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeIwanner2", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "15sj/uwYtM/LvrChMxGNjF"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "70hX/JKulGO45J++rbLX5F"}, "_contentSize": {"__type__": "cc.Size", "width": 237, "height": 152}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [9.5, -12, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLineCar", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "09oLq/Vu5E/pEmLTJwEYZS"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "5cpvMmNN5MSIxs364TTak6"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeReaction", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a6wslThuJBn4RjsLoUyqVC"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "2eW9k6wHZCk7DDL5j4uyEk"}, "fileId": "8aZQyRqc9Iip1CNEuHIPC2"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "a2", "texture": "14290bb4b", "rect": [249, 185, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a8", "texture": "14290bb4b", "rect": [3, 431, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a7", "texture": "14290bb4b", "rect": [249, 431, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a15", "texture": "14290bb4b", "rect": [431, 3, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a14", "texture": "14290bb4b", "rect": [431, 185, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a5", "texture": "14290bb4b", "rect": [495, 367, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a4", "texture": "14290bb4b", "rect": [3, 613, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a10", "texture": "14290bb4b", "rect": [249, 613, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a9", "texture": "14290bb4b", "rect": [495, 613, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a20", "texture": "14290bb4b", "rect": [3, 795, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a12", "texture": "14290bb4b", "rect": [249, 795, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a11", "texture": "14290bb4b", "rect": [677, 3, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a1", "texture": "14290bb4b", "rect": [738, 741, 237, 152], "offset": [0, 0], "originalSize": [237, 152], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a13", "texture": "14290bb4b", "rect": [677, 249, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a17", "texture": "14290bb4b", "rect": [677, 495, 240, 176], "offset": [0, 0], "originalSize": [240, 176], "rotated": 1, "capInsets": [0, 0, 0, 0]}}]