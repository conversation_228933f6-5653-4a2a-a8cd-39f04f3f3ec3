[{"__type__": "cc.SpriteFrame", "content": {"name": "s011", "texture": "1e025b9e6", "rect": [3, 396, 864, 470], "offset": [0, 0], "originalSize": [864, 470], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vStagePFinderChooseDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__type__": "cc.Node", "_name": "CC_nodeTitleBar", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b5xJNr/0ZII5rDf5FgbBg0"}, "fileId": "0d2T073jZIkZdJlIZ+sEEh"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 251, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b5xJNr/0ZII5rDf5FgbBg0"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 1}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b5xJNr/0ZII5rDf5FgbBg0"}, "fileId": "96XQcUIOxFj4OMCgJd3dmg"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "s011", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "6d7fouU2VJ8rNiccGTwmA8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b5xJNr/0ZII5rDf5FgbBg0"}, "fileId": "f1T3DvnaZGIa/6ZIhpyhXg"}, "_contentSize": {"__type__": "cc.Size", "width": 864, "height": 470}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeContent", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Layout", "node": {"__id__": 4}, "_layoutSize": {"__type__": "cc.Size", "width": 778, "height": 380}, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b5xJNr/0ZII5rDf5FgbBg0"}, "fileId": "be3kOT7FpL1JZ3TaJ+L7bd"}, "_contentSize": {"__type__": "cc.Size", "width": 778, "height": 380}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -13.776, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 1}, "_children": [{"__id__": 6}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 5}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b5xJNr/0ZII5rDf5FgbBg0"}, "fileId": "d8NwS+HOBPpbuhKI9xeD6D"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [400.531, 202.629, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "b09", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48+6YuBptMr4rHwXDDBRsQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b5xJNr/0ZII5rDf5FgbBg0"}, "fileId": "3dsfjW9j5I0any1vjCFAED"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTouchMask", "_parent": {"__id__": 1}, "_active": false, "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 7}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b5xJNr/0ZII5rDf5FgbBg0"}, "fileId": "4es6UBvphNJqnVfLYejJ49"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}]]