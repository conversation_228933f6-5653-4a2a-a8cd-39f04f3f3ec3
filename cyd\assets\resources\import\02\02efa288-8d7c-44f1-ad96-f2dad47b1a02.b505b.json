[{"__type__": "cc.Prefab", "_name": "vEditorNodeController", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vEditorNodeController", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "35DeDnWfNDMJnN5Afg6cJU"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 500}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "nodeContent", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 9}, {"__id__": 14}, {"__id__": 36}], "_components": [{"__type__": "cc.Widget", "node": {"__id__": 2}, "alignMode": 2, "_alignFlags": 45}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "58kTjGibZDyLJS6dlHpf3t"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 500}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGraphics", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Widget", "node": {"__id__": 3}, "alignMode": 0, "_alignFlags": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "bfDe5A0cNFqrcqznmoJieQ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 250, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "layoutBg", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9bvaMerUlDyary99mJa6xp"}, "_type": 1, "_sizeMode": 0}, {"__type__": "cc.Layout", "node": {"__id__": 4}, "_layoutSize": {"__type__": "cc.Size", "width": 320, "height": 500}}, {"__type__": "cc.Widget", "node": {"__id__": 4}, "alignMode": 0, "_alignFlags": 45, "_originalWidth": 200, "_originalHeight": 150}, {"__type__": "cc.BlockInputEvents", "node": {"__id__": 4}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "f18e/jMf9HLZTre/J4ZcI/"}, "_opacity": 127, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 500}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "nodeLeft", "_parent": {"__id__": 2}, "_children": [{"__id__": 6}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxX", "_parent": {"__id__": 5}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "8bKMVYWjBEZ5t03wVggAAQ"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 104.75999999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxY", "_parent": {"__id__": 5}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "fc1PWwMxhOzrRXXiz2OqKw"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 50.75999999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 7}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxRot", "_parent": {"__id__": 5}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "e90vh69ltKMJ861YHgbTlm"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37.480000000000004, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 8}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxOrder", "_parent": {"__id__": 5}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "8fAjvV6dZGt5rOXYBrbOGG"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -125.72000000000001, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.Widget", "node": {"__id__": 5}, "alignMode": 0, "_alignFlags": 13, "_right": 150, "_bottom": 160}, {"__type__": "cc.Layout", "node": {"__id__": 5}, "_layoutSize": {"__type__": "cc.Size", "width": 156, "height": 340}, "_N$layoutType": 2, "_N$paddingLeft": 6, "_N$paddingRight": 6, "_N$paddingTop": 6, "_N$paddingBottom": 6, "_N$spacingY": 4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "d9QFZrlTJFO57Ciw5lneld"}, "_contentSize": {"__type__": "cc.Size", "width": 156, "height": 340}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-82, 80, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelPos", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "坐标调整：", "_N$string": "坐标调整：", "_fontSize": 24, "_lineHeight": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "33c2SOY9JJcIOHfz7ZE/dU"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 148.88, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "旋转度：", "_N$string": "旋转度：", "_fontSize": 24, "_lineHeight": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "a7F0zRpQxJCLQlV+MFZpuI"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 6.639999999999992, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "排序：", "_N$string": "排序：", "_fontSize": 24, "_lineHeight": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "63bKTKykdOw5ox5KDiqYtJ"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -81.60000000000001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "nodeRight", "_parent": {"__id__": 2}, "_children": [{"__id__": 10}, {"__id__": 11}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxScaleX", "_parent": {"__id__": 9}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "9ch7vU7VJAtbXPrqUKKe2k"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 60.75999999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxScaleY", "_parent": {"__id__": 9}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "e6QZVnkAFCwpylD+GMt1aj"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 6.759999999999991, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 13}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxSkewX", "_parent": {"__id__": 9}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "5fQx0kU0FFi695MJ0S87Bx"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -81.48, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxSkewY", "_parent": {"__id__": 9}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "c2X/Ws4INESZ0suonJU3dZ"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -135.48000000000002, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.Widget", "node": {"__id__": 9}, "alignMode": 0, "_alignFlags": 37, "_left": -3, "_bottom": 160, "_originalWidth": 156}, {"__type__": "cc.Layout", "node": {"__id__": 9}, "_layoutSize": {"__type__": "cc.Size", "width": 156, "height": 340}, "_N$layoutType": 2, "_N$paddingLeft": 6, "_N$paddingRight": 6, "_N$paddingTop": 6, "_N$paddingBottom": 6, "_N$spacingY": 4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "080jelGdVHLJWXlhp65dI5"}, "_contentSize": {"__type__": "cc.Size", "width": 156, "height": 340}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [82, 80, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "缩放：", "_N$string": "缩放：", "_fontSize": 24, "_lineHeight": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "24OqM+Y4lKW4edsZr8OyWC"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 148.88, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonEqualScale", "_parent": {"__id__": 9}, "_children": [{"__id__": 12}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 11}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "99kTWByoJF0rZ9MpsOGL0k"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12, 109.75999999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "非等比缩放", "_N$string": "非等比缩放", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "b7UmdfJI1BlY+K4w9hDbJK"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "斜切：", "_N$string": "斜切：", "_fontSize": 24, "_lineHeight": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "88xJHlzApOg6eVzIyZIl56"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37.36000000000001, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "nodeBottom", "_parent": {"__id__": 2}, "_children": [{"__id__": 15}, {"__id__": 17}], "_components": [{"__type__": "cc.Widget", "node": {"__id__": 14}, "alignMode": 0, "_alignFlags": 44}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "5413lh/dhMgInlFyHompwK"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 160}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -170, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 14}, "_children": [{"__id__": 16}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxApx", "_parent": {"__id__": 15}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "2cGGoOXmdDhZMioIB6wQQE"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 20.759999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeInputBoxApy", "_parent": {"__id__": 15}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "6b72fmGWZA3pkOuMunJUs1"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -33.24, 0, 0, 0, 0, 1, 1, 1, 1]}}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 15}, "_layoutSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_N$layoutType": 2, "_N$spacingY": 4}, {"__type__": "cc.Widget", "node": {"__id__": 15}, "alignMode": 0, "_alignFlags": 13, "_originalHeight": 160}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "7cg69i7ctKdaAn0unMOwgZ"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-80, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 15}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "锚点：", "_N$string": "锚点：", "_fontSize": 24, "_lineHeight": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "0dUPlmiSVCmqEiBpiJDGJs"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 64.88, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 14}, "_children": [{"__id__": 18}, {"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}, {"__id__": 28}, {"__id__": 30}, {"__id__": 32}, {"__id__": 34}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 17}, "_layoutSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_N$layoutType": 3, "_N$paddingLeft": 10, "_N$paddingRight": 10, "_N$paddingTop": 10, "_N$paddingBottom": 10, "_N$spacingX": 10, "_N$spacingY": 10}, {"__type__": "cc.Widget", "node": {"__id__": 17}, "alignMode": 0, "_alignFlags": 37, "_originalWidth": 160, "_originalHeight": 160}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "1eX1x0NmxPBIsQNBiHdjUQ"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [80, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorLeftTop", "_parent": {"__id__": 17}, "_children": [{"__id__": 19}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 18}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "45FCub3SxIQ5Nwix0kbFi8"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, 50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "↖", "_N$string": "↖", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "7cIpJKfidLq7bS1LvHiXM0"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorTop", "_parent": {"__id__": 17}, "_children": [{"__id__": 21}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 20}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "ffH89c8mhNobRNbHkTPKVn"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 20}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "↑", "_N$string": "↑", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "4euPXbP0RMro25X/xOi1Ef"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorRightTop", "_parent": {"__id__": 17}, "_children": [{"__id__": 23}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 22}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "d1zy6JQoVOc6eCQGNHD0Is"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "↗", "_N$string": "↗", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "feqjEnBN1MlZG1tIatrpEd"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorLeft", "_parent": {"__id__": 17}, "_children": [{"__id__": 25}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 24}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "f1Bx7k/TBFw6ZK2LYyhJYz"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 24}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "←", "_N$string": "←", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "2aNtiBu9tH3oMzBDsIsElL"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorCenter", "_parent": {"__id__": 17}, "_children": [{"__id__": 27}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 26}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "5bAg5SeaVMdquV5U3w6RG2"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "7bXP2JfHxKdJWJcbI2UucA"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorRight", "_parent": {"__id__": 17}, "_children": [{"__id__": 29}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 28}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "34sON7GmBABYv3AISqUySA"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "→", "_N$string": "→", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "63p3RMwzhJEbpY490+0Ipz"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorLeftBottom", "_parent": {"__id__": 17}, "_children": [{"__id__": 31}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 30}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "c1cfS1UKBInIi+tibnqV6C"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "↙", "_N$string": "↙", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "6dtlT2QOVCG4EVBIDh8WXT"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorBottom", "_parent": {"__id__": 17}, "_children": [{"__id__": 33}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 32}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "9dOiDmvd1I0bKQ0wFqlP6s"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "↓", "_N$string": "↓", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "085QRYSppAZ7Idwd8svnvx"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAnchorRightBottom", "_parent": {"__id__": 17}, "_children": [{"__id__": 35}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0BIwQ8D5Ml7nTNQbh1YlS"}, "_type": 1, "_sizeMode": 0}, {"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 34}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "26MdNlOEVOTK/trJICApDB"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [50, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Label", "_parent": {"__id__": 34}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "↘", "_N$string": "↘", "_fontSize": 20, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "0eKKEOY3ZA6L5uR2xwwiep"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePosDrag", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0276KIjXxE8a2W8trUexoC"}, "fileId": "90Vk93X0NJlrMVfiowSl5G"}, "_color": {"__type__": "cc.Color", "r": 116, "g": 99, "b": 99}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-220, 197, 0, 0, 0, 0, 1, 1, 1, 1]}}]