[[{"__type__": "cc.Prefab", "_name": "map6002", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map6002", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 43}, {"__type__": "cc.Node", "_name": "CC_nodePoint11", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "99KQ5MLj9LALl1F/sl3TWX"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [112.737, 142.984, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePoint22", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "99JM4MTFlEFKLhJqNHgScC"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-359.293, 231.89, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePoint111", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "5dCF2E1lBEaLRC+J7rrpHf"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185.205, 142.984, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePoint222", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "abUE+2jW1Hr7KfMysF2WhF"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [736.174, 359.475, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__id__": 44}, {"__id__": 47}, {"__id__": 50}, {"__id__": 79}, {"__id__": 80}, {"__id__": 114}, {"__id__": 115}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "2f1HJZZ4VITL0ty5jiQ+0Z"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-512, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 116}, {"__id__": 117}, {"__id__": 120}, {"__type__": "cc.Node", "_name": "CC_nodeLayer", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "93FgJHXLFGSYrn4OoYcXWm"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__id__": 122}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "18QN8RrPpFSa97AX/RyN/c"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 614.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTip", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "吃到鱼就过关了！", "_N$string": "吃到鱼就过关了！", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "b7LEN3JBpDzb4XMe8bCp+N"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeXueDi", "_parent": {"__id__": 2}, "_children": [{"__id__": 5}, {"__id__": 41}, {"__id__": 42}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "e7b66f7lJMuJw9ipqODriP"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 63}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1990.409, -141.793, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 4}, "_children": [{"__id__": 6}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "a6zBy37LZGCbyRUYWsPl4s"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-18.359, 34.636, 0, 0, 0, -0.6427876096865393, 0.766044443118978, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -80}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "belly", "_parent": {"__id__": 5}, "_children": [{"__id__": 7}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 6}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "default", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "walk", "_N$skeletonData": {"__uuid__": "8dmElHMLJIIJMnqAnRs0k0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "9aDJTrFLdN+5Gs/7gc33Wu"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 35}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 6}, "_children": [{"__id__": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "5a6V1XIBdOq7TNrXDCBmA3"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 7}, "_children": [{"__id__": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "5aix2Yar1HaadNZhxuvnvJ"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:belly", "_parent": {"__id__": 8}, "_children": [{"__id__": 10}, {"__id__": 12}, {"__id__": 13}, {"__id__": 15}, {"__id__": 17}, {"__id__": 18}, {"__id__": 25}, {"__id__": 26}, {"__id__": 28}, {"__id__": 35}, {"__id__": 36}, {"__id__": 37}, {"__id__": 39}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "2egmNDuRRBAJv/2v9cdTIX"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tail", "_parent": {"__id__": 9}, "_children": [{"__id__": 11}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "46oI5gU4BI8ra28BgDw70X"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -4, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tail", "_parent": {"__id__": 10}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 11}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "walk", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "walk", "_N$skeletonData": {"__uuid__": "5f4JLpSbpAdLzq19EXMg4/"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "69fFA8AyFFY4RW+fp7SixS"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 59}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLArm", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f66tGmnVhDtpNr5USyzV3S"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "4fmnnIyNNN25TdbN4W1RGL"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [26.647, 8.695, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lArm", "_parent": {"__id__": 9}, "_children": [{"__id__": 14}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "5d6BAOb5hLe6EhSMKAf7Nt"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.147, -3.805, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "arm_leftdown", "_parent": {"__id__": 13}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 14}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "walk", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "walk", "_N$skeletonData": {"__uuid__": "020WEKR7VFTJMAY09oI1+3"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "c552SZikRPAqbXmmPxo7NB"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lLeg", "_parent": {"__id__": 9}, "_children": [{"__id__": 16}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "261kAYI+ZKwpEavdPlhN5n"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.186, -3.537, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 15}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 16}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "26jeJlDdJM8ZUaRLSv6gVx"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "1fWS4OomdFgp/p8aysdl0s"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLLeg", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f66tGmnVhDtpNr5USyzV3S"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "3bOs3Qlw1AwYbhzsbTGA7y"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5.305, 8.963, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ass", "_parent": {"__id__": 9}, "_children": [{"__id__": 19}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "8fVtSFb4xPaodqwCoSh0oU"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ass", "_parent": {"__id__": 18}, "_children": [{"__id__": 20}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 19}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "58Z/sYGr9EW4dlXHxurIdN"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "c1EzAWHSBCd5UymTu9e9mt"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 19}, "_children": [{"__id__": 21}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "balC3T2lhKOoLffpi90NKW"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 20}, "_children": [{"__id__": 22}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "03Z2FQPDpCpIQfbKjvdf4/"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:point", "_parent": {"__id__": 21}, "_children": [{"__id__": 23}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "37kXyIVAFKM5/LJRofeBRD"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:ass", "_parent": {"__id__": 22}, "_children": [{"__id__": 24}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "61RFfb85xFRpDwynxl+f+6"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c11", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "908bH6NNpAR4DqR5lALaxP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "08ouTlhWFKbaDS1oe0rSmG"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-11.939, 25.696, 0, 0, 0, 0.7253743710122876, 0.6883545756937539, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 93}}, {"__type__": "cc.Node", "_name": "CC_nodeRLeg", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a39UH8wQZLk77tvZThHDw4"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "3b9tslg/tK77UMiobxV6NB"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-20.47, 8.829, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "rLeg", "_parent": {"__id__": 9}, "_children": [{"__id__": 27}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "d5Csb8tqxOn558yDm3uh0m"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.056, -3.6709999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "leg_rightdown", "_parent": {"__id__": 26}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 27}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "4egSN3MSNH2YttCSDNI4Po"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "e6M4yMvMhOo4zIeo0N2kd3"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "chest", "_parent": {"__id__": 9}, "_children": [{"__id__": 29}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "eaqvVlKPRMmqAgncXG1M/5"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -0.624, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "chest", "_parent": {"__id__": 28}, "_children": [{"__id__": 30}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 29}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "07yLDLBoVEEbmY8mTbxeY+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "89nrqsLN5N0rJNYUgg0rzi"}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 35}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 29}, "_children": [{"__id__": 31}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "4bnxoSXp9NopIrEy5xrMEg"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 30}, "_children": [{"__type__": "cc.Node", "_name": "ATTACHED_NODE:belly", "_parent": {"__id__": 31}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "12wwg/s2tBfZxccBScjeTX"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "3ezw4WrgNJ1YBJ3bIiWpR6"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:point", "_parent": {"__id__": 31}, "_children": [{"__id__": 33}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "7bPcdRJEVCMKziNeMlaSdB"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:chest", "_parent": {"__id__": 32}, "_children": [{"__id__": 34}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "eeIjSf7gdBoLcJs6EL0QSU"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c12", "_parent": {"__id__": 33}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "38hYw/3WZAVaYXIH04a9dh"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "e5mc4v5U9JlZf3k/oJC1aw"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-10.582, -2.313, 0, 0, 0, 0.7193398003386512, 0.6946583704589973, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 92}}, {"__type__": "cc.Node", "_name": "c10", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c38nIi+z1KULWbUMznLHZW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "db8G41HoZKQIkjmDijP404"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [17, 39.376, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeRArm", "_parent": {"__id__": 9}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a39UH8wQZLk77tvZThHDw4"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "97Ia2eXrJB0ri3VTuwGGV6"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [12.415, 8.695, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "rArm", "_parent": {"__id__": 9}, "_children": [{"__id__": 38}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "8dspCEjk1BsY8WIvpHNnLG"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.906, -3.805, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "arm_rightdown", "_parent": {"__id__": 37}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 38}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "d3cPE0uDtKhKnnSzkPicyj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "a74sI0EqtF2q7fyDDS9DqS"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "head", "_parent": {"__id__": 9}, "_children": [{"__id__": 40}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "3cUo3UUpZGVJwqtx5dbABt"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.109, -4, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "head", "_parent": {"__id__": 39}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 40}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "walk", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "walk", "_N$skeletonData": {"__uuid__": "a5POKYvMRGRrD1dnfK35x1"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "eeEH71+StOMq+/bBYLm00v"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 80.01}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON>", "_parent": {"__id__": 4}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 41}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "<PERSON><PERSON><PERSON>", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "<PERSON><PERSON><PERSON>", "_N$skeletonData": {"__uuid__": "ef8owX+eFPF6tWQQY1tnPF"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "56uS6/TCZHYIacTzsfmsUy"}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [40.497, -21.647, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5]}}, {"__type__": "cc.Node", "_name": "x<PERSON>i", "_parent": {"__id__": 4}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 42}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle02", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle02", "_N$skeletonData": {"__uuid__": "491hIr26BDjJMonvfKhFaG"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "84dEK7PfdOiJxgYoHri5VM"}, "_contentSize": {"__type__": "cc.Size", "width": 169, "height": 63}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeCanvas", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Graphics", "node": {"__id__": 43}, "_materials": [{"__uuid__": "a1U5RdJRFMFL57BdJC9H1X"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "8aOCFLUnZP6Jl3dNyg7B+i"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeHouse", "_parent": {"__id__": 2}, "_children": [{"__id__": 45}, {"__id__": 46}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 44}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 44}, "_offset": {"__type__": "cc.Vec2", "x": 60, "y": 20}, "_size": {"__type__": "cc.Size", "width": 422, "height": 210}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "fdqEHiS1VOR6g7m+vfoPtj"}, "_contentSize": {"__type__": "cc.Size", "width": 318, "height": 228}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-361.815, -56.94, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "c30", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "30RM/cJZxD+IwT5rFZUq49"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "8d8XvO9BlHGIMeDgAM95ma"}, "_contentSize": {"__type__": "cc.Size", "width": 242, "height": 176}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 211.077, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c29", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "41YKlNGRlD65UhgulvNP3Z"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "afmWvZqqVF6ZJegTYTsUQ4"}, "_contentSize": {"__type__": "cc.Size", "width": 442, "height": 202}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [60, 30, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTriggerBox", "_parent": {"__id__": 2}, "_children": [{"__id__": 48}, {"__id__": 49}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 47}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 47}, "_size": {"__type__": "cc.Size", "width": 10, "height": 250}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "662Lf1EDxF2Yk9YasTfnjw"}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 250}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [150, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "box", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "28wQ4sbF9Nr773KxBQVlKE"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 48}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 48}, "_size": {"__type__": "cc.Size", "width": 100, "height": 20}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 48}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "8d42+cIO9IorK4Lc4d27Vd"}, "_contentSize": {"__type__": "cc.Size", "width": 104, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 127.931, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "c06", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "66PfOeTNFNG425WWY5K2OV"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "97lD9vtkVDqLYl8/G9904F"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 400}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1, -96.127, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground2", "_parent": {"__id__": 2}, "_children": [{"__id__": 51}, {"__id__": 52}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 50}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 50}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 1280, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "4dcIVLMChPOK3IWh2Yz17i"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 51}, "_alignFlags": 45, "_left": -64, "_right": -64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "d4q9Ip13RBR4DQMEgH7N0W"}, "_contentSize": {"__type__": "cc.Size", "width": 1408, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 50}, "_children": [{"__id__": 53}, {"__id__": 54}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 64}, {"__id__": 65}, {"__id__": 66}, {"__id__": 67}, {"__id__": 69}, {"__id__": 70}, {"__id__": 72}, {"__id__": 73}, {"__id__": 74}, {"__id__": 75}, {"__id__": 76}, {"__id__": 77}, {"__id__": 78}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 52}, "_layoutSize": {"__type__": "cc.Size", "width": 1408, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "1bUYKqJJ1Lba7oytgTZqr1"}, "_contentSize": {"__type__": "cc.Size", "width": 1408, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "b3SP5CQoNMyYuzYXOOZv7T"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-672, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 52}, "_children": [{"__id__": 55}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "6d4Cs9aJtMEL1EhYcM28Xi"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 54}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 55}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "5aaDojGhxKOJAENuISfUMy"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "0aquZbdYpE+bjjeKdAWz57"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "c1idIErwpBMo86YVzFUBpv"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "81AEUMwmFB6p9ZdB5pT1eu"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "1cLS54Lk5HWY5BYkOxqQyt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "3fzno+F4BNxqS8gUalejzq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "b1QlEV2pJJ0KNQcDWoGwDC"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_children": [{"__id__": 63}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "fdrOf67HxEorMczeT3AcUR"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 62}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 63}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "1dyMIaDXlKV7TDx5L8VH4B"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, 28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 64}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "f4idKpNLhP1JJCCwr8cVBh"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "a1M9beJwJM5rHrlR4V6gC1"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 66}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "12PqQG25tHh4llQ4AAUwKv"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_children": [{"__id__": 68}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "30AqeS1PJHt5t60dMLUkwK"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 67}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 68}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "52240IQT1DZp0CuqdU6Uw4"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.9759999999999991, 23.812, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "e9en4sP55Jw5WM9MACbcsv"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_children": [{"__id__": 71}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 70}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "79QFDNGG1N1J21efmyrSqB"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little04", "_parent": {"__id__": 70}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 71}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78LL5TK2RJ/7QmPvmVHdJr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "ce2m+dlwJP5Yzmj35aBaTB"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 7.955, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 72}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "2ai7ppmSBCo6P6Y0BaZLSJ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "1cMgAS8hxJCp8FhGfEN6/Q"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 74}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "e8LzibAr9H4K7/Sj5vxHq7"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 75}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "90dvmzEJVA0YlxMl+82D+T"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 76}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "30slS6dftJtIgC/voQ4Gtp"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 77}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "519+FkIUJGPKrPciWf9eh9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 78}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "63vZm+PP1Kma7WAByOixlI"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [672, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeFish", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 79}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9bOKYTu1dDuLE2s7Vzrmun"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 79}, "_type": 0, "_allowSleep": false, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 79}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 74, "height": 56}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 79}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "cczVDM7wBDQrBDygvqJCxm"}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 56}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [320, -85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeCat", "_parent": {"__id__": 2}, "_children": [{"__id__": 81}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 80}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "1a78fb09b1HQKAGtBMt1Waw", "node": {"__id__": 80}, "root": {"__id__": 81}, "head": {"__id__": 112}, "chest": {"__id__": 102}, "leftArm": {"__id__": 88}, "rightArm": {"__id__": 110}, "ass": {"__id__": 93}, "leftLeg": {"__id__": 90}, "rightLeg": {"__id__": 100}, "tail": {"__id__": 85}}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 80}, "_offset": {"__type__": "cc.Vec2", "y": 18}, "_size": {"__type__": "cc.Size", "width": 64, "height": 44}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "394f4ZYptDi40Bnv25gjFq"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-394.662, 79.976, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "belly", "_objFlags": 512, "_parent": {"__id__": 80}, "_children": [{"__id__": 82}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 81}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "default", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "walk", "_N$skeletonData": {"__uuid__": "8dmElHMLJIIJMnqAnRs0k0"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "86dCBXfUlLHrfSdhfDZ+qI"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 35}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 81}, "_children": [{"__id__": 83}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "b7WpCnNOFD9KhxKlML1RZE"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 82}, "_children": [{"__id__": 84}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "22pJeYPOtJz60NiiM2Pl0Q"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:belly", "_parent": {"__id__": 83}, "_children": [{"__id__": 85}, {"__id__": 87}, {"__id__": 88}, {"__id__": 90}, {"__id__": 92}, {"__id__": 93}, {"__id__": 99}, {"__id__": 100}, {"__id__": 102}, {"__id__": 108}, {"__id__": 109}, {"__id__": 110}, {"__id__": 112}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "6ab2iR8vZDc5Z6kScvH/mS"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tail", "_parent": {"__id__": 84}, "_children": [{"__id__": 86}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "0edzN6srBEbL411TQY3cEh"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -4, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tail", "_parent": {"__id__": 85}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 86}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "5f4JLpSbpAdLzq19EXMg4/"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "e5i5Oz64xBCqBYCdpNmiR8"}, "_contentSize": {"__type__": "cc.Size", "width": 30, "height": 59}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLArm", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 87}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f66tGmnVhDtpNr5USyzV3S"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "efTx6A/YtGybljiKbztr5E"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [26.647, 8.695, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lArm", "_parent": {"__id__": 84}, "_children": [{"__id__": 89}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "54PkwuVHVPObgBcVwqATOD"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.147, -3.805, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "arm_leftdown", "_parent": {"__id__": 88}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 89}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "020WEKR7VFTJMAY09oI1+3"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "2ctNU5a4FF5ooKq8ifDlW1"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lLeg", "_parent": {"__id__": 84}, "_children": [{"__id__": 91}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "066vRWfN1ISp+SAGMRvXtA"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.186, -3.537, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 90}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 91}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "26jeJlDdJM8ZUaRLSv6gVx"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "24+jiggidLs4ovaBHZFxaB"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLLeg", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 92}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f66tGmnVhDtpNr5USyzV3S"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "fepNqPORZAhqx1/y2QGri5"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5.305, 8.963, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ass", "_parent": {"__id__": 84}, "_children": [{"__id__": 94}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "520Rgbcf1Ea5aeiGWGYlT9"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ass", "_parent": {"__id__": 93}, "_children": [{"__id__": 95}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 94}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "58Z/sYGr9EW4dlXHxurIdN"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "472S03nG1FtqH/h25el2Fa"}, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 94}, "_children": [{"__id__": 96}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "29yqw32KlBDIPqlMKr/hia"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 95}, "_children": [{"__id__": 97}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "78Z5xqoFVGjJkqQ4N1NkEB"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:point", "_parent": {"__id__": 96}, "_children": [{"__type__": "cc.Node", "_name": "ATTACHED_NODE:ass", "_parent": {"__id__": 97}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "ceKwaC44ROwoFyANsx0Mnr"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 98}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "5cwwkW2hhHLL2lanGa/SrR"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c11", "_parent": {"__id__": 97}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 98}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "908bH6NNpAR4DqR5lALaxP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "72eFUyR+1CBIb4+N+EkVJF"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [39, -11, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "CC_nodeRLeg", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 99}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a39UH8wQZLk77tvZThHDw4"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "aeforxL5JKSqDiKGr+VIyU"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-20.47, 8.829, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "rLeg", "_parent": {"__id__": 84}, "_children": [{"__id__": 101}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "dcDsvbnnVLvaPZ3bM1jDkG"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.056, -3.6709999999999994, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "leg_rightdown", "_parent": {"__id__": 100}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 101}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "4egSN3MSNH2YttCSDNI4Po"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "57/+yfdgxJ5b9AwR/eGzie"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "chest", "_parent": {"__id__": 84}, "_children": [{"__id__": 103}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "47hjVR2TJBRLYyDfvqV7WH"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -0.624, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "chest", "_parent": {"__id__": 102}, "_children": [{"__id__": 104}], "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 103}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "07yLDLBoVEEbmY8mTbxeY+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "8dmNiUkb1Krp4h5WjuBzms"}, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 35}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE_TREE", "_parent": {"__id__": 103}, "_children": [{"__id__": 105}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "75tWugKbBPELxprIpkzPXT"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:root", "_parent": {"__id__": 104}, "_children": [{"__type__": "cc.Node", "_name": "ATTACHED_NODE:belly", "_parent": {"__id__": 105}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "e27mRlB75JE5yhuNNf9VDc"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 106}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "535bkmnOxAIa64hqwPziD0"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ATTACHED_NODE:point", "_parent": {"__id__": 105}, "_children": [{"__type__": "cc.Node", "_name": "ATTACHED_NODE:chest", "_parent": {"__id__": 106}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "9asSeuX/dGXZIjyjPODDp5"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 107}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "56be72gVJNhY3o7/8nyKe7"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c12", "_parent": {"__id__": 106}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 107}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "38hYw/3WZAVaYXIH04a9dh"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "15oaqhYDBCDroos9KDrNuL"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [39.821, -14.419, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -90}}, {"__type__": "cc.Node", "_name": "c10", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 108}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c38nIi+z1KULWbUMznLHZW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "88zSciwJ9FerN141RDz5cc"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 180}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [17, 39.376, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeRArm", "_parent": {"__id__": 84}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 109}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a39UH8wQZLk77tvZThHDw4"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "57Rp4BaV5DPrJeS+BErP1N"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [12.415, 8.695, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "rArm", "_parent": {"__id__": 84}, "_children": [{"__id__": 111}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "44HFY9stNOZolfakQMenC3"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.906, -3.805, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "arm_rightdown", "_parent": {"__id__": 110}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 111}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "d3cPE0uDtKhKnnSzkPicyj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "a2tU2Ix05E4ZNjv7WM50As"}, "_contentSize": {"__type__": "cc.Size", "width": 13, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "head", "_parent": {"__id__": 84}, "_children": [{"__id__": 113}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "f0FtW0vs9GFa6MJoIFsY08"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.109, -4, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "head", "_parent": {"__id__": 112}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 113}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "a5POKYvMRGRrD1dnfK35x1"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "93KojYjsVHooKh+DTYwT4j"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 80.01}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTrigger", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 114}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 114}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 5.1}, "_size": {"__type__": "cc.Size", "width": 50, "height": 10}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 114}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "ab9TulbplJX676anO5PQWk"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 10}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-579.074, -124.557, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeleaf", "_parent": {"__id__": 2}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 115}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "f5nF+lBuNClbldJ/dVoGgW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "74yP5qr+FAAofX3wo+5HSU"}, "_contentSize": {"__type__": "cc.Size", "width": 126, "height": 56}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-579.074, -130.159, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTriggerGround", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 116}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 116}, "_offset": {"__type__": "cc.Vec2", "y": 2.5}, "_size": {"__type__": "cc.Size", "width": 250, "height": 5}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 116}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "a7Knf7RsVOwoRP5OyEQfPz"}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [22.664, -122.545, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeDianJi", "_parent": {"__id__": 2}, "_children": [{"__id__": 118}, {"__id__": 119}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "e0S/zlYiBG3qohy1dkb2CB"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1265.948, 34.438, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c05", "_parent": {"__id__": 117}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 118}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "54jN9kwk1GDrkDpfM5lFK3"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "41ZroYujNOw6qS3f3qDIfJ"}, "_contentSize": {"__type__": "cc.Size", "width": 172, "height": 146}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -22.339, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "c04", "_parent": {"__id__": 117}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 119}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b6HFvuRgxP877G6VPIaD7c"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "34YyUiXChPVYgBMk/hvwvb"}, "_contentSize": {"__type__": "cc.Size", "width": 172, "height": 146}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -22.339, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 2}, "_children": [{"__id__": 121}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "2d+TLgCCpH963j+Z7l/dQy"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ui_qiaomen", "_parent": {"__id__": 120}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 121}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "q<PERSON>iu", "_preCacheMode": 0, "premultipliedAlpha": false, "timeScale": 0.5, "_animationName": "q<PERSON>iu", "_N$skeletonData": {"__uuid__": "40XD+qGMVOCJe522kgdX1D"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "96mod2k6xDhJ0vTPv6jUGq"}, "_contentSize": {"__type__": "cc.Size", "width": 155, "height": 155}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBoom", "_parent": {"__id__": 2}, "_children": [{"__id__": 123}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 122}, "_defaultClip": {"__uuid__": "c7KIY85FlJk7knYxSPaEff"}, "_clips": [{"__uuid__": "c7KIY85FlJk7knYxSPaEff"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "2fEWbpmQtCqYEwRESgewK2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [737.938, -740.679, 0, 0, 0, 0, 1, 0, 0, 0]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "c42", "_parent": {"__id__": 122}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 123}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "dfMpzfJDdBcpCXAxe47frR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "23y8gw07xNLbmI1JRNTJ7F"}, "fileId": "c2hsLWs79EP6pFvgvh2n9y"}, "_contentSize": {"__type__": "cc.Size", "width": 198, "height": 178}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [11.438, -7.149, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], {"__type__": "cc.SpriteFrame", "content": {"name": "c07", "texture": "72nvLafINDspzBDcBiMqHB", "rect": [0, 0, 104, 50], "offset": [0, 0], "originalSize": [104, 50], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c30", "texture": "38GdvXRI9JoIsuNZpwnmk3", "rect": [0, 0, 242, 176], "offset": [0, 0], "originalSize": [242, 176], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c29", "texture": "13Nm842CtDFLWU5IiHedlV", "rect": [0, 0, 442, 202], "offset": [0, 0], "originalSize": [442, 202], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c06", "texture": "f8/FrhMQdEo7+ZeGjeccmW", "rect": [0, 0, 24, 12], "offset": [0, 0], "originalSize": [24, 12], "capInsets": [0, 0, 0, 0]}}]