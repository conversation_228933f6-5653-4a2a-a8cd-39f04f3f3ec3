[{"__type__": "cc.Prefab", "_name": "map514", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map514", "_children": [{"__id__": 2}, {"__id__": 57}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 25}, {"__id__": 32}, {"__id__": 35}, {"__id__": 38}, {"__id__": 41}, {"__id__": 44}, {"__id__": 47}, {"__id__": 50}, {"__id__": 53}, {"__id__": 29}, {"__id__": 55}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "groud", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 3}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 3}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 640, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "70GihgbCVM+ZSno9rOKPqJ"}, "_contentSize": {"__type__": "cc.Size", "width": 640, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-420, -170, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 4}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "e4ypEsV51CrrD1Oj8NQtkt"}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 5}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "75wpkwwb1DvqDKpb8o3UDa"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 3}, "_children": [{"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 6}, "_layoutSize": {"__type__": "cc.Size", "width": 512, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "01Z7Y9BYFNxLjR7P1YhBFm"}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "a5t8CUCjpBj5jbtr4B0ccT"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "a4ZQzP8JlEzYMOBZWBpfAb"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "2dzIrfLX1DYIzjbYz9p7/V"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "f62qOKLnNC2LUhEGihdRys"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "008jXPFUtKqLHqr8OohGyC"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "5b0VevubxOD7x5ScSwByhf"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "9056gQiZRAebjQsYjPekSk"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 6}, "_children": [{"__id__": 15}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "541tIzSjJE+YmNAyz8ejFb"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "b3cvCLMRxN1qoM0c1gIawJ"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 16}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "2fVVSAZ61KgLz7qgPNXWL7"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 17}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "f4WU6/b9NN2KIPbnNb0rD8"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 18}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "50bIkntJlOAKmiiszuJ3wp"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 19}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "bahhYeLDROLIsh0CKsvyO/"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, -96, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 20}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "d3qOePMhZA2aBK+C0oOPfw"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 21}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "82IVHiTRtI6q77KKFwCZv9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 22}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "51jB+ti1hCGIremZcpPQq8"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 23}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "2e2oEMq8tD9JzJN8WhwW55"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 3}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 24}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "b6UO5VsoxEA4QgkOd2SteM"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 2}, "_children": [{"__id__": 26}, {"__id__": 27}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 25}, "_type": 0, "_gravityScale": -1.5}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "cc.RopeJoint", "node": {"__id__": 25}, "anchor": {"__type__": "cc.Vec2", "x": 0.5}, "connectedBody": {"__id__": 28}, "_maxLength": 215}, {"__type__": "59b16aRe/NFSL9WFTVWpxEy", "node": {"__id__": 25}, "ropeNode": {"__id__": 26}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "07bMub2vxK/YcZIuBhOQyr"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [251.898, 215, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 26}, "_type": 0, "_allowSleep": false}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 26}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": -107.5}, "_size": {"__type__": "cc.Size", "width": 3, "height": 215}}, {"__type__": "87875GeJxtIPr6+l3TEHNoR", "node": {"__id__": 26}, "bRefreshPhysicCollider": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "3en/iRB2xDsKCshotnBt3G"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 3, "height": 215}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b21", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "47gEt1rr5LaZdHVFOdit2e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "24xjDXOwxPirPO1LUwltTs"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 62}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.RigidBody", "node": {"__id__": 29}, "_allowSleep": false, "_gravityScale": 5, "_linearDamping": 0.1, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.Node", "_name": "CC_nodeDoorEnd", "_parent": {"__id__": 2}, "_children": [{"__id__": 30}, {"__id__": 31}], "_components": [{"__id__": 28}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 29}, "_friction": 1, "_size": {"__type__": "cc.Size", "width": 100, "height": 124}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 29}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "0f2DhRHQZEWKhhWpWLdRUA"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [252.275, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 29}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 30}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 30}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "f1+lzoe+1CwI4cwjgZuv1U"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -62, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 29}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 31}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "e9Ytwc4qtHKp+Jm1zxiEUc"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 85, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 2}, "_children": [{"__id__": 33}, {"__id__": 34}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 32}, "_type": 0, "_gravityScale": -1.5}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 32}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "cc.RopeJoint", "node": {"__id__": 32}, "anchor": {"__type__": "cc.Vec2", "x": 0.5}, "connectedBody": {"__id__": 28}, "_maxLength": 175}, {"__type__": "59b16aRe/NFSL9WFTVWpxEy", "node": {"__id__": 32}, "ropeNode": {"__id__": 33}}, {"__type__": "e5e2cLjgSlIWJjl5gIYRPed", "node": {"__id__": 32}}, {"__type__": "611bdQHU3xE3Jq1bah1KnhH", "node": {"__id__": 32}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "d0FjmceNxAL7C20P4o6Whz"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [365.082, 135.266, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 33}, "_type": 0, "_allowSleep": false}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 33}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": -87.5}, "_size": {"__type__": "cc.Size", "width": 3, "height": 175}}, {"__type__": "87875GeJxtIPr6+l3TEHNoR", "node": {"__id__": 33}, "bRefreshPhysicCollider": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "beGu1TM7JAYoJXE2GI1ac+"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 3, "height": 175}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, -0.3420201433256687, 0.9396926207859084, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -40}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b21", "_parent": {"__id__": 32}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "47gEt1rr5LaZdHVFOdit2e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "e50KN+ajNPxYTJX6G3TBbf"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 62}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 2}, "_children": [{"__id__": 36}, {"__id__": 37}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 35}, "_type": 0, "_gravityScale": -1.5}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 35}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "cc.RopeJoint", "node": {"__id__": 35}, "anchor": {"__type__": "cc.Vec2", "x": 0.5}, "connectedBody": {"__id__": 28}, "_maxLength": 176}, {"__type__": "59b16aRe/NFSL9WFTVWpxEy", "node": {"__id__": 35}, "ropeNode": {"__id__": 36}}, {"__type__": "e5e2cLjgSlIWJjl5gIYRPed", "node": {"__id__": 35}}, {"__type__": "611bdQHU3xE3Jq1bah1KnhH", "node": {"__id__": 35}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "ccfRkVKsNCroDtMfv8c9SW"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [428.898, 8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 36}, "_type": 0, "_allowSleep": false}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 36}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": -88}, "_size": {"__type__": "cc.Size", "width": 3, "height": 176}}, {"__type__": "87875GeJxtIPr6+l3TEHNoR", "node": {"__id__": 36}, "bRefreshPhysicCollider": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "cafo94KitHnbnVgrGzq1I3"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 3, "height": 176}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, -0.6915130557822694, 0.7223639620597556, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -87.5}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b21", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "47gEt1rr5LaZdHVFOdit2e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "34pZb1pnpI3Y4XvpTS1D/X"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 62}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 2}, "_children": [{"__id__": 39}, {"__id__": 40}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 38}, "_type": 0, "_gravityScale": -1.5}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 38}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "cc.RopeJoint", "node": {"__id__": 38}, "anchor": {"__type__": "cc.Vec2", "x": 0.5}, "connectedBody": {"__id__": 28}, "_maxLength": 220}, {"__type__": "59b16aRe/NFSL9WFTVWpxEy", "node": {"__id__": 38}, "ropeNode": {"__id__": 39}}, {"__type__": "e5e2cLjgSlIWJjl5gIYRPed", "node": {"__id__": 38}}, {"__type__": "611bdQHU3xE3Jq1bah1KnhH", "node": {"__id__": 38}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "c6NqJOJapH+6B3C3jnaTu1"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [385.898, -174, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 38}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 39}, "_type": 0, "_allowSleep": false}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 39}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": -110}, "_size": {"__type__": "cc.Size", "width": 3, "height": 220}}, {"__type__": "87875GeJxtIPr6+l3TEHNoR", "node": {"__id__": 39}, "bRefreshPhysicCollider": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "caAipsdj1NJa+29vwtFZSY"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 3, "height": 220}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, -0.9469301294951056, 0.3214394653031617, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -142.5}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b21", "_parent": {"__id__": 38}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "47gEt1rr5LaZdHVFOdit2e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "11sAlC6dpN0qvqZ9wCJO0g"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 62}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 2}, "_children": [{"__id__": 42}, {"__id__": 43}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 41}, "_type": 0, "_gravityScale": -1.5}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 41}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "cc.RopeJoint", "node": {"__id__": 41}, "anchor": {"__type__": "cc.Vec2", "x": 0.5}, "connectedBody": {"__id__": 28}, "_maxLength": 197}, {"__type__": "59b16aRe/NFSL9WFTVWpxEy", "node": {"__id__": 41}, "ropeNode": {"__id__": 42}}, {"__type__": "e5e2cLjgSlIWJjl5gIYRPed", "node": {"__id__": 41}}, {"__type__": "611bdQHU3xE3Jq1bah1KnhH", "node": {"__id__": 41}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "70HmA0oMFH2Zc2am9myZM6"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [251.898, -197, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 42}, "_type": 0, "_allowSleep": false}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 42}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": -98.5}, "_size": {"__type__": "cc.Size", "width": 3, "height": 197}}, {"__type__": "87875GeJxtIPr6+l3TEHNoR", "node": {"__id__": 42}, "bRefreshPhysicCollider": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "5edejPlrVHrK87pHB02NxA"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 3, "height": 197}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b21", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "47gEt1rr5LaZdHVFOdit2e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "3dnsq6OXBB7o24KQ+vMl9g"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 62}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 2}, "_children": [{"__id__": 45}, {"__id__": 46}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 44}, "_type": 0, "_gravityScale": -1.5}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 44}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "cc.RopeJoint", "node": {"__id__": 44}, "anchor": {"__type__": "cc.Vec2", "x": 0.5}, "connectedBody": {"__id__": 28}, "_maxLength": 212}, {"__type__": "59b16aRe/NFSL9WFTVWpxEy", "node": {"__id__": 44}, "ropeNode": {"__id__": 45}}, {"__type__": "e5e2cLjgSlIWJjl5gIYRPed", "node": {"__id__": 44}}, {"__type__": "611bdQHU3xE3Jq1bah1KnhH", "node": {"__id__": 44}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "792Ev0tW5NCK64xvBP+hqj"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120.898, -167, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 45}, "_type": 0, "_allowSleep": false}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 45}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": -106}, "_size": {"__type__": "cc.Size", "width": 3, "height": 212}}, {"__type__": "87875GeJxtIPr6+l3TEHNoR", "node": {"__id__": 45}, "balloonNode": {"__id__": 44}, "bRefreshPhysicCollider": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "29TPlk2YpIFpDI8/0O2b/5"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 3, "height": 212}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.9455185755993167, 0.32556815445715676, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 142}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b21", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "47gEt1rr5LaZdHVFOdit2e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "448vEme8RNDK2AnPHXtMoF"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 62}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 2}, "_children": [{"__id__": 48}, {"__id__": 49}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 47}, "_type": 0, "_gravityScale": -1.5}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 47}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "cc.RopeJoint", "node": {"__id__": 47}, "anchor": {"__type__": "cc.Vec2", "x": 0.5}, "connectedBody": {"__id__": 28}, "_maxLength": 171}, {"__type__": "59b16aRe/NFSL9WFTVWpxEy", "node": {"__id__": 47}, "ropeNode": {"__id__": 48}}, {"__type__": "e5e2cLjgSlIWJjl5gIYRPed", "node": {"__id__": 47}}, {"__type__": "611bdQHU3xE3Jq1bah1KnhH", "node": {"__id__": 47}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "35AJq8ptpDB5VokJjLLSDe"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [80.898, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 48}, "_type": 0, "_allowSleep": false}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 48}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": -85.5}, "_size": {"__type__": "cc.Size", "width": 3, "height": 171}}, {"__type__": "87875GeJxtIPr6+l3TEHNoR", "node": {"__id__": 48}, "bRefreshPhysicCollider": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "4e1QnQd+RJQr5r5dFtXHj2"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 3, "height": 171}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.7223639620597555, 0.6915130557822694, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 92.5}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b21", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "47gEt1rr5LaZdHVFOdit2e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "5ceKWASGFE4b9LLGBYy120"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 62}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 2}, "_children": [{"__id__": 51}, {"__id__": 52}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 50}, "_type": 0, "_gravityScale": -1.5}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 50}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 50}}, {"__type__": "cc.RopeJoint", "node": {"__id__": 50}, "anchor": {"__type__": "cc.Vec2", "x": 0.5}, "connectedBody": {"__id__": 28}, "_maxLength": 244}, {"__type__": "59b16aRe/NFSL9WFTVWpxEy", "node": {"__id__": 50}, "ropeNode": {"__id__": 51}}, {"__type__": "e5e2cLjgSlIWJjl5gIYRPed", "node": {"__id__": 50}}, {"__type__": "611bdQHU3xE3Jq1bah1KnhH", "node": {"__id__": 50}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "7bsMF0+htP4rANGDbDw48P"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [114.898, 201, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "77uXDRsMxCiqZmCVbpYOiE"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 51}, "_type": 0, "_allowSleep": false}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 51}, "_friction": 1, "_offset": {"__type__": "cc.Vec2", "y": -122}, "_size": {"__type__": "cc.Size", "width": 3, "height": 244}}, {"__type__": "87875GeJxtIPr6+l3TEHNoR", "node": {"__id__": 51}, "bRefreshPhysicCollider": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "9czRihGYdN2ZtvV3ubFlU5"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 3, "height": 244}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.29654157497557093, 0.9550199444571866, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 34.5}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b21", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "47gEt1rr5LaZdHVFOdit2e"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "14t1BboLpPVI9P2c73nzr/"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 62}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "doorFalse", "_parent": {"__id__": 2}, "_children": [{"__id__": 54}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "3foysQSKZIzI6FBQpjYdJM"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-435, -172, 0, 0, 0, 0, 1, -1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "door01", "_parent": {"__id__": 53}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0aZLcVNaNAOKeB2gjoh/N5"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "d5FZ1dcJ9DX46JWArU3k7y"}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 56}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 55}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 55}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 55}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "97LtkH/NBIIpEYADBdIz0T"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-392, -170, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "50dXijZXhKD5afqE18GnK4"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "荡（不了）秋千", "_N$string": "荡（不了）秋千", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "00+ChU73tGJJfTV9upfcF0"}, "fileId": "b44OYsbIlJKJO4KhyFS9GI"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-185, 50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}]