[{"__type__": "cc.SpriteFrame", "content": {"name": "a02", "texture": "1biK03j/1Lj697KsG4saVN", "rect": [0, 0, 64, 32], "offset": [0, 0], "originalSize": [64, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a18", "texture": "ee3Ys8+7tECJeKs2HGJo19", "rect": [0, 0, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a03", "texture": "ddDXTJFxtD6KnK8oFGrwzd", "rect": [0, 0, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a16", "texture": "1etoEHcLBC3KAAYyyjwp4p", "rect": [0, 0, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a15", "texture": "653oEZxndCOLX4th/tk4au", "rect": [0, 0, 64, 32], "offset": [0, 0], "originalSize": [64, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a05", "texture": "53x2gS1yFMKb0cpxhT9Hqo", "rect": [0, 0, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}}]