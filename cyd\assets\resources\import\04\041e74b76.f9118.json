[{"__type__": "cc.SpriteFrame", "content": {"name": "g06", "texture": "18f882691", "rect": [904, 314, 179, 108], "offset": [0, 0], "originalSize": [179, 108], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "z43", "texture": "143d15e68", "rect": [706, 856, 195, 96], "offset": [0, 0], "originalSize": [195, 96], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "z46", "texture": "124db27dc", "rect": [3, 315, 178, 95], "offset": [0, 0], "originalSize": [178, 95], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vStageEnterButton", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vStageEnterButton", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "f26qyoIYZPJrJCCFCagVBK"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-389.5, 104, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonSkating", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 2}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "e4zKKGIUxAjKLbCsphkxtQ"}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeSkating", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "86cMxHFZVCuqnqtdtP/n+F"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "5dRcrhqA5De6r3LnJTujAJ"}, "_contentSize": {"__type__": "cc.Size", "width": 183, "height": 103}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeSlideBlock", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d5/rB/2fJLSqIWHlkvKCF8"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "10weQNW/JPtYKlJv7Bw47P"}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePortal", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1aLo83wR1Ljbm+tgUzUYWQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "dc3PiJnvpMV7g+T8BqNO7G"}, "_contentSize": {"__type__": "cc.Size", "width": 179, "height": 108}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node<PERSON>ewyear", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "40CMDBeLFByY78e7k0oPUJ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "59/GCw42NOWZwDrNG2UKtw"}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 111}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGame186", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b8cNpW9dRCCJrDv0Pu6KFr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "8e2903F35ABYXHVfYpayXX"}, "_contentSize": {"__type__": "cc.Size", "width": 175, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeFeralCat", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "41pAff5KVOLKwQ8bpouUIq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "d7JpEGbsRH+4Os3A4G6WOT"}, "_contentSize": {"__type__": "cc.Size", "width": 189, "height": 95}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePointLine", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "7alYnoMj1HRKFwR4sVgUkY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "19UF2I5ndNqqz06y5tI5Vs"}, "_contentSize": {"__type__": "cc.Size", "width": 174, "height": 112}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDFinder", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "29VSOqa4ZC94T0rlLb2lmw"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "6daB/XbjhD1rFbBBnLlr3x"}, "_contentSize": {"__type__": "cc.Size", "width": 181, "height": 102}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGame198", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c44KnVpVNL47cWgakhuBwG"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "7dybLUG8hJ4bno0HSVrGaY"}, "_contentSize": {"__type__": "cc.Size", "width": 198, "height": 105}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_node<PERSON><PERSON>ner", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00JzpevldHTL5aR8UCEiXL"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "e8/Oo6VH9Leob/xAfl2cO+"}, "_contentSize": {"__type__": "cc.Size", "width": 186, "height": 106}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeWordGame", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "24ZvjOXChDNoXxa7cgW4DS"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "9cKvnuFvVGx5grkf+Op43a"}, "_contentSize": {"__type__": "cc.Size", "width": 185, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePuzzle", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "feqOy5QZJMg7iAu6tjQFFr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "caj9f7rZhLc7v4/rRRlpdi"}, "_contentSize": {"__type__": "cc.Size", "width": 171, "height": 105}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodePFinder", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d3atS7N7tORYelJWlxuETa"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "f5b7PndDNAoJHe0gQVeDTi"}, "_contentSize": {"__type__": "cc.Size", "width": 172, "height": 99}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBlackWhite", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "39CrLYxGpDpJQUNMp/Ebn/"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "fbgI5kbYNF0rzU9WvoElR4"}, "_contentSize": {"__type__": "cc.Size", "width": 195, "height": 96}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeWanShengJie", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "95Q3VM2mpNhp+drVoXyVg6"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "84mNN3hL9PYoBv/mn3KAvc"}, "_contentSize": {"__type__": "cc.Size", "width": 202, "height": 97}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeGridMove", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "4da2oz3nlPf74iobWZcsPY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "26b+KcT2xHvpE+KyVYVNOz"}, "_contentSize": {"__type__": "cc.Size", "width": 178, "height": 95}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeMark", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98Ak75An9NV71dwdhkZ5tg"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "81fWgQmidNMLs0HMokX56S"}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 43}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-60.507, 51.473, 0, 0, 0, 0.10745277637292554, 0.9942101894718994, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 12.337000000000003}}, {"__type__": "cc.Node", "_name": "CC_nodeMark1", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c8iJ7A/9BOFbxejW4DdAzi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "69A2FjD7hJlLmEd6eAUPG0"}, "fileId": "40K0FAvJpFaJhM47JgbQJI"}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 43}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-60.507, 51.473, 0, 0, 0, 0.10745277637292554, 0.9942101894718994, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 12.337000000000003}}], {"__type__": "cc.SpriteFrame", "content": {"name": "g08", "texture": "124db27dc", "rect": [304, 88, 183, 103], "offset": [0, 0], "originalSize": [183, 103], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "z45", "texture": "18f882691", "rect": [314, 924, 202, 97], "offset": [0, 0], "originalSize": [202, 97], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "f12", "texture": "124db27dc", "rect": [180, 284, 175, 101], "offset": [0, 0], "originalSize": [175, 101], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "g07", "texture": "124db27dc", "rect": [287, 303, 176, 94], "offset": [0, 0], "originalSize": [176, 94], "capInsets": [0, 0, 0, 0]}}]