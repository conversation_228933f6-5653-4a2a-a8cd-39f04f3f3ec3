[{"__type__": "cc.SpriteFrame", "content": {"name": "c103", "texture": "eexvdwEFdPnbZJ9tSS2uXu", "rect": [0, 0, 18, 32], "offset": [0, 0], "originalSize": [18, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c102", "texture": "4bUiUmVv5AprMlS9cWk/BO", "rect": [0, 0, 96, 108], "offset": [0, 0], "originalSize": [96, 108], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c106", "texture": "6bj5V+U7RP06rvGOvPWoG/", "rect": [0, 0, 12, 14], "offset": [0, 0], "originalSize": [12, 14], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c104", "texture": "caSTlwHJ5G4JSo/xNes7Zx", "rect": [0, 0, 96, 108], "offset": [0, 0], "originalSize": [96, 108], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "c105", "texture": "9b30cg8vVJG4L13WiyBnvt", "rect": [0, 0, 12, 14], "offset": [0, 0], "originalSize": [12, 14], "capInsets": [0, 0, 0, 0]}}]