[{"__type__": "cc.SpriteFrame", "content": {"name": "art07", "texture": "1fsYuGH2NEKJb/I4eA59VN", "rect": [0, 0, 176, 84], "offset": [0, 0], "originalSize": [176, 84], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a68", "texture": "9760jqL9tAmJkAyzKWHSoG", "rect": [0, 0, 823, 59], "offset": [0, 0], "originalSize": [823, 59], "capInsets": [382, 0, 383, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "z39", "texture": "124db27dc", "rect": [104, 397, 77, 67], "offset": [0, 0], "originalSize": [77, 67], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vGame186Scene", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "vGameScene", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__type__": "cc.Node", "_name": "CC_nodeGame", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "09Bqmb9ihGC7juqLtsyzKl"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 6}, {"__id__": 10}, {"__type__": "cc.Node", "_name": "CC_LLT_nodeLeftTop", "_parent": {"__id__": 1}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "44MLtXeuFLtYXcTZtesz5t"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-512, 288, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 13}, {"__id__": 26}, {"__id__": 29}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "adaEjUgi9CqawZTWlnr47x"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 219.97045256124744, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeCamera", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Camera", "node": {"__id__": 2}, "_cullingMask": -290, "_depth": 2, "_nearClip": 0.1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "5dt8CnTWFOxKphnY7O+vMe"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 354.20439014783545, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "cameraUi", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Camera", "node": {"__id__": 3}, "_cullingMask": 32, "_clearFlags": 7, "_depth": 1, "_nearClip": 0.1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "48m1aiA4NDv7DG0LFjeqIf"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 354.20439014783545, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "cameraDefault", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Camera", "node": {"__id__": 4}, "_cullingMask": 1, "_depth": 3, "_nearClip": 0.1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "77GgeXAExN8Zr2OjTSrmLA"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 354.20439014783545, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBg", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "97RJtdVWVKeLT0Ggb3lMuE"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "59gDElX9FNabX9T6dAnu/C"}, "_contentSize": {"__type__": "cc.Size", "width": 1600, "height": 960}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 5, "groupIndex": 5}, {"__type__": "cc.Node", "_name": "guanka", "_parent": {"__id__": 1}, "_children": [{"__id__": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "c88K2nGSZBW5UOJFT9q6he"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-421.212, 303.213, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLv", "_parent": {"__id__": 6}, "_children": [{"__id__": 8}, {"__id__": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "5aWpttG+tDN7j9sZQcfpp8"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "art06", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "30wQMzA3FPzamVq/nOHf8D"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "55whHTPqRG77FBRAobzY2i"}, "_contentSize": {"__type__": "cc.Size", "width": 146, "height": 240}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 21.873, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelLv", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "第xxx关", "_N$string": "第xxx关", "_fontSize": 28, "_lineHeight": 28, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "5b5ENL6NRCk70Z/e+rXVX8"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 35.28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -47.838, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}}, {"__type__": "cc.Node", "_name": "kaishi", "_parent": {"__id__": 1}, "_children": [{"__id__": 11}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "c7lLsBS4RBHZOiqUqXShN6"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 257, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTitle", "_parent": {"__id__": 10}, "_children": [{"__id__": 12}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "22CujnNz5Apr5HK4gBBLo1"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "2fTAjC/PxJeo7xKAJ+k07G"}, "_contentSize": {"__type__": "cc.Size", "width": 823, "height": 59}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -2.01, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelTitle", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "进门就过关啦！", "_N$string": "进门就过关啦！", "_fontSize": 30, "_lineHeight": 30, "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$overflow": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "e1POMvUlFFlJfP5kZQfcMh"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 766, "height": 48}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_LR_nodeRightCenter", "_parent": {"__id__": 1}, "_children": [{"__id__": 14}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "c3QyCRHdZJKLlPjgnOwUIp"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [512, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 13}, "_children": [{"__id__": 15}, {"__id__": 17}, {"__id__": 22}, {"__id__": 24}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 14}, "_layoutSize": {"__type__": "cc.Size", "width": 80, "height": 241.2}, "_resize": 1, "_N$layoutType": 2, "_N$spacingY": 10}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "ccMldfjVxK8JIugfBLsKsi"}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 241.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-55, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnStop", "_parent": {"__id__": 14}, "_children": [{"__id__": 16}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "4fTbhj21VFZoeT6xWh9QEi"}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 52.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 94.19999999999999, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonStop", "_parent": {"__id__": 15}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "abW1hjPyZF2rWBqEVtTr4/"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "69fZ/tLYJLY6gojGGjRV/N"}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 52.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnTip", "_parent": {"__id__": 14}, "_children": [{"__id__": 18}, {"__id__": 19}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "f0J90LeExL5q5Q9X7nk+mx"}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 52.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31.399999999999984, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonTip", "_parent": {"__id__": 17}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d9aCmc+PRGmLKmtQb5azMR"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "95CcIy1fRJeqt1CoYrPAPN"}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 52.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeAdIconTip", "_parent": {"__id__": 17}, "_children": [{"__id__": 20}, {"__id__": 21}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "8b9YY0zkVHqqx9F4vjTCw6"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-19.675, 22.224, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "ad", "_parent": {"__id__": 19}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c2EBVSVIJIV4l2PxZy2VNP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "65ritanmhNL6949LzG6m4A"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 38}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}}, {"__type__": "cc.Node", "_name": "light", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "8cGqXexQVKc6b70I4pwvsB"}, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 87}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3, -4, 0, 0, 0, -0.17364817766693033, 0.984807753012208, 0.4, 0.4, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -20}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnRestart", "_parent": {"__id__": 14}, "_children": [{"__id__": 23}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "228anXEbFAjZPwXCsNAUjr"}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 52.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -31.400000000000013, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonRestart", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ebWm99k/VEFqleynufScfS"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "43VguaWqtGW6kqg8LEzt+B"}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 52.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeBtnShop", "_parent": {"__id__": 14}, "_children": [{"__id__": 25}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "daOYyxyKBKqrANQbygF8IN"}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 52.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -94.20000000000002, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonShop", "_parent": {"__id__": 24}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2fGzx3pR9DIb7zqHMTAknx"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "e6dDdTZ/1G0bM1ab02X/ir"}, "_contentSize": {"__type__": "cc.Size", "width": 60.8, "height": 52.8}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "anniu", "_parent": {"__id__": 1}, "_children": [{"__id__": 27}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "e3D64vEKdLPIVVIWyGfYvH"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -229.462, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonStart", "_parent": {"__id__": 26}, "_children": [{"__id__": 28}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "ccWO7uXLJG1KtUzOEuWPyh"}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "art01", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1fYun/JmlJpLaNqpATMF1U"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "5agDTfv6xGdIpcvt+QehiG"}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 84}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTouchMask", "_parent": {"__id__": 1}, "_active": false, "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 29}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cfRYZuEPpHZbTA8+RbqVjG"}, "fileId": "bbWWDz5W9KZ53SxopDKvNP"}, "_contentSize": {"__type__": "cc.Size", "width": 1600, "height": 960}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}]]