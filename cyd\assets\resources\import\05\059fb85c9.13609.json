[{"__type__": "cc.Material", "_name": "sprite_hsl_color", "_effectAsset": {"__uuid__": "bdvtRV1PZMybtiJbEjeY9W"}, "_techniqueData": {"0": {"defines": {"USE_TEXTURE": true}, "props": {"dH": 0, "dS": 0, "dL": 0}}}}, {"__type__": "cc.EffectAsset", "_name": "sprite_hsl_color", "techniques": [{"passes": [{"blendState": {"targets": [{"blend": true}]}, "rasterizerState": {"cullMode": 0}, "properties": {"texture": {"value": "white", "type": 29}, "alphaThreshold": {"value": [0.5], "type": 13}, "dH": {"value": [0], "editor": {"tooltip": "色相"}, "type": 13}, "dS": {"value": [0], "editor": {"tooltip": "饱和度"}, "type": 13}, "dL": {"value": [0], "editor": {"tooltip": "亮度"}, "type": 13}}, "program": "sprite_hsl_color|vs|fs"}]}], "shaders": [{"hash": 1298596391, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_color;\n#if USE_TEXTURE\nin vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nuniform glow {\n  float dH;\n  float dS;\n  float dL;\n};\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  float r = o.r;\n  float g = o.g;\n  float b = o.b;\n  float a = o.a;\n  float h;\n  float s;\n  float l;\n  {\n    float max = r;\n    if ( g > max ) max = g;\n    if ( b > max ) max = b;\n    float min = r;\n    if ( g < min ) min = g;\n    if ( b < min ) min = b;\n    if ( max == min ) {\n      h = 0.0;\n    } else if ( max == r && g >= b ) {\n      h = 60.0 * (g - b) / (max - min) + 0.0;\n    } else if ( max == r && g < b ) {\n      h = 60.0 * (g - b) / (max - min) + 360.0;\n    } else if ( max == g ) {\n      h = 60.0 * (b - r) / (max - min) + 120.0;\n    } else if ( max == b ) {\n      h = 60.0 * (r - g) / (max - min) + 240.0;\n    }\n    l = 0.5 * (max + min);\n    if(l == 0.0 || max == min) {\n      s = 0.0;\n    } else if(0.0 <= l && l <= 0.5) {\n      s = (max - min) / (2.0 * l);\n    } else if(l > 0.5) {\n      s = (max - min) / (2.0 - 2.0 * l);\n    }\n  }\n  h = h + dH;\n  s = s + dS;\n  if ( s < 0.0 ){\n    s = 0.0;\n  }\n  if ( s > 1.0 ) {\n    s = 1.0;\n  }\n  l = l + dL;\n  vec4 finalColor;\n  {\n    float q;\n    if (l < 0.5) {\n      q = l * (1.0 + s);\n    } else if (l >= 0.5) {\n      q = l + s - l * s;\n    }\n    float p = 2.0 * l - q;\n    float hk = h / 360.0;\n    float t[3];\n    t[0] = hk + 1.0 / 3.0;\n    t[1] = hk;\n    t[2] = hk - 1.0 / 3.0;\n    for (int i = 0; i < 3; i++) {\n      if (t[i] < 0.0) t[i] += 1.0;\n      if (t[i] > 1.0) t[i] -= 1.0;\n    }\n    float c[3];\n    for (int i = 0; i < 3; i++) {\n      if( t[i] < 1.0 / 6.0) {\n        c[i] = p + ((q - p) * 6.0 * t[i]);\n      } else if (1.0 / 6.0 <= t[i] && t[i] <0.5) {\n        c[i] = q;\n      } else if (0.5 <= t[i] && t[i] < 2.0 / 3.0) {\n        c[i] = p + ((q - p) * 6.0 * (2.0 / 3.0 - t[i]));\n      } else {\n        c[i] = p;\n      }\n    }\n    finalColor = vec4(c[0], c[1], c[2], a);\n  }\n  finalColor.r += dL;\n  finalColor.g += dL;\n  finalColor.b += dL;\n  gl_FragColor = finalColor;\n}"}, "glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_color;\n#if USE_TEXTURE\nvarying vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nuniform float dH;\nuniform float dS;\nuniform float dL;\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture2D(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  float r = o.r;\n  float g = o.g;\n  float b = o.b;\n  float a = o.a;\n  float h;\n  float s;\n  float l;\n  {\n    float max = r;\n    if ( g > max ) max = g;\n    if ( b > max ) max = b;\n    float min = r;\n    if ( g < min ) min = g;\n    if ( b < min ) min = b;\n    if ( max == min ) {\n      h = 0.0;\n    } else if ( max == r && g >= b ) {\n      h = 60.0 * (g - b) / (max - min) + 0.0;\n    } else if ( max == r && g < b ) {\n      h = 60.0 * (g - b) / (max - min) + 360.0;\n    } else if ( max == g ) {\n      h = 60.0 * (b - r) / (max - min) + 120.0;\n    } else if ( max == b ) {\n      h = 60.0 * (r - g) / (max - min) + 240.0;\n    }\n    l = 0.5 * (max + min);\n    if(l == 0.0 || max == min) {\n      s = 0.0;\n    } else if(0.0 <= l && l <= 0.5) {\n      s = (max - min) / (2.0 * l);\n    } else if(l > 0.5) {\n      s = (max - min) / (2.0 - 2.0 * l);\n    }\n  }\n  h = h + dH;\n  s = s + dS;\n  if ( s < 0.0 ){\n    s = 0.0;\n  }\n  if ( s > 1.0 ) {\n    s = 1.0;\n  }\n  l = l + dL;\n  vec4 finalColor;\n  {\n    float q;\n    if (l < 0.5) {\n      q = l * (1.0 + s);\n    } else if (l >= 0.5) {\n      q = l + s - l * s;\n    }\n    float p = 2.0 * l - q;\n    float hk = h / 360.0;\n    float t[3];\n    t[0] = hk + 1.0 / 3.0;\n    t[1] = hk;\n    t[2] = hk - 1.0 / 3.0;\n    for (int i = 0; i < 3; i++) {\n      if (t[i] < 0.0) t[i] += 1.0;\n      if (t[i] > 1.0) t[i] -= 1.0;\n    }\n    float c[3];\n    for (int i = 0; i < 3; i++) {\n      if( t[i] < 1.0 / 6.0) {\n        c[i] = p + ((q - p) * 6.0 * t[i]);\n      } else if (1.0 / 6.0 <= t[i] && t[i] <0.5) {\n        c[i] = q;\n      } else if (0.5 <= t[i] && t[i] < 2.0 / 3.0) {\n        c[i] = p + ((q - p) * 6.0 * (2.0 / 3.0 - t[i]));\n      } else {\n        c[i] = p;\n      }\n    }\n    finalColor = vec4(c[0], c[1], c[2], a);\n  }\n  finalColor.r += dL;\n  finalColor.g += dL;\n  finalColor.b += dL;\n  gl_FragColor = finalColor;\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}], "samplers": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplers": []}}, "defines": [{"name": "USE_TEXTURE", "type": "boolean", "defines": []}, {"name": "CC_USE_MODEL", "type": "boolean", "defines": []}, {"name": "USE_ALPHA_TEST", "type": "boolean", "defines": []}, {"name": "CC_USE_ALPHA_ATLAS_texture", "type": "boolean", "defines": ["USE_TEXTURE"]}, {"name": "INPUT_IS_GAMMA", "type": "boolean", "defines": ["USE_TEXTURE"]}], "blocks": [{"name": "ALPHA_TEST", "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"], "binding": 0}, {"name": "glow", "members": [{"name": "dH", "type": 13, "count": 1}, {"name": "dS", "type": 13, "count": 1}, {"name": "dL", "type": 13, "count": 1}], "defines": [], "binding": 1}], "samplers": [{"name": "texture", "type": 29, "count": 1, "defines": ["USE_TEXTURE"], "binding": 30}], "record": null, "name": "sprite_hsl_color|vs|fs"}]}]