[{"__type__": "cc.SpriteFrame", "content": {"name": "a07", "texture": "115a8446a", "rect": [3, 730, 154, 78], "offset": [0, 0], "originalSize": [154, 78], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a04", "texture": "116ee99c8", "rect": [3, 3, 573, 400], "offset": [0, 0], "originalSize": [573, 400], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a06", "texture": "115a8446a", "rect": [3, 3, 454, 142], "offset": [0, 0], "originalSize": [454, 142], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a08", "texture": "116ee99c8", "rect": [409, 515, 34, 158], "offset": [0, 0], "originalSize": [34, 158], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a02", "texture": "116ee99c8", "rect": [582, 3, 573, 400], "offset": [0, 0], "originalSize": [573, 400], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a05", "texture": "116ee99c8", "rect": [3, 409, 573, 400], "offset": [0, 0], "originalSize": [573, 400], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "mapReaction001", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "mapReaction001", "_children": [{"__id__": 2}, {"__id__": 34}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__type__": "cc.Node", "_name": "CC_nodeTouchArea", "_parent": {"__id__": 2}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "71wDD9zoBGPZ6FsdWTTs1B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__id__": 3}, {"__id__": 18}, {"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 33}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "nodeStickParent", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "8c1kZMsSBLsLTozDsn+dcl"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick1", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 4}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "2bbtqBmmxPlp13x3e1XF+L"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e7P4lqaTNAa4ZAI6IqrH0U"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 4}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 4}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "64T+S9lCFAmJ3FQ9QZRsI4"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-422.5, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick2", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 5}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "9fp34Gt0xJkrolvQKcrslB"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8aVoM0HgRNRIfoNdueo1kN"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 5}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 5}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "eeZTHOhiJAc7Xfhb52IWEX"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-357.5, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick3", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 6}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "62ME128SxMh4Vngf5YGsn+"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e7P4lqaTNAa4ZAI6IqrH0U"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 6}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 6}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "71+o5ZZSpBUb2CZraYMg29"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-292.5, -22, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick4", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 7}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "ceMJjnPaRJA4Mwn3wC4umx"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8aVoM0HgRNRIfoNdueo1kN"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 7}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 7}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "42HNTzuxRK6YcBpu2dmaeH"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-227.5, -6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick5", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 8}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "d9sc0bFuZMnoznrZm68P4W"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e7P4lqaTNAa4ZAI6IqrH0U"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 8}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 8}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "d7wxBMkGZItr3AIjeymVyc"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-162.5, 6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick6", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 9}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "63dCtG9uNNT5Ue89UW6fMS"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8aVoM0HgRNRIfoNdueo1kN"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 9}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 9}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "b9INjBHlNCXKKAJWPFfLbr"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-97.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick7", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 10}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "25ZidpKMlMTqrvZYLA8qSd"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e7P4lqaTNAa4ZAI6IqrH0U"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 10}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 10}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "23oaZ+ULhAIaAmPp/dIJmt"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32.5, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick8", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 11}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "44lb4Geu1LVrszSioA6F/U"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8aVoM0HgRNRIfoNdueo1kN"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 11}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 11}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "1b/cDgODpOcY0DKhBdDUVB"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32.5, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick9", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 12}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "2aRwYsP/BMpJxgxGHoDTyh"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e7P4lqaTNAa4ZAI6IqrH0U"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 12}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 12}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "14dY99gvpOobKfvwVkw6Zn"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [97.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick10", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 13}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "080BwiKJVOrreEQHfKPK/U"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8aVoM0HgRNRIfoNdueo1kN"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 13}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 13}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "34pdnwm+dBZYzruCs5/CIL"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [162.5, 6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick11", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 14}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "a2pGn5R0ZP1bcVqE0Glv8S"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e7P4lqaTNAa4ZAI6IqrH0U"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 14}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 14}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "8f+LS/7IRBwL1DlStCw2/y"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [227.5, -6, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick12", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 15}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "b6jQ6+K1tAWqYeBA0U6tGW"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8aVoM0HgRNRIfoNdueo1kN"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 15}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 15}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "12llZXDPZJ1Z4WwITrAQZP"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [292.5, -22, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick13", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 16}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "eeB2kZ/aBOSI4CIugYLjB6"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e7P4lqaTNAa4ZAI6IqrH0U"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 16}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 16}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "3bzOHGuyNHNoBe8ZrV00ga"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [357.5, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStick14", "_parent": {"__id__": 3}, "_children": [{"__type__": "cc.Node", "_name": "area", "_parent": {"__id__": 17}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "1bm5pmtMhMibT3Jrvuvj05"}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 158}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8aVoM0HgRNRIfoNdueo1kN"}, "_sizeMode": 0}, {"__type__": "cc.RigidBody", "node": {"__id__": 17}, "_type": 0, "_fixedRotation": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 17}, "_offset": {"__type__": "cc.Vec2", "y": -79}, "_size": {"__type__": "cc.Size", "width": 34, "height": 158}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "7f0GyfxFxFs4MfYlAOIVrp"}, "_contentSize": {"__type__": "cc.Size", "width": 34, "height": 158}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [422.5, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trigger", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 18}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 18}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 1280, "height": 10}}, {"__type__": "1e346wO/bBLBpsb7nJM+T4N", "node": {"__id__": 18}, "msgId": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "fcR/Fjx3BNNpwGzBn3gdCd"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 10}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -810, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "a06", "_parent": {"__id__": 2}, "_children": [{"__id__": 20}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "87USwbe+RF5bxltwCQkxf7"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "03lC7ui1VEI6vIbUb1QM1t"}, "_contentSize": {"__type__": "cc.Size", "width": 454, "height": 142}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a06", "_parent": {"__id__": 19}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "87USwbe+RF5bxltwCQkxf7"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "06M+eO5dVKqadLa4yYLW2e"}, "_contentSize": {"__type__": "cc.Size", "width": 454, "height": 142}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a07", "_parent": {"__id__": 2}, "_children": [{"__id__": 22}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3eBmtVfPFBDJFlsjL7mDJF"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "9cRcCgaARCKrkowCh0eeIh"}, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 245, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_labelCount", "_parent": {"__id__": 21}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "00", "_N$string": "00", "_enableWrapText": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "8bPUlHIUBB3Lk1eRqIGOrb"}, "_color": {"__type__": "cc.Color", "r": 71, "g": 217, "b": 127}, "_contentSize": {"__type__": "cc.Size", "width": 44.49, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 24}, {"__id__": 25}, {"__id__": 27}, {"__id__": 29}, {"__id__": 31}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "c7Waxnwa1LOofinnnP8ZiU"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleNormal", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e43xJFS9VEiK2PP8OWUAIh"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "30uqmAUqhJRpBrCLoNFiAW"}, "_contentSize": {"__type__": "cc.Size", "width": 573, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -455, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleAction1Suc", "_parent": {"__id__": 23}, "_children": [{"__id__": 26}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "5e1IZpijlEy4Vp+dEFIRDk"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-394, -15, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a04", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dtcDsHdpNu5+ipGIz3TMX"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "b0/FBgo/JHSYtKGmW+i11x"}, "_contentSize": {"__type__": "cc.Size", "width": 573, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-6, -370, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleAction1Fail", "_parent": {"__id__": 23}, "_children": [{"__id__": 28}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "002/yqKXFK95SGComfMdx/"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-219.823, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a04", "_parent": {"__id__": 27}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "bdJj3NIC9EBqJ8a59RNn8l"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "f7JpsTDdRLzKIVcefLOZIm"}, "_contentSize": {"__type__": "cc.Size", "width": 573, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [26, -345, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleAction2Suc", "_parent": {"__id__": 23}, "_children": [{"__id__": 30}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "55HgAhOt1KlK2KbhT9YUyy"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [51.201, -45, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a02", "_parent": {"__id__": 29}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "96b8EKMxhJ/IL/UZbP3jfT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "b5ei36nxRKJ7dZ/NLhFHQ1"}, "_contentSize": {"__type__": "cc.Size", "width": 573, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [186, -340, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleAction2Fail", "_parent": {"__id__": 23}, "_children": [{"__id__": 32}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "74+nrwU6BIv7b8aQjdaUYm"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [229.225, -65, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a02", "_parent": {"__id__": 31}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eeeZ9GymRLhIwe1KuWslW4"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "8cmK4nc7pOx6tAogLdCp1T"}, "_contentSize": {"__type__": "cc.Size", "width": 573, "height": 400}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [240, -320, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_labelTime", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "3", "_N$string": "3", "_fontSize": 32, "_lineHeight": 32, "_N$file": {"__uuid__": "9bN8TRoeVAi4LPWjeLFH1Z"}, "_isSystemFontUsed": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "00VEyvNghHM5+Y8ePGnmit"}, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -190, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeDev", "_parent": {"__id__": 1}, "_children": [{"__id__": 35}, {"__id__": 37}, {"__id__": 39}, {"__id__": 41}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "70xq3Y6zdJaZEadN1jCgtH"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 150, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonShow", "_parent": {"__id__": 34}, "_children": [{"__id__": 36}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "c2uAeaVJVBVIxmoPDe9Wem"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "显/隐", "_N$string": "显/隐", "_fontSize": 18, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "dbb1wR4M9BHra012bmyzOI"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 41, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAdd10", "_parent": {"__id__": 34}, "_children": [{"__id__": 38}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "c1+aHGAYJG9onkgKpqTz/f"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 37}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "+10", "_N$string": "+10", "_fontSize": 18, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "e8aZBMIXFER6RqvdG9bzuu"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 30.53, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAdd100", "_parent": {"__id__": 34}, "_children": [{"__id__": 40}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "9bwv7IV5ZOvrPIu82MeONj"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -100, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 39}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "+100", "_N$string": "+100", "_fontSize": 18, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "a2dUTTQ5pHUImFdDACQN4A"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 40.54, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonAdd1000", "_parent": {"__id__": 34}, "_children": [{"__id__": 42}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "59oGSiG6lB2YN7+vPKbyHx"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -150, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 41}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "+1000", "_N$string": "+1000", "_fontSize": 18, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "beQg80qa5C+Yd05pXnY/dv"}, "fileId": "dbCNQudVtMMJ+xwh/ss9Y1"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 50.55, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "a01", "texture": "116ee99c8", "rect": [409, 582, 573, 400], "offset": [0, 0], "originalSize": [573, 400], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a09", "texture": "115a8446a", "rect": [463, 3, 34, 158], "offset": [0, 0], "originalSize": [34, 158], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a03", "texture": "115a8446a", "rect": [3, 151, 573, 400], "offset": [0, 0], "originalSize": [573, 400], "rotated": 1, "capInsets": [0, 0, 0, 0]}}]