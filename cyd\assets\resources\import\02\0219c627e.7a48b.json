[{"__type__": "cc.SpriteFrame", "content": {"name": "a439", "texture": "1d7eb79f9", "rect": [3, 3, 784, 415], "offset": [0, 0], "originalSize": [784, 415], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a441", "texture": "1d7eb79f9", "rect": [39, 640, 261, 243], "offset": [0, 0], "originalSize": [261, 243], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "map274", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map274", "_children": [{"__id__": 2}, {"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-3, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg1", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 2}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "74hbwN6xNKuIKKAEZ+R2af"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -768, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg2", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "4cBAt/7eNFIIsKvN/0gfpz"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLoopBg3", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48SFudYXZITpkdPl867dpa"}, "_type": 2, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "93jTTSImVGsbqcbcWawikI"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 768, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "LabelDesc", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "远在天边", "_N$string": "远在天边", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "83xjemIKhPdr67a2XPZjAr"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 122.339, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_labelHeight", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "高度\n10000/10000", "_N$string": "高度\n10000/10000", "_fontSize": 32, "_lineHeight": 36, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "e4C1A1aihIUKQNwXxRuSGd"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 186.86, "height": 81.36}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-400, 100, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 8}, {"__id__": 43}, {"__type__": "cc.Node", "_name": "CC_nodePropParent", "_parent": {"__id__": 7}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "33HoVQdspH7JhQ30Sr8FIC"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__id__": 57}, {"__id__": 60}, {"__id__": 63}, {"__id__": 80}, {"__id__": 83}, {"__id__": 84}, {"__id__": 85}, {"__id__": 87}, {"__id__": 98}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "groud", "_parent": {"__id__": 7}, "_children": [{"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}, {"__id__": 42}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 8}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 8}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 1280, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "5bIYnV8AJMuJSSfGOLURsy"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 9}, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "31wx7GSyRKs7WGnLPPuc7x"}, "_contentSize": {"__type__": "cc.Size", "width": 1152, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 10}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "f42A1860FCvqhZTFQXfCkT"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 8}, "_children": [{"__id__": 12}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}, {"__id__": 28}, {"__id__": 29}, {"__id__": 31}, {"__id__": 32}, {"__id__": 33}, {"__id__": 35}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 11}, "_layoutSize": {"__type__": "cc.Size", "width": 1152, "height": 64}, "_resize": 1, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "156zghgcJITbAAhiDmKd7z"}, "_contentSize": {"__type__": "cc.Size", "width": 1152, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 11}, "_children": [{"__id__": 13}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "4cMwOEM/1D6L0aD89xvWUQ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "afonGu41FE7pJftp0D0xCY"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "34iD5fhA9BC6vSkhjG5Tzy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "50E86gyNRI57jV+2eAUbxR"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "03EtxZ94pIU5bOYhvFlBaV"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 11}, "_children": [{"__id__": 18}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "fahbKty+lKTb+ASEdNR6uw"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 17}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "d9ZgTnVEJOEq+PIrIP5/CC"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "90UmEE6EhBq4Y/4mBeq6Ro"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "51P3tzcG1H8q4gIeqUeQwf"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 11}, "_children": [{"__id__": 22}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "2bVjL9o2lD6bNq4x7W87qX"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 21}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "b9zk/GzpVKX4Y1GnrXGi3x"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, 28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "55eMirB/NP5q0jUrPoXTsv"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "61m9f0AT9KvokiQ0T1dfnW"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "9dAbvt9YlNyZRrYNo+54dJ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 11}, "_children": [{"__id__": 27}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "30GkhBn9VOc4mLhKp+Pa3C"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "0djb13WmFO75ztIlbRTXbx"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.9759999999999991, 23.812, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "casMJovhRMqICWx2Z5qZNf"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 11}, "_children": [{"__id__": 30}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "32p+STeJBKQrgJAuzKt/EP"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little04", "_parent": {"__id__": 29}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78LL5TK2RJ/7QmPvmVHdJr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "68C8YEKuBHFbjPi2GZL59E"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 7.955, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "5d9DeRSPZD/rj6biS3yoU6"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "79lR2Jp4dDfqAbzbuoS85C"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 11}, "_children": [{"__id__": 34}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "6acQaOQfdM1phNmFLVcj29"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 33}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "63cb2qgt1I0Z5a8uh7QBKN"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 11}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "78Lgqr1epKpYvyhAL+7vo9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 36}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "4cY4lOWEhLj45pWMXKzt/5"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 37}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "f9kmoCr3JFj5rgBpiYw1tE"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 38}, "_alignFlags": 13, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "c1IYlFbPtA2ZJepaB82O2A"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 39}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "19ckgFnuNJwqV7XJZAzAjs"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}}, {"__type__": "cc.Widget", "node": {"__id__": 40}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "6fe38iVB9JeJ6WubqskZ3z"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 41}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "7bALbJfFpLLYHfz6hW2WXu"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -224, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}}, {"__type__": "cc.Widget", "node": {"__id__": 42}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "66SLZUE0VC3pYfLcUXiZEX"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePlatform", "_parent": {"__id__": 7}, "_children": [{"__id__": 44}, {"__id__": 46}, {"__id__": 53}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 43}, "_type": 0, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 43}, "_density": 0, "_offset": {"__type__": "cc.Vec2", "x": 207, "y": -1.5}, "_size": {"__type__": "cc.Size", "width": 412.7, "height": 11}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "9cmoGCQNhCkL58+vSLN3iO"}, "_contentSize": {"__type__": "cc.Size", "height": 13}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [700, -25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_parent": {"__id__": 43}, "_children": [{"__id__": 45}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "16GYr38glI+qm9j1zuxdce"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "h552", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d7yeCDepxKL6nIPfVw3Y+G"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "7fQdHnUIROjYOdfwdC7Qmm"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "<PERSON>an", "_parent": {"__id__": 43}, "_children": [{"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 52}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 46}, "_layoutSize": {"__type__": "cc.Size", "width": 384, "height": 13}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "1fjCCxrltOg5RXf1G5vTOS"}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 13}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [223, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f168", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "50NC8dq3JIhoilMrmWdozR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "c3iQ5vcE1GgKJ7Se0rra4E"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 13}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f168", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "50NC8dq3JIhoilMrmWdozR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "d0pZEhRI9BQbUl0nVM/ZgB"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 13}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f168", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "50NC8dq3JIhoilMrmWdozR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "fbs9RprNpHLZae9ngX9aoU"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 13}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f168", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "50NC8dq3JIhoilMrmWdozR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "b7oJqn4QJKcqOC/cwE0Ylw"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 13}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f168", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "50NC8dq3JIhoilMrmWdozR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "2ayxJUb/1M7LbaAKK5YdjN"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 13}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f168", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "50NC8dq3JIhoilMrmWdozR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "a4nDCQp55FgpYLEq7SR2lq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 13}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeUFO", "_parent": {"__id__": 43}, "_children": [{"__type__": "cc.Node", "_name": "root", "_parent": {"__id__": 53}, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "94mDQaLuJG9YcXUG6+qS2E"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__id__": 54}, {"__id__": 56}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "63YInYBDxIS522RMqa2ikp"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [413.668, -3.761, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRoleHei", "_parent": {"__id__": 53}, "_children": [{"__id__": 55}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "29MBHE0GZENIeCXLgAoQx2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [6.5, 28, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "songhuadan", "_parent": {"__id__": 54}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 55}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "d6PuohEB1BK6IoFZyGvKDM"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "edElaXdOBITYnnw68YhmEH"}, "_contentSize": {"__type__": "cc.Size", "width": 149, "height": 143.99}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, -1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "f14", "_parent": {"__id__": 53}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "88NtRV06tNUYj3QxvGMuDs"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "e0JkH20lJOTbQiQykeiKaO"}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 238}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeLeft", "_parent": {"__id__": 7}, "_children": [{"__id__": 58}, {"__id__": 59}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "dekXVmXD5MAriXFmgcoQY0"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3, -889.918, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a04", "_parent": {"__id__": 57}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "26boGrf5RBBZdJD+iM/Tnr"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "d6a/j0uMNBvbxr4grqvbF1"}, "_contentSize": {"__type__": "cc.Size", "width": 133, "height": 99}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "suo", "_parent": {"__id__": 57}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 59}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "1bOQp164lBhphVsssZZ4fR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "c9ZWf0ahVMUot5iConW4iT"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRight", "_parent": {"__id__": 7}, "_children": [{"__id__": 61}, {"__id__": 62}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "28tIb7oIFIQ7xYTNHaA+Cb"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [231.958, -889.918, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a05", "_parent": {"__id__": 60}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78HXp7GrZC8paPaf7VorCD"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "757P7Q6YZKQKlDBS31YpmD"}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 101}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "suo", "_parent": {"__id__": 60}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 62}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "1bOQp164lBhphVsssZZ4fR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "7doheIa2ZLgagJqeUd4AC/"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 91}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePropPool", "_parent": {"__id__": 7}, "_children": [{"__id__": 64}, {"__id__": 66}, {"__id__": 68}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}, {"__id__": 76}, {"__id__": 78}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "7f7m/ZZnpI3afParpAtgrC"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [990.682, -925.114, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "Box", "_parent": {"__id__": 63}, "_children": [{"__id__": 65}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 64}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 64}, "tag": 100, "_density": 0, "_offset": {"__type__": "cc.Vec2", "x": -48}, "_size": {"__type__": "cc.Size", "width": 96, "height": 90}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 64}, "msgId": 7, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "f1SCRTBvNBt6rALf8Cp4Ng"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "b01", "_parent": {"__id__": 64}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3aOoN8sc9HZ5BKJFR5YvKz"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "3azzydchRIn7vxJvFAsnwl"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Box", "_parent": {"__id__": 63}, "_children": [{"__id__": 67}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 66}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 66}, "tag": 100, "_density": 0, "_offset": {"__type__": "cc.Vec2", "x": -73}, "_size": {"__type__": "cc.Size", "width": 146, "height": 52}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 66}, "msgId": 7, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "b04YddLG5DS7/N+MFVR8S+"}, "_contentSize": {"__type__": "cc.Size", "width": 146, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [243.091, -66.49, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "h177", "_parent": {"__id__": 66}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "dbBqGka/dAb5UwutYxRVyQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "47NV2roOZPOrz3n4qcDxuO"}, "_contentSize": {"__type__": "cc.Size", "width": 154, "height": 138}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-73.984, 39.252, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Box", "_parent": {"__id__": 63}, "_children": [{"__id__": 69}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 68}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 68}, "tag": 100, "_density": 0, "_offset": {"__type__": "cc.Vec2", "x": -34}, "_size": {"__type__": "cc.Size", "width": 68, "height": 98}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 68}, "msgId": 7, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "2frfvi/KxG/7Lk9MpLyxlY"}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [408.779, -433.307, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "h150", "_parent": {"__id__": 68}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78WXkyIAxPoYCuCJnLIrMo"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "2bvWa7+otNZq05HxDvrscp"}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 105}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-34, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Box", "_parent": {"__id__": 63}, "_children": [{"__id__": 71}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 70}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 70}, "tag": 100, "_offset": {"__type__": "cc.Vec2", "x": -42}, "_size": {"__type__": "cc.Size", "width": 84, "height": 82}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 70}, "msgId": 7, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "046OsVNShINIcKJrzg20UW"}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 82}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [610.834, -259.188, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "h331", "_parent": {"__id__": 70}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 71}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b4kkiobalODotCAG2/8Wp9"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "45cUbRjr5HXa+KDu6ETTL4"}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 90}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-42, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Box", "_parent": {"__id__": 63}, "_children": [{"__id__": 73}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 72}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 72}, "tag": 100, "_offset": {"__type__": "cc.Vec2", "x": -33}, "_size": {"__type__": "cc.Size", "width": 66, "height": 118}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 72}, "msgId": 7, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "9exydYIi5Pcpte6pG/cVHi"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 118}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [633.227, -527.902, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "f49", "_parent": {"__id__": 72}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8f2JFktwlIJL7KmyAFmdGl"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "efaRs9Cz5NWo48aOd7yFxB"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 124}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-33, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Box", "_parent": {"__id__": 63}, "_children": [{"__id__": 75}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 74}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 74}, "tag": 100, "_density": 0, "_offset": {"__type__": "cc.Vec2", "x": -35}, "_size": {"__type__": "cc.Size", "width": 70, "height": 52}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 74}, "msgId": 7, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "bbHfUE809E9I69b75iXbEX"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [900.074, -540.964, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "h569", "_parent": {"__id__": 74}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 75}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "63IlJyeZREZZMuaZfefV3C"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "ff8gYdiPVAzJ7uu8BDOJnB"}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 58}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-35, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Box", "_parent": {"__id__": 63}, "_children": [{"__id__": 77}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 76}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 76}, "tag": 100, "_density": 0, "_offset": {"__type__": "cc.Vec2", "x": -50}, "_size": {"__type__": "cc.Size", "width": 100, "height": 38}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 76}, "msgId": 7, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "7aB7ljJ6xLX7zvk2CPzRcj"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1027.18, -620.285, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "b01", "_parent": {"__id__": 76}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 77}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9f3QtU7ZREO4niCvprVJre"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "d2k0bLorRHzaXjqatQ682z"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "Box", "_parent": {"__id__": 63}, "_children": [{"__id__": 79}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 78}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 78}, "tag": 100, "_offset": {"__type__": "cc.Vec2", "x": -48}, "_size": {"__type__": "cc.Size", "width": 96, "height": 70}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 78}, "msgId": 7, "count": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "0eMVoXZ/BOJKaozChIGnAz"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [822.235, -723.679, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "b01", "_parent": {"__id__": 78}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 79}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "99y/h0wu1Exa2/yXUKqfOG"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "9cDdnT/7NArKQlF1Y+PY/I"}, "_contentSize": {"__type__": "cc.Size", "width": 122, "height": 84}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [12, 3.482, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeJiaZi", "_parent": {"__id__": 7}, "_children": [{"__id__": 81}, {"__id__": 82}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "8chudwkYxNEJWerzOxrYDN"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [700, -25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "close", "_parent": {"__id__": 80}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 81}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0cejNEfhBDs5MCvUOu1l2G"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "1939fl0EpNUqKaTY3J7t6V"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "open", "_parent": {"__id__": 80}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 82}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0bfq1d47hMBJkZQnzncbe3"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "ec9rhqNyVAYLHVoriE9V7R"}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 50}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStage", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 83}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5cV4XY14hDcpEyeJWlMfyi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "f8t8tVfbJOvr+m/KXKoA46"}, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 102}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [197.206, -124, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeStageBlock", "_parent": {"__id__": 7}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 84}, "_type": 0, "_allowSleep": false, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 84}, "_density": 0, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 22}, "_size": {"__type__": "cc.Size", "width": 366, "height": 44}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 84}, "msgId": 9}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "dbk4uNxepClYchF/3eTxoK"}, "_contentSize": {"__type__": "cc.Size", "width": 366, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [197.206, -124, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeDoorEnd", "_parent": {"__id__": 7}, "_children": [{"__id__": 86}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 85}, "_allowSleep": false, "_gravityScale": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 85}, "_offset": {"__type__": "cc.Vec2", "x": -50}, "_size": {"__type__": "cc.Size", "width": 100, "height": 124}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 85}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "8cu8zLcGxJQ68oAhrvNTOE"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1550.743, -282.58, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 85}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 86}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 86}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "9d6/pUTRVJuJicK0nbJdJP"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, -62, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "CC_nodeDialog", "_parent": {"__id__": 7}, "_children": [{"__id__": 88}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "9eEhql1dZHp66NNfaSO6C2"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 415}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "content", "_parent": {"__id__": 87}, "_children": [{"__id__": 89}, {"__id__": 92}, {"__id__": 93}, {"__id__": 97}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 88}, "_defaultClip": {"__uuid__": "5frpcWF55CJbk9Iat/SdPk"}, "_clips": [{"__uuid__": "5frpcWF55CJbk9Iat/SdPk"}, {"__uuid__": "f9cNBzBTxI+qfRdIWC0zsy"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "70O8V8y4tHQY1GrXBA6290"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 88}, "_children": [{"__id__": 90}, {"__id__": 91}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 89}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0bsV9TkGRJ7LnI3/epfjHq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "bfyr4O17lMWK33uZAEDg+q"}, "_contentSize": {"__type__": "cc.Size", "width": 784, "height": 415}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 90}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "在合适的时机跳跃", "_N$string": "在合适的时机跳跃", "_fontSize": 24, "_lineHeight": 24, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "e4nqA+gnZD5KH6OFSOw4dj"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-193, -160, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 89}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 91}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "到达最高处即可过关", "_N$string": "到达最高处即可过关", "_fontSize": 24, "_lineHeight": 24, "_styleFlags": 1, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "9crFErwj9Bjoe1IroQiwAz"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 30.24}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [193, -160, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg1", "_parent": {"__id__": 88}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 92}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "81Xg9zYIlEQqqrwX9Sfbje"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "3ccDW/qpZNbYfWbn9DLAwW"}, "_contentSize": {"__type__": "cc.Size", "width": 261, "height": 243}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-190.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg2", "_parent": {"__id__": 88}, "_children": [{"__id__": 94}, {"__id__": 95}, {"__id__": 96}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 93}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1fnidYnFhDmL3jthjsMoo+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "35rbS+wEZEKricS9jjz5UQ"}, "_contentSize": {"__type__": "cc.Size", "width": 261, "height": 243}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [190.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 93}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 94}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 94}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "4celb9+hJASqe1b1HIXsYB"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [65.138, -14.5, 0, 0, 0, 0, 1, 0.54, 0.54, 0.54]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTigger", "_parent": {"__id__": 93}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 95}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 95}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 50, "height": 68}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 95}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "eeAxf97ItPpoIVBM9QKMR9"}, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 68}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [63.138, 19, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a442", "_parent": {"__id__": 93}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 96}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1tQfbiklIGK2OumRa2DMk"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "26WLWkpZ9Er6e09C31a4gr"}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 15}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [89, 7, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 88}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 97}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a9Ocfw8GpFsYwfyIYFsbLK"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "37la1HTL9H4qGi89nXiWgO"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [375, 150, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 7}, "_children": [{"__id__": 99}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 98}, "_allowSleep": false, "_gravityScale": 3, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 98}, "tag": 50, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 40, "height": 70}}, {"__type__": "ea793GQ97VBI4vTbUU7OY89", "node": {"__id__": 98}, "aniNode": {"__id__": 98}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "4ckywZqwdG7YUQpN6jeLok"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-394.658, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 98}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 99}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "63ddtxK0hKh4evYvoIdZzz"}, "fileId": "ceNp7bfmRGiYhUQBv/GABP"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}], {"__type__": "cc.SpriteFrame", "content": {"name": "a440", "texture": "1d7eb79f9", "rect": [288, 640, 261, 243], "offset": [0, 0], "originalSize": [261, 243], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a438", "texture": "1d7eb79f9", "rect": [839, 806, 54, 53], "offset": [0, 0], "originalSize": [54, 53], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a442", "texture": "1d7eb79f9", "rect": [149, 618, 32, 15], "offset": [0, 0], "originalSize": [32, 15], "capInsets": [0, 0, 0, 0]}}]