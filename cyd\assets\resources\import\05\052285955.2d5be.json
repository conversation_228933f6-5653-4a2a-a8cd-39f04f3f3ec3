[{"__type__": "cc.SpriteFrame", "content": {"name": "a03", "texture": "1e64ef683", "rect": [495, 655, 207, 200], "offset": [0, 0], "originalSize": [207, 200], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a05", "texture": "1e64ef683", "rect": [856, 321, 198, 19], "offset": [0, 0], "originalSize": [198, 19], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a02", "texture": "1e64ef683", "rect": [698, 3, 176, 153], "offset": [0, 0], "originalSize": [176, 153], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vFreeAdGiftDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "parent", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}, {"__id__": 30}, {"__id__": 34}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "f1LuiDk/ZM3ZgAmfCRWLGI"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "72jQQEyjxMpZljVl/EJ7ER"}, "_type": 1, "_sizeMode": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "4cnBxHhO9JKrzw+HZP4++s"}, "_contentSize": {"__type__": "cc.Size", "width": 689, "height": 496}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [6.565, 15.614, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a10", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "bcssftV5BAiJbDyo3ZmUD6"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "a559plEB9CRKRGvweFLRkx"}, "_contentSize": {"__type__": "cc.Size", "width": 197, "height": 51}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [22.521, 218.458, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a09", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f7hx3wHxBNsJRd/qbSP0ID"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "e60Z7RX4dJJa9pR0F+adRF"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 368}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133.385, -22.192, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a08", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "73Ds0uVrZAkIeU0vs8ctL7"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "a4HhX5AwREmJPCLNIqOfwj"}, "_contentSize": {"__type__": "cc.Size", "width": 118, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-134.67, 131.199, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a12", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f60VLhpBtKHrNTvCtdK/uv"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "1572S3JmBMELl5F7fYvXJT"}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 153}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-139.814, 30.011, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a06", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eaI54sBV1EB5Amj3cPb/Bq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "baDV3j9EBBpJKlbkFcQu4S"}, "_contentSize": {"__type__": "cc.Size", "width": 231, "height": 45}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-143.329, -87.725, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a05", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "57Cvtv80ROCrsRyPZuPFuV"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "9dN4reP2lFSpr5H5LpedR4"}, "_contentSize": {"__type__": "cc.Size", "width": 198, "height": 19}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-146.064, -85.888, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a01", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "82zAjplDxLObIeXQxXnmKj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "a49IrDhrRHNrQm4OH4n8y9"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-51.901, -29.34, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelDailyAdTicketCount", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "x99", "_N$string": "x99", "_fontSize": 30, "_lineHeight": 34, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}, {"__type__": "cc.LabelOutline", "node": {"__id__": 11}, "_color": {"__type__": "cc.Color"}, "_width": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "1e4fjjk99KWoP17J+BaBqO"}, "_color": {"__type__": "cc.Color", "r": 255, "g": 246}, "_contentSize": {"__type__": "cc.Size", "width": 52.37, "height": 46.84}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-52.097, -28.962, 0, 0, 0, -0.17364817766693033, 0.984807753012208, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -20}}, {"__type__": "cc.Node", "_name": "CC_buttonDailyAdTicket", "_parent": {"__id__": 2}, "_children": [{"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 17}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 12}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "3aCW/KKyxN576iXqx5KDI+"}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-134.971, -149.797, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "btn_01", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78/01KIaBJGJCxAuSY2x9h"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "dd1zQzPAtLg7CmaSjzMvAD"}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label copy", "_parent": {"__id__": 12}, "_active": false, "_components": [{"__type__": "cc.Label", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "6元", "_N$string": "6元", "_fontSize": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "c0JByX+t9Jpa8nIw8ZN8qD"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 37.35, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-59.375, 10.968, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label copy", "_parent": {"__id__": 12}, "_active": false, "_components": [{"__type__": "cc.Label", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "——", "_N$string": "——", "_fontSize": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "26q8olQu1E2Yt6zSeQjrZL"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-59.375, 10.968, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "6元购买", "_N$string": "6元购买", "_fontSize": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "a2nSRkKlJB+onapche/3Pr"}, "_color": {"__type__": "cc.Color", "r": 225, "g": 40, "b": 40}, "_contentSize": {"__type__": "cc.Size", "width": 85.35, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.712, 10.968, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label copy", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "（每日限购1次）", "_N$string": "（每日限购1次）", "_fontSize": 16, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "b8orEYh09I2L30ytzeS9EH"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120.9, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, -14.635, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a14", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5fv0nlWSZMnpgg41eKizcg"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "94IUTB7XhPULUmUiops/mI"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 368}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [143.725, -22.104, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a13", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "bdKBlTyxNPMr8L3K3tjJwP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "1aW6kmVIdKtrs4eKkeYpEu"}, "_contentSize": {"__type__": "cc.Size", "width": 118, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [140.957, 132.144, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a02", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5aoDG1AVlBPZ7hR1t6cpIS"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "17ypvm26xLwYfqFz6CmgNd"}, "_contentSize": {"__type__": "cc.Size", "width": 176, "height": 153}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [139.698, 25.68, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a06", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "eaI54sBV1EB5Amj3cPb/Bq"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "c4vZmMhPpM8aCfAmyQx2ON"}, "_contentSize": {"__type__": "cc.Size", "width": 231, "height": 45}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128.836, -88.66, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a07", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a0ue2yBJ5LarbFd33SZwrg"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "89k9ejDAROCJeCejVhWUHA"}, "_contentSize": {"__type__": "cc.Size", "width": 152, "height": 19}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [127.821, -85.964, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a01", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "82zAjplDxLObIeXQxXnmKj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "a24tv4Vt5LQYO1Mxans6HC"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 67}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [226.604, -32.965, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_labelForeverAdTicketCount", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "x999", "_N$string": "x999", "_fontSize": 30, "_lineHeight": 34, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}, {"__type__": "cc.LabelOutline", "node": {"__id__": 24}, "_color": {"__type__": "cc.Color"}, "_width": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "717gz411FJHbs0r/JecyH/"}, "_color": {"__type__": "cc.Color", "r": 255, "g": 246}, "_contentSize": {"__type__": "cc.Size", "width": 69.05, "height": 46.84}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [226.098, -32.04, 0, 0, 0, -0.08715574274765817, 0.9961946980917455, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -10}}, {"__type__": "cc.Node", "_name": "a03", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "32UjdiBX9GtJ/9NEFpLnye"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "1f91Nn/nZIdL6qOi0/7v9V"}, "_contentSize": {"__type__": "cc.Size", "width": 207, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [350.762, -140.398, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a11", "_parent": {"__id__": 2}, "_children": [{"__id__": 27}, {"__id__": 28}, {"__id__": 29}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "9efxlepz9MvajwmyfFxZ8K"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "891rO5GqdJdroQfxx2kRTp"}, "_contentSize": {"__type__": "cc.Size", "width": 204, "height": 74}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [381.883, -12.127, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "不想看广告？那就", "_N$string": "不想看广告？那就", "_fontSize": 18, "_lineHeight": 22, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "c3VTVGN7ZGWqAVPvrTrL3U"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 27.72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, 17.14, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label copy", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "试试免广券", "_N$string": "试试免广券", "_fontSize": 18, "_lineHeight": 22, "_styleFlags": 4, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "2d7axNUUdE+p6BcyUXzD/8"}, "_color": {"__type__": "cc.Color", "r": 255, "b": 90}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 27.72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-31.726, -3.649, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label copy", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "吧！", "_N$string": "吧！", "_fontSize": 18, "_lineHeight": 22, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "3dhzczH6ZL67i8o2JGZp7T"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 36, "height": 27.72}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [34.757, -2.942, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonForeverAdTicket", "_parent": {"__id__": 2}, "_children": [{"__id__": 31}, {"__id__": 32}, {"__id__": 33}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 30}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "e8cIxRJYdKLI+WwOJm6vBV"}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [140.454, -150.265, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "btn_01", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "78/01KIaBJGJCxAuSY2x9h"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "c01LL4C6pLkLvYw3NNSgiu"}, "_contentSize": {"__type__": "cc.Size", "width": 191, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "18元购买", "_N$string": "18元购买", "_fontSize": 24, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "5fmLn0A7hImrObOYg+YJhM"}, "_color": {"__type__": "cc.Color", "r": 225, "g": 40, "b": 40}, "_contentSize": {"__type__": "cc.Size", "width": 98.7, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.062, 10.968, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "New Label copy", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "（每日限购1次）", "_N$string": "（每日限购1次）", "_fontSize": 16, "_lineHeight": 20, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "c4N+O1gIxPs6dB3FHZY+QR"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120.9, "height": 25.2}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, -14.635, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 2}, "_children": [{"__id__": 35}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 34}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "a1qdR4/jZKo5zsjt45pafM"}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [278, 226.632, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "btn_01", "_parent": {"__id__": 34}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "48+6YuBptMr4rHwXDDBRsQ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5eVySV9wVBYqUznXcuZ6hM"}, "fileId": "a5LqdZY/xLGK9QN+bl1z/w"}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 53}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "a14", "texture": "1e64ef683", "rect": [3, 505, 240, 368], "offset": [0, 0], "originalSize": [240, 368], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a04", "texture": "1e64ef683", "rect": [3, 3, 689, 496], "offset": [0, 0], "originalSize": [689, 496], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a08", "texture": "1e64ef683", "rect": [698, 346, 118, 30], "offset": [0, 0], "originalSize": [118, 30], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a01", "texture": "1e64ef683", "rect": [698, 418, 96, 67], "offset": [0, 0], "originalSize": [96, 67], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a11", "texture": "1e64ef683", "rect": [701, 655, 204, 74], "offset": [0, 0], "originalSize": [204, 74], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a07", "texture": "1e64ef683", "rect": [698, 321, 152, 19], "offset": [0, 0], "originalSize": [152, 19], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a10", "texture": "1e64ef683", "rect": [781, 655, 197, 51], "offset": [0, 0], "originalSize": [197, 51], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a13", "texture": "1e64ef683", "rect": [698, 382, 118, 30], "offset": [0, 0], "originalSize": [118, 30], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a06", "texture": "1e64ef683", "rect": [800, 418, 231, 45], "offset": [0, 0], "originalSize": [231, 45], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a12", "texture": "1e64ef683", "rect": [698, 162, 176, 153], "offset": [0, 0], "originalSize": [176, 153], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a09", "texture": "1e64ef683", "rect": [249, 505, 240, 368], "offset": [0, 0], "originalSize": [240, 368], "capInsets": [0, 0, 0, 0]}}]