[{"__type__": "cc.AnimationClip", "_name": "map11047trap01Anim", "_duration": 1, "wrapMode": 2, "curveData": {"paths": {"1": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineOut"}, {"frame": 0.5, "value": 250, "curve": "sineIn"}, {"frame": 1, "value": 85}]}}, "2": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineOut"}, {"frame": 0.5, "value": 250, "curve": "sineIn"}, {"frame": 1, "value": 85}]}}, "3": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineOut"}, {"frame": 0.5, "value": 250, "curve": "sineIn"}, {"frame": 1, "value": 85}]}}, "4": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineOut"}, {"frame": 0.5, "value": 250, "curve": "sineIn"}, {"frame": 1, "value": 85}]}}, "5": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineOut"}, {"frame": 0.5, "value": 250, "curve": "sineIn"}, {"frame": 1, "value": 85}]}}, "6": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineOut"}, {"frame": 0.5, "value": 250, "curve": "sineIn"}, {"frame": 1, "value": 85}]}}, "7": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineOut"}, {"frame": 0.5, "value": 250, "curve": "sineIn"}, {"frame": 1, "value": 85}]}}, "8": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineOut"}, {"frame": 0.5, "value": 250, "curve": "sineIn"}, {"frame": 1, "value": 85}]}}}}}, {"__type__": "cc.AnimationClip", "_name": "map11047trap011Anim", "_duration": 2, "wrapMode": 2, "curveData": {"paths": {"1": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineIn"}, {"frame": 1, "value": -155, "curve": "sineOut"}, {"frame": 2, "value": 85}]}}, "2": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineIn"}, {"frame": 1, "value": -155, "curve": "sineOut"}, {"frame": 2, "value": 85}]}}, "3": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineIn"}, {"frame": 1, "value": -155, "curve": "sineOut"}, {"frame": 2, "value": 85}]}}, "4": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineIn"}, {"frame": 1, "value": -155, "curve": "sineOut"}, {"frame": 2, "value": 85}]}}, "5": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineIn"}, {"frame": 1, "value": -155, "curve": "sineOut"}, {"frame": 2, "value": 85}]}}, "6": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineIn"}, {"frame": 1, "value": -155, "curve": "sineOut"}, {"frame": 2, "value": 85}]}}, "7": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineIn"}, {"frame": 1, "value": -155, "curve": "sineOut"}, {"frame": 2, "value": 85}]}}, "8": {"props": {"y": [{"frame": 0, "value": 85, "curve": "sineIn"}, {"frame": 1, "value": -155, "curve": "sineOut"}, {"frame": 2, "value": 85}]}}}}}]