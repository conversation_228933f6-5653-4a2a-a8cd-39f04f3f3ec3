[{"__type__": "cc.SpriteFrame", "content": {"name": "a16", "texture": "32QI9DoCdEA5PgMWm1VmQ+", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a22", "texture": "0dV8vEE45NqY80BIKe3W1R", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a24", "texture": "9ba9PS91hA7blNlQd8PU16", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a20", "texture": "77jQexxo9Ir45haEOJfTqA", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a19", "texture": "26o0JyrRxET7PnePFMPmSM", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a26", "texture": "94V6vS3FlOza01MUWE9IOA", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "map292", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "map292", "_children": [{"__id__": 2}, {"__id__": 78}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 36}, {"__id__": 72}, {"__id__": 75}, {"__id__": 77}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5evd81slVOqJyUBAGqaEG1"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "a8+mjlyNhN5I413awzXze3"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodePicture", "_parent": {"__id__": 2}, "_children": [{"__id__": 5}, {"__id__": 6}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 16}, {"__id__": 17}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c5/TRPJ/dCDbb4zobqk08L"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "b4DaSR305B1o7nS7XW/Ba+"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a15", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b6R/8s8WxJu6W+34qmW7RI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "6f+pByY3pJBLMoRhauGlgg"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "4", "_parent": {"__id__": 4}, "_children": [{"__id__": 7}], "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 6}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "60H5zBIMlPE561eY8tuQg9"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-600.427, 299.515, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "863Z31RlRHZpMvCZ8Gh/+P"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "eeHIqjbBBLoqK0wH5eChi0"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, -0.08715574274765817, 0.9961946980917455, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": -10}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a16", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00LwbKuxJLA7F/KJ03Irkt"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "92zzl0dFRLuJuHssiVoB9a"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a17", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "c4R7POOHdCVLMwT+7V+rZj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "01xVZaixBAMInlsNvByQx2"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "7", "_parent": {"__id__": 4}, "_children": [{"__id__": 11}], "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 10}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "a4OY07gV5Omb6qIaCmr9oO"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [988.031, 24.964, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "13cZKzdTJPJZ7k8NawfFcT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "51V9rms81HKYLTcQ65sOn0"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0.13052619222005157, 0.9914448613738104, 0.8, 0.8, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 15}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a18", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d2wr2BFzRCMqg/4vCxjOGd"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "ffH1/VnOVPw7JweZW4j7Rw"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a19", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3dkIc9AVpOpYSoVcL22vhW"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "d06/3HtIFK8bnlsKRYM4sG"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "3", "_parent": {"__id__": 4}, "_children": [{"__id__": 15}], "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 14}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "04469TFTVJTZvV2ILG+9bE"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-730.228, 10.603, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "88nUdRir5L56L6rovt8X3a"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "92Fd5sS+JMF5OeNWhB5l07"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a20", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "21Cw+A2KBKM6PN9S4EQQck"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "947gn9uFdJaJH5QjQYg+r4"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "5", "_parent": {"__id__": 4}, "_children": [{"__id__": 18}], "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 17}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "9385ajQ6RIOKMx4L1uaY6t"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-891.951, -210.376, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 17}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 18}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b24aRC9dVKEbAhZfsaJMSn"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "a8Xesup35OhrzzRt39onIa"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a21", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "94cnwSMuRCa5Jcioxw2lby"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "00ulF31OBNpaWl3j85I5zU"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a22", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0eHmC/RmRAXrv746cupFMc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "fcFuLVZbZElKHqsj9cZmTV"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a23", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8eRJNsq+BPgYJtZ4WtpJFt"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "3a38o0A3RFqICpkRjEHfZY"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a24", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "1e4ME2xnZBEZq7+P9ubV4W"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "32ql2YmftE0LD1GO4BT+TX"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 4}, "_children": [{"__id__": 24}], "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 23}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "8awbLCl9BJYablskoNYyZp"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-424.491, -88.86, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d4uTewOPpMyr/xM3k5J3nj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "95VgW1JD1PzbWj8ajdRYav"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "8", "_parent": {"__id__": 4}, "_children": [{"__id__": 26}], "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 25}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "aaH99obRZHloEBmi1+/Syl"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-109.937, 197.571, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 25}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 26}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2b4XsZr6VJiKbsw7INKr/P"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "52Y4uNqYRPYY/gVz64ew1v"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a25", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f4Q/vuildEpoNFNphZ+4dI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "96oOP4ZLlF077/9skazMaf"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a26", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "4792PEj6hBVoLI0LCPA1ek"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "717t3VIB5Ia5KUCXxkAQoG"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a27", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b3QRb60l5PoYCB9ZVITDdF"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "31NF43mrRG0Ym1fidUuXhB"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "2", "_parent": {"__id__": 4}, "_children": [{"__id__": 31}], "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 30}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "c2+4sPyrlCgKdkbr5HLbm1"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [767.321, -340.721, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ceRpwAIFNIdqoBFomn2Wmi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "87tRUm5c5AmJDP9icteapA"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a28", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "99+9EXLEZBj7RjpCXKWsLm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "9bU/qQseROx66C3PpayaRz"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a29", "_parent": {"__id__": 4}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a7QeQftOtPQbD3UTWc3goS"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "32vCPvdwlHqJspkST9GFy2"}, "_contentSize": {"__type__": "cc.Size", "width": 2048, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "6", "_parent": {"__id__": 4}, "_children": [{"__id__": 35}], "_components": [{"__type__": "cc.BlockInputEvents", "node": {"__id__": 34}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "cbel97Fd5DMJmCvaUoeSb+"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [435.553, 176.487, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 34}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "59JyZ/m7NE+5Qok4jZIDtA"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "03NvIYnVFKj7G3Y8q1wF+z"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTask", "_parent": {"__id__": 2}, "_children": [{"__id__": 37}, {"__id__": 38}, {"__id__": 71}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "405JVzSsBBtZAA8iKa/mvn"}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -288, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "faXIV05tBELYu9WF4rMKK6"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "57X3LIBZRP3JLr75vSqAvf"}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTaskItems", "_parent": {"__id__": 36}, "_children": [{"__id__": 39}, {"__id__": 43}, {"__id__": 47}, {"__id__": 51}, {"__id__": 55}, {"__id__": 59}, {"__id__": 63}, {"__id__": 67}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 38}, "_layoutSize": {"__type__": "cc.Size", "width": 750, "height": 80}, "_N$layoutType": 1, "_N$paddingLeft": 35, "_N$spacingX": 15}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "caewnfOhZNIKSCyzH5Fpri"}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 80}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 38}, "_children": [{"__id__": 40}, {"__id__": 41}, {"__id__": 42}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "20bbVlX9lEGaQlIumLfSAf"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-305, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 39}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02kbL+Co9JXY9ojnyzq1qW"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "126f0Yl31NfYF0eq92N/9y"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 39}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d4uTewOPpMyr/xM3k5J3nj"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "e9ck7zCJ9AfYG56KCYCN7f"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "unknown", "_parent": {"__id__": 39}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "?", "_N$string": "?", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "90hiCdRXlFCaXQPrneHeut"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "2", "_parent": {"__id__": 38}, "_children": [{"__id__": 44}, {"__id__": 45}, {"__id__": 46}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "bbvyExIr9DiJppS0+aTb/e"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-220, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02kbL+Co9JXY9ojnyzq1qW"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "5c+i9XHYZJZ5g6jpDRZ16A"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "ceRpwAIFNIdqoBFomn2Wmi"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "9aD7yCd35L06WkfvU6BFA7"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "unknown", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "?", "_N$string": "?", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "58Pjh1HLBFhqZ116Py4/IQ"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "3", "_parent": {"__id__": 38}, "_children": [{"__id__": 48}, {"__id__": 49}, {"__id__": 50}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "ba+nPOnrZNXpA6xxsfWpx3"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-135, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02kbL+Co9JXY9ojnyzq1qW"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "2c9/GWMmZKBJgaV9s3lgG0"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "88nUdRir5L56L6rovt8X3a"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "87lEAMBHpNyreHzRG4bKd9"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "unknown", "_parent": {"__id__": 47}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "?", "_N$string": "?", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "56klb+wiJNqIk2aseckqoQ"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "4", "_parent": {"__id__": 38}, "_children": [{"__id__": 52}, {"__id__": 53}, {"__id__": 54}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "9exD9IzW5Bp7G4yXxFiCYW"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 51}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02kbL+Co9JXY9ojnyzq1qW"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "71Bkr8IsRFMpN+dZrLgjXm"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 51}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "863Z31RlRHZpMvCZ8Gh/+P"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "bdc+l9kBZPZZ9xrUe+5Jub"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "unknown", "_parent": {"__id__": 51}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "?", "_N$string": "?", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "2aKK8Vy+xP0a8huSmWgYu4"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "5", "_parent": {"__id__": 38}, "_children": [{"__id__": 56}, {"__id__": 57}, {"__id__": 58}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "4avaP/+zhMtYo9q0K5LAMs"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [35, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02kbL+Co9JXY9ojnyzq1qW"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "62tPvyEedIoo2nyW+FOV7U"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "b24aRC9dVKEbAhZfsaJMSn"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "70viV9xPtC5ZSq/iRjjPzX"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "unknown", "_parent": {"__id__": 55}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "?", "_N$string": "?", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "21vZTLu3ZABo/o7rvZMur6"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "6", "_parent": {"__id__": 38}, "_children": [{"__id__": 60}, {"__id__": 61}, {"__id__": 62}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "6e9RnJ5/5MSan5DRJZBCk4"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 59}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02kbL+Co9JXY9ojnyzq1qW"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "f9j/zKCABAtJ4rfhr7CAD3"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 59}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "59JyZ/m7NE+5Qok4jZIDtA"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "4fREaTlLZJSbB9r/82bVdM"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "unknown", "_parent": {"__id__": 59}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "?", "_N$string": "?", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "cewhrbxpBIHKQeUf0kg2bq"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "7", "_parent": {"__id__": 38}, "_children": [{"__id__": 64}, {"__id__": 65}, {"__id__": 66}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "83mtZDl4xB7ISeCCs9eh2N"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [205, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 63}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 64}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02kbL+Co9JXY9ojnyzq1qW"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "d4o64ZZbJNxaFhZrEIy2xv"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 63}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "13cZKzdTJPJZ7k8NawfFcT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "1eDYQm+EpAb5DjnOckPz/F"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "unknown", "_parent": {"__id__": 63}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 66}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "?", "_N$string": "?", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "30DQxC/LVM/Zg15F0/2ytE"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "8", "_parent": {"__id__": 38}, "_children": [{"__id__": 68}, {"__id__": 69}, {"__id__": 70}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "dc0a7CpjpM4paKZuy5lOAn"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 69}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [290, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bg", "_parent": {"__id__": 67}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 68}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "02kbL+Co9JXY9ojnyzq1qW"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "43+LgG8k5JTaHKhDkg00Ii"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 70}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 67}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "2b4XsZr6VJiKbsw7INKr/P"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "57Gk//bqFMnp/pc4ViE0L9"}, "_contentSize": {"__type__": "cc.Size", "width": 70, "height": 94}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "unknown", "_parent": {"__id__": 67}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 70}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "?", "_N$string": "?", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "801VAD+aNM7KRz8lEkNAdB"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "desc", "_parent": {"__id__": 36}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 71}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "找到节奏人", "_N$string": "找到节奏人", "_fontSize": 24, "_lineHeight": 28, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "a4d2h5rjxEorp7J7s3nQBm"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 35.28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 18, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeCircle", "_parent": {"__id__": 2}, "_children": [{"__id__": 73}], "_components": [{"__type__": "cc.ProgressBar", "node": {"__id__": 72}, "_N$barSprite": {"__id__": 74}, "_N$mode": 2, "_N$reverse": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "caHDFWsO5MOqyugBNdTNMt"}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 119}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "bar", "_parent": {"__id__": 72}, "_components": [{"__id__": 74}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "efy8Dr3IdEd6Z0oikVXp8o"}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 119}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "bdcpCZFv1Cf7IyFUMojFtL"}, "_type": 3, "_sizeMode": 2, "_fillType": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_fillStart": 0.8, "_fillRange": -1}, {"__type__": "cc.Node", "_name": "CC_nodeTipEff", "_parent": {"__id__": 2}, "_children": [{"__id__": 76}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "e2NixZ+wxBHYvVSdFIjWbb"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "f42", "_parent": {"__id__": 75}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 76}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8cd2vAv4dJBpXeUm/83LuI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "f41Zw2KJlM4b1dnngfUOeD"}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 136}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeNo", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 77}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0fcyDHuHBJibaFtU6dVdWI"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "feVNw/MYBLQb433UZjrRyI"}, "_contentSize": {"__type__": "cc.Size", "width": 84, "height": 78}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeDialog", "_parent": {"__id__": 1}, "_children": [{"__id__": 79}, {"__id__": 80}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "13KNHzBQRCzprNQGE5izs3"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "mask", "_parent": {"__id__": 78}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 79}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}, {"__type__": "cc.BlockInputEvents", "node": {"__id__": 79}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "57M6NiPa9LqLrFJNlsvEJU"}, "_opacity": 123, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "content", "_parent": {"__id__": 78}, "_children": [{"__id__": 81}, {"__id__": 82}], "_components": [{"__type__": "cc.Animation", "node": {"__id__": 80}, "_defaultClip": {"__uuid__": "5frpcWF55CJbk9Iat/SdPk"}, "_clips": [{"__uuid__": "5frpcWF55CJbk9Iat/SdPk"}, {"__uuid__": "f9cNBzBTxI+qfRdIWC0zsy"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "3eNbBbnCZMjYJIxWEh8Ae3"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "e16", "_parent": {"__id__": 80}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 81}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "75EAH3vQNF5afrH3HgYhYJ"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "422k4M6N1JNp839EkTz1D+"}, "_contentSize": {"__type__": "cc.Size", "width": 484, "height": 205}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "tip", "_parent": {"__id__": 80}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 82}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "找到所有节奏人", "_N$string": "找到所有节奏人", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "5e6t+ubtZNpKWd3uK1RBrP"}, "fileId": "7aOxL7J8hJ6YDI+sLxZNb2"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 10, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "a23", "texture": "85xm9M9hFM7pnT8f7idh1u", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a21", "texture": "a1W4QGxcxKj7dYF141WuEV", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a28", "texture": "39nIQ6gMpIdJ0S32ILj53N", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a29", "texture": "e4rUK68EtGspxsOWf9e6Zv", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a27", "texture": "71yTaerP1Ptb4VTAi14C4m", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a15", "texture": "9ce8HHuJRLza4RSzyCMdYZ", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a17", "texture": "06OWESJmlOC7L+qixVXzM6", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a18", "texture": "bfvQ3Gw8hM7ZI/cmRou7fD", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a25", "texture": "eayR38Ub1I65BZa5+rtCR0", "rect": [0, 0, 2048, 768], "offset": [0, 0], "originalSize": [2048, 768], "capInsets": [0, 0, 0, 0]}}]