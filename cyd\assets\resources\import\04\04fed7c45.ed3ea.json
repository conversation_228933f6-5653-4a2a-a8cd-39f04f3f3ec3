[{"__type__": "cc.SpriteFrame", "content": {"name": "h108", "texture": "12312c5c3", "rect": [457, 956, 13, 64], "offset": [0, 0], "originalSize": [13, 64], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h107", "texture": "1ba78d6c6", "rect": [870, 968, 52, 52], "offset": [0, 0], "originalSize": [52, 52], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h103", "texture": "1bca0c226", "rect": [572, 706, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h104", "texture": "1ba78d6c6", "rect": [608, 259, 52, 52], "offset": [0, 0], "originalSize": [52, 52], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h109", "texture": "12312c5c3", "rect": [476, 956, 64, 13], "offset": [0, 0], "originalSize": [64, 13], "rotated": 1, "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h105", "texture": "1ba78d6c6", "rect": [666, 259, 52, 52], "offset": [0, 0], "originalSize": [52, 52], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h102", "texture": "1bca0c226", "rect": [602, 950, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h106", "texture": "1ba78d6c6", "rect": [724, 259, 52, 52], "offset": [0, 0], "originalSize": [52, 52], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "h101", "texture": "1bca0c226", "rect": [617, 662, 100, 100], "offset": [0, 0], "originalSize": [100, 100], "capInsets": [20, 20, 20, 20]}}]