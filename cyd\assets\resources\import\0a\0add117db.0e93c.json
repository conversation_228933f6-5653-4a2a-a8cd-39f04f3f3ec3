[{"__type__": "cc.AnimationClip", "_name": "map11050tigger04Anim", "_duration": 0.05, "curveData": {"props": {"y": [{"frame": 0, "value": 615}, {"frame": 0.05, "value": 315}]}}}, {"__type__": "cc.AnimationClip", "_name": "map11050trap02Anim", "_duration": 0.08333333333333333, "curveData": {"props": {"y": [{"frame": 0, "value": -238}, {"frame": 0.08333333333333333, "value": -180}]}}}, [{"__type__": "cc.Prefab", "_name": "map11050", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "11050", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 13}, {"__id__": 14}, {"__id__": 17}, {"__id__": 25}, {"__id__": 24}, {"__id__": 28}, {"__id__": 18}, {"__id__": 26}, {"__id__": 34}, {"__id__": 42}, {"__id__": 50}, {"__id__": 79}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTips", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "24+f2lyxREZ62fHEZWB+8m"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23.423, -27.327, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "tips", "_parent": {"__id__": 3}, "_children": [{"__id__": 5}, {"__id__": 10}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "92PLK2GmhOV41moSdABxEo"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [182.379, 0, 219.97045, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 4}, "_children": [{"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 5}, "_enabled": false, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "b4Mfjbj+tOnpiOSQzN5rHA"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-109.174, -52.75, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc copy", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "跳到足球上后\n在足球上升时进行跳跃", "_N$string": "跳到足球上后\n在足球上升时进行跳跃", "_fontSize": 32, "_lineHeight": 32, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "78Bc7tTMxAq4nCpsA7ibAy"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 72.32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-584.088, 166.103, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "labelDesc copy", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "不要跳上平台\n碰到此处后\n立即返回足球", "_N$string": "不要跳上平台\n碰到此处后\n立即返回足球", "_fontSize": 32, "_lineHeight": 32, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "37X/eT2yRH3IGpICllPX7I"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 104.32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-302.67, 276.349, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "labelDesc copy", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "跳上平台后注意\n躲避右方的足球", "_N$string": "跳上平台后注意\n躲避右方的足球", "_fontSize": 32, "_lineHeight": 32, "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "b4tWwKenNO641SERzcbREQ"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 224, "height": 72.32}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [409.448, 170.209, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "a20", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fd4iXgx4hNG7rVTsSGSEWm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "d4cOsEcCpDIa+7qYhsC2Xu"}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-458.555, 310.258, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "1 copy", "_parent": {"__id__": 4}, "_children": [{"__id__": 11}, {"__id__": 12}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 10}, "_enabled": false, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "b9MaEJontHs7D/4eyY/DzI"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 168.656, 0, 0, 0, 1, 6.123233995736766e-17, 1.25, 1.25, 1.25]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}}, {"__type__": "cc.Node", "_name": "h48", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dnO3ZNAdEB6IYoyNjBjtH"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "dfJQm849xEhpkUbE/vsLA+"}, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [409.753, -67.379, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}}, {"__type__": "cc.Node", "_name": "h47", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "16P8uw8MJLJKdDk8H1+tH+"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "25B2OhGPtGvKpDgG8d9j8X"}, "_contentSize": {"__type__": "cc.Size", "width": 27, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-144.318, 39.425, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "z": 180}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "蹦蹦跳跳4", "_N$string": "蹦蹦跳跳4", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "a3Z0S6kvJIfpSMwPKHvl1i"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 182.25, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 242.547, 0, 0, 0, 0, 1, 1.25, 1.25, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "door", "_parent": {"__id__": 2}, "_children": [{"__id__": 15}, {"__id__": 16}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 14}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 14}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 50}, "_size": {"__type__": "cc.Size", "width": 80, "height": 100}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 14}}, {"__type__": "cc.Animation", "node": {"__id__": 14}, "_clips": [null, {"__uuid__": "dbQ/7aKklCvox5G9jhPvTx"}, {"__uuid__": "74UE9aZ/xL2bQMiOyIXq+f"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "4cIEq4dZVKs6OYU9fQrn8Q"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [504, 155, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 14}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 15}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 15}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "ed9zuWCilNZa29NFvGzvrc"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 16}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "8dazftxM5HEKqcDcsnN2kO"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 118.66, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "tiggerActionNode02", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 17}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 17}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 150, "height": 200}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 17}, "tagetNode": {"__id__": 14}, "animClips": {"__uuid__": "dbQ/7aKklCvox5G9jhPvTx"}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 17}, "tagetNode": {"__id__": 18}, "animClips": {"__uuid__": "5bNmMoghtJI5QiV+/+Rrxx"}, "delayTime": 1.5}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 17}, "tagetNode": {"__id__": 24}, "animClips": {"__uuid__": "0fqK4ZOSJGIo6/dbnG6y30"}}, {"__type__": "cc.Animation", "node": {"__id__": 17}, "_clips": [{"__uuid__": "faBgfNvvFKWKyLzHzfCC/T"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "87OR/YR2dAgZ9aWt30hppk"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [436.731, 269.699, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap03", "_parent": {"__id__": 2}, "_children": [{"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 18}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 400}, "_N$layoutType": 1, "_N$spacingX": 80}, {"__type__": "cc.Animation", "node": {"__id__": 18}, "_clips": [null, {"__uuid__": "5bNmMoghtJI5QiV+/+Rrxx"}, {"__uuid__": "d62UIQbkxBBqq0itgdvt9x"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "68KPz5bc5AXYx1qxGi0/nt"}, "_contentSize": {"__type__": "cc.Size", "width": 400}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1080, 234, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 19}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 19}, "_friction": 0.3, "_radius": 30}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "19wpv8O+JLF4Bcu1MXCkA9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-168, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "2", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 20}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 20}, "_friction": 0.3, "_radius": 30}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "51rAU7zZBI0Y0VgPExuTP7"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-24, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "3", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 21}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 21}, "_friction": 0.3, "_radius": 30}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "65fyOtygRCgIXwbVL7Uvvy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [120, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "4", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 22}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 22}, "_friction": 0.3, "_radius": 30}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "9deAGD2NhECaslkGagk9gX"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [264, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "5", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 23}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsCircleCollider", "node": {"__id__": 23}, "_friction": 0.3, "_radius": 30}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "79kdsEzRBHO7yj1Ty40rk2"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [408, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "tiggerActionNode04", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 24}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 24}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 120, "height": 200}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 24}, "tagetNode": {"__id__": 14}, "animClips": {"__uuid__": "74UE9aZ/xL2bQMiOyIXq+f"}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 24}, "tagetNode": {"__id__": 18}, "animClips": {"__uuid__": "d62UIQbkxBBqq0itgdvt9x"}, "delayTime": 0.1}, {"__type__": "cc.Animation", "node": {"__id__": 24}, "_clips": [null, {"__uuid__": "0fqK4ZOSJGIo6/dbnG6y30"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "90wLzQ1yxKbIe7rKNwcRke"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-401, 615, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "tiggerActionNode03", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 25}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 25}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 1600, "height": 50}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 25}, "tagetNode": {"__id__": 26}, "animClips": {"__uuid__": "100oFPHlpC/I+k8hkZCBqc"}}, {"__type__": "cc.Animation", "node": {"__id__": 25}, "_clips": [null, {"__uuid__": "1aJ9ae9XxEhILpuBlUVT6G"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "e7DorX/OhBUbqUv2VFQ8MN"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -152.451, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap02", "_parent": {"__id__": 2}, "_children": [{"__id__": 27}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 26}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 26}, "_size": {"__type__": "cc.Size", "width": 1220, "height": 22}}, {"__type__": "cc.Animation", "node": {"__id__": 26}, "_clips": [null, null, {"__uuid__": "100oFPHlpC/I+k8hkZCBqc"}]}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 26}, "msgId": 5}, {"__type__": "cc.Layout", "node": {"__id__": 26}, "_layoutSize": {"__type__": "cc.Size", "width": 1220, "height": 22}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "61fnkCLkBGp5E0SolEDlMF"}, "_contentSize": {"__type__": "cc.Size", "width": 1220, "height": 22}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -238, 0, 0, 0, 0, 1, 1.25, 1.25, 1.25]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "b19", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "879GXWvOFBaYvGbYunodWG"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 27}, "_alignFlags": 40, "_originalWidth": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "30OFRQTAdLsLNuwbrEoL+5"}, "_contentSize": {"__type__": "cc.Size", "width": 2440, "height": 44}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap01", "_parent": {"__id__": 2}, "_children": [{"__id__": 29}, {"__id__": 30}, {"__id__": 31}, {"__id__": 32}, {"__id__": 33}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 28}, "_layoutSize": {"__type__": "cc.Size", "width": 980, "height": 100}, "_N$layoutType": 1, "_N$paddingLeft": 175, "_N$spacingX": 80}, {"__type__": "cc.Animation", "node": {"__id__": 28}, "_defaultClip": {"__uuid__": "69OHtaNY1JUYUfFddk8KUR"}, "_clips": [{"__uuid__": "69OHtaNY1JUYUfFddk8KUR"}], "playOnLoad": true}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "22bteKKhZJYoMeg83WTxDA"}, "_contentSize": {"__type__": "cc.Size", "width": 980, "height": 100}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 79, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 29}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsPolygonCollider", "node": {"__id__": 29}, "points": [{"__type__": "cc.Vec2", "x": -15, "y": -24}, {"__type__": "cc.Vec2", "x": 15, "y": -24}, {"__type__": "cc.Vec2", "x": 28}, {"__type__": "cc.Vec2", "x": 14, "y": 25}, {"__type__": "cc.Vec2", "x": -14, "y": 25}, {"__type__": "cc.Vec2", "x": -28}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "96z+5ebBFJTqBBa9eJhHaD"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-283, 80, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "2", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 30}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsPolygonCollider", "node": {"__id__": 30}, "points": [{"__type__": "cc.Vec2", "x": -15, "y": -24}, {"__type__": "cc.Vec2", "x": 15, "y": -24}, {"__type__": "cc.Vec2", "x": 28}, {"__type__": "cc.Vec2", "x": 14, "y": 25}, {"__type__": "cc.Vec2", "x": -14, "y": 25}, {"__type__": "cc.Vec2", "x": -28}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "1eNJxOndFFS59zShTAzsLD"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-139, -226, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "3", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 31}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsPolygonCollider", "node": {"__id__": 31}, "points": [{"__type__": "cc.Vec2", "x": -15, "y": -24}, {"__type__": "cc.Vec2", "x": 15, "y": -24}, {"__type__": "cc.Vec2", "x": 28}, {"__type__": "cc.Vec2", "x": 14, "y": 25}, {"__type__": "cc.Vec2", "x": -14, "y": 25}, {"__type__": "cc.Vec2", "x": -28}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "1dIZRpCZFGOZtWn9aqFrm0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 80, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "4", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 32}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsPolygonCollider", "node": {"__id__": 32}, "points": [{"__type__": "cc.Vec2", "x": -15, "y": -24}, {"__type__": "cc.Vec2", "x": 15, "y": -24}, {"__type__": "cc.Vec2", "x": 28}, {"__type__": "cc.Vec2", "x": 14, "y": 25}, {"__type__": "cc.Vec2", "x": -14, "y": 25}, {"__type__": "cc.Vec2", "x": -28}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "6fDvIXhvBDfL8NrHH3NZBy"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [149, -226, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "5", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a1B66fEgRH+aaAznKYtWCx"}}, {"__type__": "cc.RigidBody", "node": {"__id__": 33}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsPolygonCollider", "node": {"__id__": 33}, "points": [{"__type__": "cc.Vec2", "x": -15, "y": -24}, {"__type__": "cc.Vec2", "x": 15, "y": -24}, {"__type__": "cc.Vec2", "x": 28}, {"__type__": "cc.Vec2", "x": 14, "y": 25}, {"__type__": "cc.Vec2", "x": -14, "y": 25}, {"__type__": "cc.Vec2", "x": -28}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "28Sg1ySV1Aoqo1Hg8HpxFI"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [293, 80, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_groupIndex": 1, "groupIndex": 1}, {"__type__": "cc.Node", "_name": "trap99", "_parent": {"__id__": 2}, "_children": [{"__id__": 35}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 34}, "_type": 1, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 34}, "_friction": 0.25, "_size": {"__type__": "cc.Size", "width": 240, "height": 40}}, {"__type__": "cc.Animation", "node": {"__id__": 34}, "_defaultClip": {"__uuid__": "cbMGa4i+9Kxpaq88uEO9gv"}, "_clips": [{"__uuid__": "cbMGa4i+9Kxpaq88uEO9gv"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "b5gHHUjWlP/qxuZqWn/gTI"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [497, 140, 0, 0, 0, 0, 1, 1, 0.75, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 34}, "_children": [{"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 35}, "_layoutSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "52zCdTZhhMt5gZ9ktvh87G"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h57", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8bqREWFvpOuJw4h2zxDOnT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "09cZl91b1Ebbdu7Q8cm4ri"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h58", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c3EE1jyxHuK/3soFdjdpm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "0cfOdeEnxNr7YCnfzVAIke"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-60, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h58", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 38}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c3EE1jyxHuK/3soFdjdpm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "cdeyHBvoRC6pmmx9ai/IME"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-20, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h58", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 39}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c3EE1jyxHuK/3soFdjdpm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "0bTzMHGpBAOL9BCAWLKcC5"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h58", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c3EE1jyxHuK/3soFdjdpm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "0cv50UbVRCiogpMrUbdhkj"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [60, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h57", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8bqREWFvpOuJw4h2zxDOnT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "28vGKzLsJO+I3t6/gpuLiM"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 0, 0, 0, 0, 0, 1, -1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap99 copy", "_parent": {"__id__": 2}, "_children": [{"__id__": 43}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 42}, "_type": 1, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 42}, "_friction": 0.25, "_size": {"__type__": "cc.Size", "width": 240, "height": 40}}, {"__type__": "cc.Animation", "node": {"__id__": 42}, "_defaultClip": {"__uuid__": "cbMGa4i+9Kxpaq88uEO9gv"}, "_clips": [{"__uuid__": "cbMGa4i+9Kxpaq88uEO9gv"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "e6COMVPotJUJjHP0k57JEm"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-497, 140, 0, 0, 0, 0, 1, 1, 0.75, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 42}, "_children": [{"__id__": 44}, {"__id__": 45}, {"__id__": 46}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 43}, "_layoutSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "858i4/VvxB8YD9zXPXlqOu"}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h57", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 44}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8bqREWFvpOuJw4h2zxDOnT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "cfmPtONlFEY4P42oEMBCkJ"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h58", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c3EE1jyxHuK/3soFdjdpm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "efolE/1DRPsIBIS0mqIET8"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-60, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h58", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 46}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c3EE1jyxHuK/3soFdjdpm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "f0OzMLsXFEiKlRY/wON/eA"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-20, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h58", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c3EE1jyxHuK/3soFdjdpm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "fdvyjT6M5Dm48fn8W78Slr"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h58", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c3EE1jyxHuK/3soFdjdpm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "bfo72+Nu5GT6vc/1Ab5o65"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [60, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "h57", "_parent": {"__id__": 43}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8bqREWFvpOuJw4h2zxDOnT"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "25rb0lnkJB2qSi97jk9RCB"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [100, 0, 0, 0, 0, 0, 1, -1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groud01", "_parent": {"__id__": 2}, "_children": [{"__id__": 51}, {"__id__": 52}, {"__id__": 75}, {"__id__": 76}, {"__id__": 77}, {"__id__": 78}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 50}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 50}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 1536, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "018uzcbGdBJZGJb6bN6NfK"}, "_contentSize": {"__type__": "cc.Size", "width": 1536, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -190, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 51}, "_enabled": false, "_alignFlags": 45, "_left": 64, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "49PiyBzIBMs4XerYSZ3lcR"}, "_contentSize": {"__type__": "cc.Size", "width": 1408, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 50}, "_children": [{"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}, {"__id__": 64}, {"__id__": 65}, {"__id__": 66}, {"__id__": 67}, {"__id__": 68}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}, {"__id__": 74}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 52}, "_layoutSize": {"__type__": "cc.Size", "width": 1408, "height": 64}, "_N$layoutType": 1, "_N$affectedByScale": true}, {"__type__": "cc.Widget", "node": {"__id__": 52}, "_enabled": false, "_alignFlags": 40, "_left": 64, "_right": 64, "_originalWidth": 768}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "4582R40JNHebcR5GurtZj9"}, "_contentSize": {"__type__": "cc.Size", "width": 1408, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "29g3WPd95HB6gy3Go5ISFi"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-672, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "fcsObUSdlEJ4PQMB3qlFof"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-608, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 55}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "43lxeuJwJEiaRjAbx2vXKI"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "d2PAP6bYRLI5pPKAYwl3W9"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "abH9pVh2xC04IPPPB7N8XF"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 58}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "01DajpAClPIJToRVrO3P+d"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "6asBNL1RZA5rvygCK871fW"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 60}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "78kY+lHtxD+pVX4+cyQQ6f"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 61}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "83CZf3ulxG7r0Rz3c6Lo4U"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 62}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "ddNuvba1dOH7aEbZPGk8SI"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 63}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "98EfjZpydASJGIQ+m+WOT4"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 64}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "646HVOfzdPpoqzlbV69HEo"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 65}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "8fW/8JRwFOg750AjMiXecL"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 66}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "7cOGmc8hxAo7BlTFjupZ2r"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 67}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "aaaW/tWTBD0aVo2CzPXIvZ"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 68}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "5fBpG+qH9GaJkE9A/A0D46"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 69}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "c5Sb+Ke/hGSIH7NxZRlY/a"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 70}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "82bPKU2Y9FLaBTpTjf/deW"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [416, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 71}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "030o0nDP9Mg4ftSfMfGt8D"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 72}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "54XXODLNxHqquuBW0K1Q3h"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [544, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 73}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "71kxSOk6ZH+b0ffK8U8tGO"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [608, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 52}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 74}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "eenXlTTGdMkoB2XP7PDm8D"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [672, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 75}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 75}, "_enabled": false, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "3d5aBQ0YlONq6OTu2irVuv"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-736, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 76}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 76}, "_enabled": false, "_alignFlags": 13, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "2dM3Y+mPNFfIZeyOncF7X5"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-736, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 77}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 77}, "_enabled": false, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "e1m9taaUJPVa/aP56GjdUt"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [736, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 50}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 78}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 78}, "_enabled": false, "_alignFlags": 33}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "3eKTOjlghNg4vZkoIvVL7b"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [736, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 80}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 79}, "_allowSleep": false, "_gravityScale": 4.5, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 79}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 26, "height": 70}}, {"__type__": "bb3e1rlAgNFSI8IZkNdeApj", "node": {"__id__": 79}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "8dFZdjiwZGmqIlAq99b9uQ"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-524, 154.043, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 79}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 80}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "58KkXiSsFPn6Zprw9YQyRl"}, "fileId": "f5e61cK15AC7VskjnmDq3e"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}], {"__type__": "cc.AnimationClip", "_name": "map11050trap03Anim", "_duration": 5, "curveData": {"paths": {"1": {"props": {"x": [{"frame": 0, "value": -168}, {"frame": 5, "value": -2568}]}}, "2": {"props": {"x": [{"frame": 0, "value": -24}, {"frame": 5, "value": -2424}]}}, "3": {"props": {"x": [{"frame": 0, "value": 120}, {"frame": 5, "value": -2280}]}}, "4": {"props": {"x": [{"frame": 0, "value": 264}, {"frame": 5, "value": -2136}]}}, "5": {"props": {"x": [{"frame": 0, "value": 408}, {"frame": 5, "value": -1992}]}}}}}, {"__type__": "cc.AnimationClip", "_name": "map11050trap01Anim", "_duration": 3, "wrapMode": 2, "curveData": {"paths": {"1": {"props": {"y": [{"frame": 0, "value": 80}, {"frame": 1.5, "value": -226}, {"frame": 3, "value": 80}]}}, "2": {"props": {"y": [{"frame": 0, "value": -226}, {"frame": 1.5, "value": 80}, {"frame": 3, "value": -226}]}}, "3": {"props": {"y": [{"frame": 0, "value": 80}, {"frame": 1.5, "value": -226}, {"frame": 3, "value": 80}]}}, "4": {"props": {"y": [{"frame": 0, "value": -226}, {"frame": 1.5, "value": 80}, {"frame": 3, "value": -226}]}}, "5": {"props": {"y": [{"frame": 0, "value": 80}, {"frame": 1.5, "value": -226}, {"frame": 3, "value": 80}]}}}}}, {"__type__": "cc.AnimationClip", "_name": "map11050door2Anim", "_duration": 5, "curveData": {"props": {"position": [{"frame": 0, "value": [-504, 155, 0]}, {"frame": 0.3333333333333333, "value": [-804, 155, 0]}, {"frame": 0.6666666666666666, "value": [-804, -194, 0]}, {"frame": 1, "value": [-504, -194, 0]}, {"frame": 4, "value": [-504, -194, 0]}, {"frame": 5, "value": [504, -194, 0]}]}}}, {"__type__": "cc.AnimationClip", "_name": "map11050trap033Anim", "_duration": 3, "curveData": {"paths": {"1": {"props": {"x": [{"frame": 0, "value": -2568}, {"frame": 3, "value": -168}]}}, "2": {"props": {"x": [{"frame": 0, "value": -2424}, {"frame": 3, "value": -24}]}}, "3": {"props": {"x": [{"frame": 0, "value": -2280}, {"frame": 3, "value": 120}]}}, "4": {"props": {"x": [{"frame": 0, "value": -2136}, {"frame": 3, "value": 264}]}}, "5": {"props": {"x": [{"frame": 0, "value": -1992}, {"frame": 3, "value": 408}]}}}}}, {"__type__": "cc.AnimationClip", "_name": "map11050doorAnim", "_duration": 0.8333333333333334, "curveData": {"props": {"position": [{"frame": 0, "value": [504, 155, 0]}, {"frame": 0.3333333333333333, "value": [504, 400, 0]}, {"frame": 0.5, "value": [-504, 400, 0]}, {"frame": 0.8333333333333334, "value": [-504, 155, 0]}]}}}]