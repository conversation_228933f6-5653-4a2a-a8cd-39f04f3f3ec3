[[{"__type__": "cc.Prefab", "_name": "map11015", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "11015", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "08+asnXxBCarkjp0kDW9rB"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 768}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "node", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 12}, {"__id__": 15}, {"__id__": 16}, {"__id__": 20}, {"__id__": 21}, {"__id__": 25}, {"__id__": 57}, {"__id__": 58}, {"__id__": 60}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "8bitUQZ8tDe7XExKvVy19B"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "CC_nodeTips", "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "59ihWe5DxD/qDSuL5i0RL7"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [23.423, -27.327, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "tips", "_parent": {"__id__": 3}, "_children": [{"__id__": 5}, {"__id__": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "edv6kYPiVG6JApFUbHUXKg"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 4}, "_children": [{"__id__": 6}, {"__id__": 7}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 5}, "_enabled": false, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "f6ufbTpu9N8rD1tdBjLtd7"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-373.793, -52.75, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a20", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fd4iXgx4hNG7rVTsSGSEWm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "fbIG2gFMRHFp5cn27e6fwk"}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 5}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "向右跳跃，在空中向左移动\n触发地面移动后跳跃通过", "_N$string": "向右跳跃，在空中向左移动\n触发地面移动后跳跃通过", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "14MSshUNJHnbFTcFf3ctFc"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 480, "height": 90.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 72.979, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "1", "_parent": {"__id__": 4}, "_children": [{"__id__": 9}, {"__id__": 10}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 8}, "_enabled": false, "_N$layoutType": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "7a32eOTBJAbYjbvHTnkvjc"}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 200}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [14.421, -52.75, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "a20", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fd4iXgx4hNG7rVTsSGSEWm"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "e0tD2Ubj9PSJ1WeT4IbJF+"}, "_contentSize": {"__type__": "cc.Size", "width": 62, "height": 60}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 8}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 10}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "向右跳跃", "_N$string": "向右跳跃", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "1bBQPvt6RCroaI6vcknY2r"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 55.782, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "labelDesc", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "似曾相识的一关", "_N$string": "似曾相识的一关", "_N$horizontalAlign": 1, "_N$verticalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "1bX1Ge5cxGF7DYgadp/qHP"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 280, "height": 50.4}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1.25, 1.25, 1.25]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "door", "_parent": {"__id__": 2}, "_children": [{"__id__": 13}, {"__id__": 14}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 12}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 12}, "_sensor": true, "_offset": {"__type__": "cc.Vec2", "y": 50}, "_size": {"__type__": "cc.Size", "width": 80, "height": 100}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 12}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "4cIEq4dZVKs6OYU9fQrn8Q"}, "_color": {"__type__": "cc.Color", "r": 122, "g": 64, "b": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [445, -124.866, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "men", "_parent": {"__id__": 12}, "_components": [{"__type__": "sp.Skeleton", "node": {"__id__": 13}, "_materials": [{"__uuid__": "7a/QZLET9IDreTiBfRn2PD"}], "defaultSkin": "normal", "defaultAnimation": "idle", "_preCacheMode": 0, "premultipliedAlpha": false, "_animationName": "idle", "_N$skeletonData": {"__uuid__": "c0LM7snnNHQaMyY33lmA1E"}}, {"__type__": "cff62e/pOdD6rb9kQvEDU5p", "node": {"__id__": 13}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "ed9zuWCilNZa29NFvGzvrc"}, "_contentSize": {"__type__": "cc.Size", "width": 112, "height": 144.34}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "cat", "_parent": {"__id__": 12}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 14}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0dZDloyLJH/prtDFYiu1U6"}}, {"__type__": "a7c9e360ABHZahjQlyN4TCW", "node": {"__id__": 14}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "8dazftxM5HEKqcDcsnN2kO"}, "_contentSize": {"__type__": "cc.Size", "width": 106, "height": 73}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 118.66, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "tiggerActionNode01", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 15}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 15}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 10, "height": 300}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 15}, "tagetNode": {"__id__": 16}, "animClips": {"__uuid__": "c87bs+RmNEVaYVkY+tw+0s"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "e02F6GNZpN3LAGOUU9jSXY"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-272, 25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap01", "_parent": {"__id__": 2}, "_children": [{"__id__": 17}, {"__id__": 18}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 16}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 16}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 64, "height": 320}}, {"__type__": "cc.Animation", "node": {"__id__": 16}, "_clips": [{"__uuid__": "c87bs+RmNEVaYVkY+tw+0s"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "cfprXhnGNIio8VeuaQ2aZ4"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 16}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 17}, "_alignFlags": 45, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "98p4dQP09JNr4afdnvKU+L"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 16}, "_children": [{"__id__": 19}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 18}, "_layoutSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "f2dh4tgo1IsYACtZRjV99N"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "d36jqY8+RPI7k4Q1FeVPTT"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "tiggerActionNode02", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 20}, "_type": 0, "_fixedRotation": true, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 20}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 10, "height": 300}}, {"__type__": "638b2HXvnpBrbQq2lScDrbH", "node": {"__id__": 20}, "tagetNode": {"__id__": 21}, "animClips": {"__uuid__": "16lPprIkhOCofg1Sh8dCRG"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "e6rY1DkUVFhLXsgd5Qf1ta"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [112, 25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "trap02", "_parent": {"__id__": 2}, "_children": [{"__id__": 22}, {"__id__": 23}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 21}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 21}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 64, "height": 320}}, {"__type__": "cc.Animation", "node": {"__id__": 21}, "_clips": [{"__uuid__": "16lPprIkhOCofg1Sh8dCRG"}]}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "edSoZpcEVLdrEF6T/LXI2Z"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 21}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 22}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 22}, "_alignFlags": 45, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "d2uexJ8z1AhbWi741btT0c"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 21}, "_children": [{"__id__": 24}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 23}, "_layoutSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_N$layoutType": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "334io+0NFNUaM+qjZiGjda"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 23}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "1dsKY2hSRH85XeKmqhpPb4"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground", "_parent": {"__id__": 2}, "_children": [{"__id__": 26}, {"__id__": 38}, {"__id__": 44}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "6dGLBABsZHkJcIxqUBUkyg"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groud01", "_parent": {"__id__": 25}, "_children": [{"__id__": 27}, {"__id__": 28}, {"__id__": 36}, {"__id__": 37}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 26}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 26}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 448, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "a7ylgmeGNNKZZwBFsq0/tW"}, "_contentSize": {"__type__": "cc.Size", "width": 448, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-672, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 27}, "_alignFlags": 45, "_left": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "99IlpRnCdPh5bNKLs2yiIC"}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 26}, "_children": [{"__id__": 29}, {"__id__": 30}, {"__id__": 31}, {"__id__": 32}, {"__id__": 33}, {"__id__": 35}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 28}, "_layoutSize": {"__type__": "cc.Size", "width": 384, "height": 64}, "_N$layoutType": 1, "_N$affectedByScale": true}, {"__type__": "cc.Widget", "node": {"__id__": 28}, "_alignFlags": 40, "_left": 64, "_originalWidth": 704}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "afCgzz3yZNJZVpTm4jnpUX"}, "_contentSize": {"__type__": "cc.Size", "width": 384, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "7fBEp7GbtKsoVoqFk/Qz9d"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 30}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "f1LIUztktGAbYBvBsQP+1t"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "540SowDCNFu6unmvt5Lzoq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "7bS4M4C5xLZosPbdPpt5YI"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 28}, "_children": [{"__id__": 34}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "e1Kfxk7cFCl5w8XaGn+0NN"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little05", "_parent": {"__id__": 33}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdTFkPiBtD75Oaaev82MWR"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "18Lfy6pydIv4p2i7G4D2yk"}, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 26}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 28}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 35}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "5es0r/NJNOGJ0/Wje6F+m0"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground07", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "896KZnaDlIporXZukGY70e"}}, {"__type__": "cc.Widget", "node": {"__id__": 36}, "_alignFlags": 8}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "f8byJ1aItNXZLt73HWOk9f"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-192, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground08", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 37}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "19bCU7hF5EL6WhM7qMo+US"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 37}, "_alignFlags": 13, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "55B4dtghJIJprNWgO+bK28"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-192, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground", "_parent": {"__id__": 25}, "_children": [{"__id__": 39}, {"__id__": 43}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 38}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 38}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 192, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "f2gLrxGJJDyqnOlGrMm0uF"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-160, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Node", "_parent": {"__id__": 38}, "_children": [{"__id__": 40}, {"__id__": 41}, {"__id__": 42}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 39}, "_layoutSize": {"__type__": "cc.Size", "width": 192, "height": 64}, "_resize": 1, "_N$layoutType": 1}, {"__type__": "cc.Widget", "node": {"__id__": 39}, "_alignFlags": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "abpgZZPQ5Ifb+TlS+Wzqw3"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 39}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 40}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "12Rw93YfNKQqaiEPidfo9l"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-64, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 39}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 41}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "93UCoQBChLe7qWTHSvXCMb"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground04", "_parent": {"__id__": 39}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 42}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e9ZxFOgClMirEyegAV8a/j"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "9eJ5kk96FKzbMSU55BGjGq"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [64, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 38}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 43}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 43}, "_alignFlags": 45, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "a8XxNs4jVApJLqGKuQA74W"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "groud01", "_parent": {"__id__": 25}, "_children": [{"__id__": 45}, {"__id__": 46}, {"__id__": 55}, {"__id__": 56}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 44}, "_type": 0}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 44}, "_offset": {"__type__": "cc.Vec2", "y": -160}, "_size": {"__type__": "cc.Size", "width": 512, "height": 320}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "500oc4IKdI553L/hdcNJQP"}, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [384, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground00", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 45}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "d1SaX3Eu5FArcbFm1PYvee"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 45}, "_alignFlags": 45, "_right": 64, "_top": 64, "_originalWidth": 1152, "_originalHeight": 128}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "19jNhx4tlFg5+nBqrvJJe8"}, "_contentSize": {"__type__": "cc.Size", "width": 448, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -64, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Layout", "_parent": {"__id__": 44}, "_children": [{"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 51}, {"__id__": 52}, {"__id__": 53}, {"__id__": 54}], "_components": [{"__type__": "cc.Layout", "node": {"__id__": 46}, "_layoutSize": {"__type__": "cc.Size", "width": 448, "height": 64}, "_N$layoutType": 1}, {"__type__": "cc.Widget", "node": {"__id__": 46}, "_alignFlags": 40, "_right": 64, "_originalWidth": 384}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "9akfKy0WBF6qh03JWYXHMR"}, "_contentSize": {"__type__": "cc.Size", "width": 448, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 47}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "37dod61SxMm7Dt8NQlc/6p"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-192, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 48}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "bcphzXRVhIXqwC5hM6/Otf"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-128, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 46}, "_children": [{"__id__": 50}], "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 49}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "fa8Kpf+RNHb5N+BujwTNml"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-64, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "little03", "_parent": {"__id__": 49}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 50}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fc69HW58tABZYp1mCyhcon"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "20z9G2wlRGBav6ujdto7+7"}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 28}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 51}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "47kzW0JfxE6oIkdglB4Ho5"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground03", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 52}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "18nByYD91NPaIuEOdFILVP"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "4aCsip9XFLaqZTHTGiRj3N"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [64, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground01", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 53}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "00aVANOgtLqrMzyBJ/sCBY"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "24XMsNsGJFIoQbDro1HRlC"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [128, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground02", "_parent": {"__id__": 46}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 54}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "98sk82CvtGhbee8bRgFQZc"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "44qYxIrK1OM6ycZ7feIxDp"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [192, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground05", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 55}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "e0IS86pZJLiqTtMiav8kbV"}}, {"__type__": "cc.Widget", "node": {"__id__": 55}, "_alignFlags": 32}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "55SEdrMJ9DZ42zdxU3VHiH"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -32, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "ground06", "_parent": {"__id__": 44}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 56}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "5dkvU9mppDBrDS512I6n4V"}, "_type": 2, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 56}, "_alignFlags": 37, "_top": 64, "_originalHeight": 64}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "77aTkJwuRPyJU7QD9dUPHs"}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 256}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [224, -192, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 2, "groupIndex": 2}, {"__type__": "cc.Node", "_name": "New Sprite(Splash)", "_parent": {"__id__": 2}, "_active": false, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 57}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "15xa9TcN1Jh5N8HDEleUSE"}, "_opacity": 124.95, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeRole", "_parent": {"__id__": 2}, "_children": [{"__id__": 59}], "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 58}, "_allowSleep": false, "_gravityScale": 4.5, "_fixedRotation": true, "enabledContactListener": true, "bullet": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 58}, "_offset": {"__type__": "cc.Vec2", "y": 35}, "_size": {"__type__": "cc.Size", "width": 26, "height": 70}}, {"__type__": "bb3e1rlAgNFSI8IZkNdeApj", "node": {"__id__": 58}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "6eDLcvGvdOSY8XInxM501o"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-519.575, -118.745, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "spr", "_parent": {"__id__": 58}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 59}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "8c20Sso/ZEn7NUfNSM+EBh"}, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "5frcAfIzZEPLBryQh5qaC9"}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_groupIndex": 3, "groupIndex": 3}, {"__type__": "cc.Node", "_name": "Dead", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.RigidBody", "node": {"__id__": 60}, "_type": 0, "enabledContactListener": true}, {"__type__": "cc.PhysicsBoxCollider", "node": {"__id__": 60}, "_sensor": true, "_size": {"__type__": "cc.Size", "width": 1280, "height": 16}}, {"__type__": "2311bbLe6NH5pWmOFM/3P2e", "node": {"__id__": 60}, "msgId": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "0ea+haGHFGXo2qJinmmWAF"}, "fileId": "ceWkO7G4tIbLzU4+BeneeV"}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 20}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -430, 0, 0, 0, 0, 1, 1.25, 1.25, 1.25]}, "_groupIndex": 2, "groupIndex": 2}], {"__type__": "cc.AnimationClip", "_name": "map11015trap02Anim", "_duration": 0.2833333333333333, "curveData": {"props": {"x": [{"frame": 0, "value": 224}, {"frame": 0.2, "value": 96}, {"frame": 0.2833333333333333, "value": 224}]}}}]