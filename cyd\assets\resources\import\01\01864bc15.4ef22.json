[{"__type__": "cc.SpriteFrame", "content": {"name": "guide7", "texture": "61zvKO+OdAHZ09MkuDW+nH", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "art01", "texture": "f9+zYcpDhKyZLd2O6debag", "rect": [0, 0, 71, 30], "offset": [0, 0], "originalSize": [71, 30], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "guide2", "texture": "a7aptzEV1PZKfgTz7bUhhC", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "guide6", "texture": "edhKou8P9Aub6vA+T1oYHs", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "guide3", "texture": "e5AEN2qu5EYI8ALLZspY5O", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "guide5", "texture": "62K4qcAEhN35PdQSVApjNW", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "guide8", "texture": "c6br2p7dJLVa8kCkOL/xYb", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}, [{"__type__": "cc.Prefab", "_name": "vGameGridMoveGuideDialog", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "layout", "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 10}, {"__id__": 14}, {"__id__": 18}, {"__id__": 22}, {"__id__": 26}, {"__id__": 30}, {"__id__": 34}, {"__id__": 35}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "09wv03m3xBPqaZzdCqaTJj"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeNormal", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "8cm6yTau9DLa6EnJ+kQ+7R"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "07NWJPykJPzJR+XVNm7NLO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "0eMb3pyZFMeb5yamAYU8/F"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 4}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "记住激光的位置", "_N$string": "记住激光的位置", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "1fx1wf/vlMCKlobiBBwdtg"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 168, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-176, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label copy", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 5}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "避开陷阱到达圣诞树", "_N$string": "避开陷阱到达圣诞树", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "26niNkaNhMkLJs825fBHLW"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeDici", "_parent": {"__id__": 1}, "_children": [{"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "722TeJp6xGU6udJEXc9LZy"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "754f/TpFVOnJmtm2d2RH2O"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "3cRFQfliZLapfE+hKpM/Pr"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 8}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "观察地刺的频率", "_N$string": "观察地刺的频率", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "bfiBTfNDxJ2q0LvVYDjk39"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 168, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-176, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label copy", "_parent": {"__id__": 6}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 9}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "抓住机会跨越陷阱", "_N$string": "抓住机会跨越陷阱", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "f4cAFQmNhBXLoL9IY2gOSV"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLight", "_parent": {"__id__": 1}, "_children": [{"__id__": 11}, {"__id__": 12}, {"__id__": 13}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "c0crKS+/ZKQJeQwguacdHv"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 11}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "362mzc1X1JOLoISGTj4HoA"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "ebfOoOC/5DfJ41DrvYczDv"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 12}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "小心手电筒的光线", "_N$string": "小心手电筒的光线", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "34PEYtKFpG/ZUnkc89tkT4"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-176, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label copy", "_parent": {"__id__": 10}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 13}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "不要被敌人发现", "_N$string": "不要被敌人发现", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "b6gi449F1O9prva9KLHMGP"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 168, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeIce", "_parent": {"__id__": 1}, "_children": [{"__id__": 15}, {"__id__": 16}, {"__id__": 17}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "9f5vIMziVEK4i5IM8+ZSU/"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 15}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "10tYZ9wxlIPLpRbPOiIW/p"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "25Dfzk/UtNaruijofrqe+u"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 16}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "冰面湿滑", "_N$string": "冰面湿滑", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "831ksX4AVNvo7crPpHS8pG"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-176, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label copy", "_parent": {"__id__": 14}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 17}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "冰面上无法控制方向", "_N$string": "冰面上无法控制方向", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "2fRmSUgehIMI80plxZrfVK"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeRay", "_parent": {"__id__": 1}, "_children": [{"__id__": 19}, {"__id__": 20}, {"__id__": 21}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "aeO8sTamVHpb5HFhYPEwwe"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 19}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "f0wqZbSiNH0rptQjyIhCxp"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "e0ubco+3RMlIdN7mdwynDS"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 20}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "寻找特殊按钮", "_N$string": "寻找特殊按钮", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "6fXkvwJtlECJF2j7NO/cB7"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-176, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label copy", "_parent": {"__id__": 18}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 21}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "点亮激光的位置", "_N$string": "点亮激光的位置", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "f9gUyJpshKQLWmqDtyDFJE"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 168, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeTransfer", "_parent": {"__id__": 1}, "_children": [{"__id__": 23}, {"__id__": 24}, {"__id__": 25}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "0dz4GDVF5O75lwpMZvaRTH"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 23}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "fdk+6L5zpMh4c+K2E5q0KO"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "53N2woMypHGLaybWADXsgf"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 24}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "进入传送门", "_N$string": "进入传送门", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "c77NdI17JD8YCCX0V0G4Tq"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-176, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label copy", "_parent": {"__id__": 22}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 25}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "快速到达另一个位置", "_N$string": "快速到达另一个位置", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "10+zZ7+mxFLY27QNobpDTf"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeSpring", "_parent": {"__id__": 1}, "_children": [{"__id__": 27}, {"__id__": 28}, {"__id__": 29}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "b9V3QwoPdDMqmfCLsOL38P"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 27}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "4fnOdHlJdKy5Pb2DUbYusz"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "17K/TK/pFMW5x6IhRygyuo"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 28}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "向弹簧移动", "_N$string": "向弹簧移动", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "1bAIgpOuFN4Y/qunHTufdq"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-176, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label copy", "_parent": {"__id__": 26}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 29}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "飞向更远的地方", "_N$string": "飞向更远的地方", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "1bMHUVjf5ECJYorm/ixWiD"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 168, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeConveyor", "_parent": {"__id__": 1}, "_children": [{"__id__": 31}, {"__id__": 32}, {"__id__": 33}], "_active": false, "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "c1NiZzmRBPpIqTKNtlEPz2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg01", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 31}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "49myfMe7xN1KTQDvoN39dl"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "9bxRCPa/xKFL7dvcsXQAbq"}, "_contentSize": {"__type__": "cc.Size", "width": 862, "height": 480}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 32}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "注意传送带的方向", "_N$string": "注意传送带的方向", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "55H2KyNrpD5Zsmg1H3bGt9"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 192, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-176, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "label copy", "_parent": {"__id__": 30}, "_components": [{"__type__": "cc.Label", "node": {"__id__": 33}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_useOriginalSize": false, "_string": "去到意想不到的地方", "_N$string": "去到意想不到的地方", "_fontSize": 24, "_lineHeight": 26, "_styleFlags": 1, "_N$horizontalAlign": 1}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "bd0c1OyNZG1YPX6Fzd4bnC"}, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 32.76}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [185, -142, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "art01", "_parent": {"__id__": 1}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 34}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "0ebfk/VI1A95zemxxzcu0K"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "77rZvUGNlBmZY5FxYB7LZL"}, "_contentSize": {"__type__": "cc.Size", "width": 71, "height": 30}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 182, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_buttonClose", "_parent": {"__id__": 1}, "_children": [{"__id__": 36}], "_components": [{"__type__": "d73bdn/VphF27UW9pqVYirD", "node": {"__id__": 35}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "4a9eW06N5GILbykyXeoMxG"}, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 64}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -184, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "e09", "_parent": {"__id__": 35}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 36}, "_materials": [{"__uuid__": "ecpdLyjvZBwrvm+cedCcQy"}], "_spriteFrame": {"__uuid__": "3cZOflBIRNx6vca4Kr5dry"}}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "e9pC7L++NOZ5JJ0uIfLoms"}, "fileId": "a3Apdm74NJEYtItIsWrYoz"}, "_contentSize": {"__type__": "cc.Size", "width": 211, "height": 86}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "guide1", "texture": "e4K1nox29FVJ4Hp8RMEMg8", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "guide4", "texture": "d7uHcBlohPYYCFOLipKn56", "rect": [0, 0, 862, 480], "offset": [0, 0], "originalSize": [862, 480], "capInsets": [0, 0, 0, 0]}}]