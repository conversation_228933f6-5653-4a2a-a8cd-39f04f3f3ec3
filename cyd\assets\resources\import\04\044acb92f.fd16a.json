[{"__type__": "cc.Material", "_name": "sprite-flash-light", "_effectAsset": {"__uuid__": "e4uSr8sRxAZ6G5E5cWivcz"}, "_techniqueData": {"0": {"props": {"lightColor": {"__type__": "cc.Color", "r": 255, "g": 245}, "lightAngle": 36, "lightWidth": 0.2, "lightCenterPoint": {"__type__": "cc.Vec2"}}, "defines": {"USE_TEXTURE": true, "ENABLE_LIGHT": true, "USE_ALPHA_TEST": false}}}}, {"__type__": "cc.EffectAsset", "_name": "sprite-flash-light", "techniques": [{"passes": [{"blendState": {"targets": [{"blend": true}]}, "rasterizerState": {"cullMode": 0}, "properties": {"texture": {"value": "white", "type": 29}, "alphaThreshold": {"value": [0.5], "type": 13}, "lightColor": {"value": [1, 1, 0, 1], "editor": {"type": "color", "tooltip": "光束颜色"}, "type": 16}, "lightCenterPoint": {"value": [0.2, 0.2], "editor": {"tooltip": "光束中心点坐标"}, "type": 14}, "lightAngle": {"value": [36], "editor": {"tooltip": "光束倾斜角度", "range": [0, 1]}, "type": 13}, "lightWidth": {"value": [0.2], "editor": {"tooltip": "光束宽度"}, "type": 13}, "enableGradientFlag": {"value": [1], "editor": {"tooltip": "是否开启光束渐变。1：开启，非1：不开启"}, "type": 13}, "enableCropAlphaFlag": {"value": [1], "editor": {"tooltip": "是否开启裁剪透明区域上的光。1：开启，非1：不开启"}, "type": 13}, "enableFogFlag": {"value": [0], "editor": {"tooltip": "是否开启迷雾效果。1：开启，非1：不开启"}, "type": 13}}, "program": "sprite-flash-light|vs|fs"}]}], "shaders": [{"hash": 3233633285, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_color;\n#if USE_TEXTURE\nin vec2 v_uv0;\nuniform sampler2D texture;\n#endif\n#if ENABLE_LIGHT\nuniform Light {\n  vec4 lightColor;\n  vec2 lightCenterPoint;\n  float lightAngle;\n  float lightWidth;\n  float enableGradientFlag;\n  float enableCropAlphaFlag;\n  float enableFogFlag;\n};\nvec4 addLightColor(vec4 textureColor, vec4 lightColor, vec2 lightCenterPoint, float lightAngle, float lightWidth) {\n  if (lightWidth <= 0.0) {\n    return textureColor;\n  }\n  float angleInRadians = radians(lightAngle);\n  float dis = 0.0;\n  if (mod(lightAngle, 180.0) != 0.0) {\n    float lightOffsetX = lightCenterPoint.x - ((1.0 - lightCenterPoint.y) / tan(angleInRadians));\n    float dx = lightOffsetX + (1.0 - v_uv0.y) / tan(angleInRadians);\n    float offsetDis = abs(v_uv0.x - dx);\n    dis = sin(angleInRadians) * offsetDis;\n  } else {\n    dis = abs(v_uv0.y - lightCenterPoint.y);\n  }\n  float a = 1.0 ;\n  if (enableCropAlphaFlag==1.0) {\n    a *= step(0.01, textureColor.a);\n  }\n  if (enableFogFlag!=1.0) {\n    a *= step(dis, lightWidth * 0.5);\n  }\n  if (enableGradientFlag == 1.0) {\n    a *= 1.0 - dis / (lightWidth * 0.5);\n  }\n  vec4 finalLightColor = lightColor * a;\n  return textureColor * textureColor.a + finalLightColor;\n}\n#endif\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  o *= texture(texture, v_uv0);\n    #if CC_USE_ALPHA_ATLAS_TEXTURE\n    o.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n    #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  gl_FragColor = o;\n  #if ENABLE_LIGHT\n  gl_FragColor = addLightColor(gl_FragColor, lightColor, lightCenterPoint, lightAngle, lightWidth);\n  #endif\n}"}, "glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_color;\n#if USE_TEXTURE\nvarying vec2 v_uv0;\nuniform sampler2D texture;\n#endif\n#if ENABLE_LIGHT\nuniform vec4 lightColor;\nuniform vec2 lightCenterPoint;\nuniform float lightAngle;\nuniform float lightWidth;\nuniform float enableGradientFlag;\nuniform float enableCropAlphaFlag;\nuniform float enableFogFlag;\nvec4 addLightColor(vec4 textureColor, vec4 lightColor, vec2 lightCenterPoint, float lightAngle, float lightWidth) {\n  if (lightWidth <= 0.0) {\n    return textureColor;\n  }\n  float angleInRadians = radians(lightAngle);\n  float dis = 0.0;\n  if (mod(lightAngle, 180.0) != 0.0) {\n    float lightOffsetX = lightCenterPoint.x - ((1.0 - lightCenterPoint.y) / tan(angleInRadians));\n    float dx = lightOffsetX + (1.0 - v_uv0.y) / tan(angleInRadians);\n    float offsetDis = abs(v_uv0.x - dx);\n    dis = sin(angleInRadians) * offsetDis;\n  } else {\n    dis = abs(v_uv0.y - lightCenterPoint.y);\n  }\n  float a = 1.0 ;\n  if (enableCropAlphaFlag==1.0) {\n    a *= step(0.01, textureColor.a);\n  }\n  if (enableFogFlag!=1.0) {\n    a *= step(dis, lightWidth * 0.5);\n  }\n  if (enableGradientFlag == 1.0) {\n    a *= 1.0 - dis / (lightWidth * 0.5);\n  }\n  vec4 finalLightColor = lightColor * a;\n  return textureColor * textureColor.a + finalLightColor;\n}\n#endif\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  o *= texture2D(texture, v_uv0);\n    #if CC_USE_ALPHA_ATLAS_TEXTURE\n    o.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n    #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  gl_FragColor = o;\n  #if ENABLE_LIGHT\n  gl_FragColor = addLightColor(gl_FragColor, lightColor, lightCenterPoint, lightAngle, lightWidth);\n  #endif\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}], "samplers": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplers": []}}, "defines": [{"name": "USE_TEXTURE", "type": "boolean", "defines": []}, {"name": "CC_USE_MODEL", "type": "boolean", "defines": []}, {"name": "USE_ALPHA_TEST", "type": "boolean", "defines": []}, {"name": "ENABLE_LIGHT", "type": "boolean", "defines": []}, {"name": "CC_USE_ALPHA_ATLAS_TEXTURE", "type": "boolean", "defines": ["USE_TEXTURE"]}], "blocks": [{"name": "ALPHA_TEST", "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"], "binding": 0}, {"name": "Light", "members": [{"name": "lightColor", "type": 16, "count": 1}, {"name": "lightCenterPoint", "type": 14, "count": 1}, {"name": "lightAngle", "type": 13, "count": 1}, {"name": "lightWidth", "type": 13, "count": 1}, {"name": "enableGradientFlag", "type": 13, "count": 1}, {"name": "enableCropAlphaFlag", "type": 13, "count": 1}, {"name": "enableFogFlag", "type": 13, "count": 1}], "defines": ["ENABLE_LIGHT"], "binding": 1}], "samplers": [{"name": "texture", "type": 29, "count": 1, "defines": ["USE_TEXTURE"], "binding": 30}], "record": null, "name": "sprite-flash-light|vs|fs"}]}]