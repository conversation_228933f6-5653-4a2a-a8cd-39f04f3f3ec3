[{"__type__": "cc.AnimationClip", "_name": "mapline053trap06startAnim", "curveData": {"paths": {"New Node1": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node2": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node3": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node4": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node5": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node6": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node7": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node8": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node9": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node10": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node11": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node12": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node13": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node14": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node15": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node16": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node17": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node18": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node19": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}, "New Node20": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": -50}]}}}}}}, {"__type__": "cc.AnimationClip", "_name": "mapline053trap06stoptAnim", "curveData": {"paths": {"New Node1": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node2": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node3": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node4": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node5": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node6": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node7": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node8": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node9": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node10": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node11": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node12": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node13": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node14": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node15": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node16": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node17": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node18": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node19": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}, "New Node20": {"comps": {"cc.RigidBody": {"angularVelocity": [{"frame": 0, "value": 0}]}}}}}}]