[{"__type__": "cc.SpriteFrame", "content": {"name": "a48", "texture": "4998xTgUxIbKPYOIVuVmmr", "rect": [0, 0, 69, 69], "offset": [0, 0], "originalSize": [69, 69], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a26", "texture": "1cX7KBuLBPoZAknY2ozjq3", "rect": [0, 0, 67, 69], "offset": [0, 0], "originalSize": [67, 69], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a40", "texture": "5aEPkI/eRC87V+k6ZPUI3J", "rect": [0, 0, 71, 70], "offset": [0, 0], "originalSize": [71, 70], "capInsets": [0, 0, 0, 0]}}, {"__type__": "cc.SpriteFrame", "content": {"name": "a51", "texture": "4aW1GztG5PConECzl5izB5", "rect": [0, 0, 38, 58], "offset": [0, 0], "originalSize": [38, 58], "capInsets": [0, 0, 0, 0]}}]