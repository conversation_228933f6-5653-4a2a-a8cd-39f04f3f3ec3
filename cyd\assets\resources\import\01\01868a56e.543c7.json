[[{"__type__": "cc.Prefab", "_name": "vGuideMask", "data": {"__id__": 1}}, {"__type__": "cc.Node", "_name": "node", "_children": [{"__id__": 2}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b6+BMJqyFGQZ94EXO9xgjU"}, "fileId": "407l3A9+tOYYHhEGVKx4+i"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_nodeLeftBottom", "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 7}], "_components": [{"__type__": "cc.Widget", "node": {"__id__": 2}, "alignMode": 0, "_alignFlags": 45}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b6+BMJqyFGQZ94EXO9xgjU"}, "fileId": "1eZqo21ohHao81gzcM3YHQ"}, "_contentSize": {"__type__": "cc.Size", "width": 1024, "height": 576}, "_anchorPoint": {"__type__": "cc.Vec2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-512, -288, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteBlockLeft", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 3}, "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 3}, "alignMode": 0, "_alignFlags": 45, "_right": 562, "_originalWidth": 100, "_originalHeight": 100}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b6+BMJqyFGQZ94EXO9xgjU"}, "fileId": "f4/w/tB2RIQ7NsA0S1dN3G"}, "_opacity": 191, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 462, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [231, 288, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteBlockRight", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 4}, "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 4}, "alignMode": 0, "_alignFlags": 45, "_left": 562, "_originalWidth": 100, "_originalHeight": 100}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b6+BMJqyFGQZ94EXO9xgjU"}, "fileId": "c5EDAmR7FKcJqdiGsZJjI2"}, "_opacity": 191, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 462, "height": 576}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [793, 288, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteBlockTop", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 5}, "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 5}, "alignMode": 0, "_alignFlags": 45, "_left": 462, "_right": 462, "_bottom": 338, "_originalWidth": 100, "_originalHeight": 100}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b6+BMJqyFGQZ94EXO9xgjU"}, "fileId": "41HfALrD9E5abrf5JcUeH+"}, "_opacity": 191, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 238}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [512, 457, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteBlockBottom", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 6}, "_spriteFrame": {"__uuid__": "a2MjXRFdtLlYQ5ouAFv/+R"}, "_sizeMode": 0}, {"__type__": "cc.Widget", "node": {"__id__": 6}, "alignMode": 0, "_alignFlags": 45, "_left": 462, "_right": 462, "_top": 338, "_originalWidth": 100, "_originalHeight": 100}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b6+BMJqyFGQZ94EXO9xgjU"}, "fileId": "29X20WjVRIm6/LgF/DANcj"}, "_opacity": 191, "_color": {"__type__": "cc.Color"}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 238}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [512, 119, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "CC_spriteHole", "_parent": {"__id__": 2}, "_components": [{"__type__": "cc.Sprite", "node": {"__id__": 7}, "_spriteFrame": {"__uuid__": "baPMZwE71EWJtZ18P18utY"}, "_type": 1, "_sizeMode": 0}], "_prefab": {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b6+BMJqyFGQZ94EXO9xgjU"}, "fileId": "2dxnL7Ap1HWotlARiS87GV"}, "_opacity": 191, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2"}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [462, 238, 0, 0, 0, 0, 1, 1, 1, 1]}}], {"__type__": "cc.SpriteFrame", "content": {"name": "mask_02", "texture": "46L0kdJjVD870v7IaBRfSs", "rect": [0, 0, 100, 100], "offset": [0, 0], "originalSize": [100, 100], "capInsets": [50, 50, 50, 50]}}]