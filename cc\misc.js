// module.exports = function(require, module, exports) {
var js = require("./platform");
var misc = {};
misc.propertyDefine = function(ctor, sameNameGetSets, diffNameGetSets) {
    function define(np, propName, getter, setter) {
        var pd = Object.getOwnPropertyDescriptor(np, propName);
        if (pd) {
            pd.get && (np[getter] = pd.get);
            pd.set && setter && (np[setter] = pd.set);
        } else {
            var getterFunc = np[getter];
            var clsName;
            false;
            js.getset(np, propName, getterFunc, np[setter]);
        }
    }
    var propName, np = ctor.prototype;
    for (var i = 0; i < sameNameGetSets.length; i++) {
        propName = sameNameGetSets[i];
        var suffix = propName[0].toUpperCase() + propName.slice(1);
        define(np, propName, "get" + suffix, "set" + suffix);
    }
    for (propName in diffNameGetSets) {
        var getset = diffNameGetSets[propName];
        define(np, propName, getset[0], getset[1]);
    }
};
misc.NextPOT = function(x) {
    x -= 1;
    x |= x >> 1;
    x |= x >> 2;
    x |= x >> 4;
    x |= x >> 8;
    x |= x >> 16;
    return x + 1;
};
false;
misc.BUILTIN_CLASSID_RE = /^(?:cc|dragonBones|sp|ccsg)\..+/;
var BASE64_KEYS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
var BASE64_VALUES = new Array(123);

for (var i = 0; i < 123; ++i) BASE64_VALUES[i] = 64;
for (var _i = 0; _i < 64; ++_i) BASE64_VALUES[BASE64_KEYS.charCodeAt(_i)] = _i;
misc.BASE64_VALUES = BASE64_VALUES;
misc.pushToMap = function(map, key, value, pushFront) {
    var exists = map[key];
    if (exists)
        if (Array.isArray(exists))
            if (pushFront) {
                exists.push(exists[0]);
                exists[0] = value;
            } else exists.push(value);
    else map[key] = pushFront ? [value, exists] : [exists, value];
    else map[key] = value;
};
misc.clampf = function(value, min_inclusive, max_inclusive) {
    if (min_inclusive > max_inclusive) {
        var temp = min_inclusive;
        min_inclusive = max_inclusive;
        max_inclusive = temp;
    }
    return value < min_inclusive ? min_inclusive : value < max_inclusive ? value : max_inclusive;
};
misc.clamp01 = function(value) {
    return value < 0 ? 0 : value < 1 ? value : 1;
};
misc.lerp = function(a, b, r) {
    return a + (b - a) * r;
};
misc.degreesToRadians = function(angle) {
    return angle * cc.macro.RAD;
};
misc.radiansToDegrees = function(angle) {
    return angle * cc.macro.DEG;
};
module.exports = misc;
// }